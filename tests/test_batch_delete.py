#!/usr/bin/env python3
"""
测试文件管理器的批量删除功能
"""

import os
import sys
import json
import tempfile
import shutil
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))

from project_manager import ProjectManager

class TestBatchDelete(unittest.TestCase):
    """测试批量删除功能"""

    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        self.project_manager = ProjectManager()
        self.project_name = "test_batch_delete_project"

        # 创建测试项目
        self.project_id = self.project_manager.create_project(self.project_name, self.test_dir)

        # 创建一些测试文件和目录
        self.setup_test_files()

    def tearDown(self):
        """清理测试环境"""
        # 删除测试项目
        self.project_manager.delete_project(self.project_id)

        # 删除临时目录
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def setup_test_files(self):
        """创建测试文件"""
        file_manager = self.project_manager.get_file_manager(self.project_id)

        # 创建测试文件
        test_files = [
            'test1.txt',
            'test2.py',
            'docs/readme.md',
            'docs/config.json',
            'src/main.py',
            'src/utils.py',
            'temp.tmp'
        ]

        for file_path in test_files:
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path:
                full_dir_path = os.path.join(file_manager.project_root, dir_path)
                os.makedirs(full_dir_path, exist_ok=True)

            # 创建文件
            full_file_path = os.path.join(file_manager.project_root, file_path)
            with open(full_file_path, 'w', encoding='utf-8') as f:
                f.write(f"Content of {file_path}")

        # 创建一个空目录
        os.makedirs(os.path.join(file_manager.project_root, 'empty_dir'), exist_ok=True)

    def test_file_manager_batch_delete(self):
        """测试文件管理器的批量删除功能"""
        file_manager = self.project_manager.get_file_manager(self.project_id)

        # 测试删除多个文件
        paths_to_delete = ['test1.txt', 'test2.py', 'temp.tmp']
        success_count = 0
        failed_count = 0

        for path in paths_to_delete:
            result = file_manager.delete_file(path)
            if result.get('success', False):
                success_count += 1
            else:
                failed_count += 1

        # 检查删除结果
        self.assertEqual(success_count, 3)
        self.assertEqual(failed_count, 0)

        # 验证文件确实被删除
        self.assertFalse(os.path.exists(os.path.join(file_manager.project_root, 'test1.txt')))
        self.assertFalse(os.path.exists(os.path.join(file_manager.project_root, 'test2.py')))
        self.assertFalse(os.path.exists(os.path.join(file_manager.project_root, 'temp.tmp')))

        # 检查其他文件仍然存在
        self.assertTrue(os.path.exists(os.path.join(file_manager.project_root, 'docs/readme.md')))
        self.assertTrue(os.path.exists(os.path.join(file_manager.project_root, 'src/main.py')))

    def test_delete_nonexistent_file(self):
        """测试删除不存在的文件"""
        file_manager = self.project_manager.get_file_manager(self.project_id)

        result = file_manager.delete_file('nonexistent_file.txt')
        self.assertFalse(result.get('success', True))  # 应该失败

    def test_delete_directory(self):
        """测试删除目录"""
        file_manager = self.project_manager.get_file_manager(self.project_id)

        # 删除空目录
        result = file_manager.delete_file('empty_dir')
        self.assertTrue(result.get('success', False))

        # 验证目录被删除
        self.assertFalse(os.path.exists(os.path.join(file_manager.project_root, 'empty_dir')))

        # 删除非空目录
        result = file_manager.delete_file('docs')
        self.assertTrue(result.get('success', False))

        # 验证目录被删除
        self.assertFalse(os.path.exists(os.path.join(file_manager.project_root, 'docs')))

    def test_frontend_javascript_integration(self):
        """测试前端JavaScript集成"""
        # 这个测试主要验证前端代码的语法正确性
        js_file_path = project_root / 'static' / 'js' / 'file-manager.js'

        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查关键的批量删除函数是否存在
        self.assertIn('performBatchDelete', js_content)
        self.assertIn('/files/batch_delete', js_content)
        self.assertIn('paths:', js_content)

        # 检查统一的删除逻辑
        self.assertIn('智能判断单文件或批量删除', js_content)
        self.assertIn('selectedFiles.size > 1', js_content)

        # 检查HTML中的删除菜单项（现在只有一个统一的删除选项）
        html_file_path = project_root / 'static' / 'file_manager.html'
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        self.assertIn('deleteFile()', html_content)
        self.assertIn('删除', html_content)
        # 确保批量删除菜单项已经被移除
        self.assertNotIn('batch-delete-item', html_content)

    def test_file_selection_functionality(self):
        """测试文件选择功能"""
        # 检查JavaScript中的文件选择相关代码
        js_file_path = project_root / 'static' / 'js' / 'file-manager.js'
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查多选相关功能
        self.assertIn('selectedFiles', js_content)
        self.assertIn('ctrlKey', js_content)
        self.assertIn('metaKey', js_content)

        # 检查统一的删除逻辑
        self.assertIn('performBatchDelete', js_content)
        self.assertIn('selectedFiles.size > 1', js_content)

    def test_backend_api_endpoint(self):
        """测试后端API端点是否正确定义"""
        # 检查后端文件中是否包含批量删除API
        ui_file_path = project_root / 'src' / 'file_manager_ui.py'
        with open(ui_file_path, 'r', encoding='utf-8') as f:
            ui_content = f.read()

        # 检查API端点定义
        self.assertIn('api_batch_delete_files', ui_content)
        self.assertIn('/files/batch_delete', ui_content)
        self.assertIn('methods=["DELETE"]', ui_content)

        # 检查关键逻辑
        self.assertIn('paths = data.get(\'paths\', [])', ui_content)
        self.assertIn('success_count', ui_content)
        self.assertIn('failed_count', ui_content)


def main():
    """运行测试"""
    print("开始测试文件管理器批量删除功能...")

    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBatchDelete)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！")
        print("🎉 文件管理器批量删除功能优化完成")
        print("\n功能说明:")
        print("- ✅ Ctrl+点击可以选择多个文件")
        print("- ✅ 统一的'删除'按钮，智能识别单文件或批量删除")
        print("- ✅ 批量删除支持同时删除文件和文件夹")
        print("- ✅ 删除完成后自动刷新文件树")
        print("- ✅ 如果文件已打开会自动关闭对应标签页")
        print("- ✅ 提供详细的删除结果反馈")
        return 0
    else:
        print(f"\n❌ 测试失败：{len(result.failures)} 个失败，{len(result.errors)} 个错误")
        return 1


if __name__ == '__main__':
    sys.exit(main())