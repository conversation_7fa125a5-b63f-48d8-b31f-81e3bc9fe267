#!/usr/bin/env python3
"""
测试文件管理器的范围选择功能
"""

import os
import sys
import tempfile
import unittest
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))

class TestRangeSelection(unittest.TestCase):
    """测试范围选择功能"""

    def setUp(self):
        """设置测试环境"""
        self.js_file_path = project_root / 'static' / 'js' / 'file-manager.js'
        self.html_file_path = project_root / 'static' / 'file_manager.html'

    def test_range_selection_functions_exist(self):
        """测试范围选择相关函数是否存在"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        required_functions = [
            'getAllVisibleFileItems',
            'selectRangeBetween',
            'clearSelection'
        ]

        for func_name in required_functions:
            self.assertIn(func_name, js_content, f"缺少函数: {func_name}")

        print("✅ 所有必需的函数都存在")

    def test_range_selection_variables_exist(self):
        """测试范围选择相关变量是否存在"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        required_variables = [
            'lastSelectedFile',
            'isRangeSelecting'
        ]

        for var_name in required_variables:
            self.assertIn(var_name, js_content, f"缺少变量: {var_name}")

        print("✅ 所有必需的变量都存在")

    def test_shift_key_detection(self):
        """测试Shift键检测逻辑"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查Shift键检测
        self.assertIn('isShiftKey = event && event.shiftKey', js_content)

        # 检查Shift+点击的处理逻辑
        self.assertIn('if (isShiftKey && this.lastSelectedFile', js_content)

        print("✅ Shift键检测逻辑正确")

    def test_range_selection_logic(self):
        """测试范围选择核心逻辑"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查范围选择的核心逻辑
        logic_checks = [
            'startIndex = -1',
            'endIndex = -1',
            'minIndex = Math.min(startIndex, endIndex)',
            'maxIndex = Math.max(startIndex, endIndex)',
            'for (let i = minIndex; i <= maxIndex; i++)',
            'item.element.addClass(\'selected\')'
        ]

        for check in logic_checks:
            self.assertIn(check, js_content, f"缺少逻辑: {check}")

        print("✅ 范围选择核心逻辑正确")

    def test_event_prevention(self):
        """测试事件阻止"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查是否正确阻止默认行为
        self.assertIn('event.preventDefault()', js_content)
        self.assertIn('event.stopPropagation()', js_content)

        print("✅ 事件处理正确")

    def test_state_management(self):
        """测试状态管理"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查状态更新
        state_updates = [
            'this.lastSelectedFile = endPath',
            'this.isRangeSelecting = true',
            'this.isRangeSelecting = false'
        ]

        for update in state_updates:
            self.assertIn(update, js_content, f"缺少状态更新: {update}")

        print("✅ 状态管理正确")

    def test_boundary_handling(self):
        """测试边界情况处理"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查边界情况处理
        boundary_checks = [
            'startIndex !== -1 && endIndex !== -1',
            'return false',
            'this.lastSelectedFile && this.lastSelectedFile !== path'
        ]

        for check in boundary_checks:
            self.assertIn(check, js_content, f"缺少边界处理: {check}")

        print("✅ 边界情况处理正确")

    def test_html_event_binding(self):
        """测试HTML事件绑定"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查事件绑定
        self.assertIn('onclick="FileManager.selectFile(', js_content)
        self.assertIn('event)', js_content)

        print("✅ HTML事件绑定正确")

    def test_user_experience_optimizations(self):
        """测试用户体验优化"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查用户体验优化
        ux_checks = [
            '!isDirectory && !isCtrlKey && !isShiftKey',  # 范围选择时不打开文件
            'this.selectedFiles.clear()',                   # 清除选择
            'this.selectedFiles.add(path)'                 # 添加到选择集合
        ]

        for check in ux_checks:
            self.assertIn(check, js_content, f"缺少用户体验优化: {check}")

        print("✅ 用户体验优化正确")

    def test_integration_with_existing_features(self):
        """测试与现有功能的集成"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查与批量删除功能的集成
        batch_delete_checks = [
            'this.selectedFiles.size > 1',  # 检查多选
            'performBatchDelete',            # 批量删除
            'selectedFiles.clear'           # 清除选择
        ]

        for check in batch_delete_checks:
            self.assertIn(check, js_content, f"缺少批量删除集成: {check}")

        print("✅ 与现有功能集成正确")

    def test_documentation_comments(self):
        """测试文档注释"""
        with open(self.js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # 检查关键函数是否有注释
        function_comments = [
            '/**\n     * 获取所有可见的文件树项，按顺序排列',
            '/**\n     * 在两个文件路径之间选择所有文件',
            '/**\n     * 清除所有选择',
            '/**\n     * 选择文件'
        ]

        for comment in function_comments:
            self.assertIn(comment, js_content, f"缺少文档注释: {comment}")

        print("✅ 文档注释完整")


def main():
    """运行测试"""
    print("开始测试文件管理器范围选择功能...")
    print("=" * 60)

    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestRangeSelection)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！")
        print("🎉 文件管理器范围选择功能实现完成")
        print("\n功能特点:")
        print("- ✅ Shift+点击范围选择：选中两个文件之间的所有文件")
        print("- ✅ Ctrl+点击多选：逐个选择多个文件")
        print("- ✅ 智能边界处理：优雅处理各种边界情况")
        print("- ✅ 状态管理：正确追踪选择状态和最后选择文件")
        print("- ✅ 用户体验：范围选择时不自动打开文件")
        print("- ✅ 集成兼容：与现有批量删除功能完美集成")
        print("- ✅ 事件处理：正确阻止默认行为和事件冒泡")
        print("- ✅ 代码质量：完整的文档注释和错误处理")

        print("\n使用方法:")
        print("1. 单击选择：直接点击文件选择单个文件")
        print("2. Ctrl+点击：按住Ctrl键点击多个文件进行多选")
        print("3. Shift+范围：点击第一个文件，然后按住Shift点击第二个文件")
        print("4. 复杂选择：可以组合使用Ctrl和Shift进行复杂选择")
        print("5. 批量操作：选中多个文件后可以右键批量删除")

        return 0
    else:
        print(f"\n❌ 测试失败：{len(result.failures)} 个失败，{len(result.errors)} 个错误")
        return 1


if __name__ == '__main__':
    sys.exit(main())