"""
工具模块
包含通用功能函数
"""

import os
import logging
import subprocess
import shutil
from typing import Optional, List
from urllib.parse import quote
from datetime import datetime
try:
    from log_manager import LLMLogManager
except ImportError:
    from log_manager import LLMLogManager


def load_template_by_type(project, template_name: str, prompts_dir: str = "prompts") -> str:
    """
    根据项目类型加载模板文件

    Args:
        project_type: 项目类型（如 PMO、default、Web 等）
        template_name: 模板文件名（如 requirement.md, design.md, gen_task.md）
        prompts_dir: prompts目录路径

    Returns:
        str: 模板内容，如果文件不存在则返回空字符串
    """
    project_type = project.project_type
    if not project_type:
        project_type = 'default'

    # 构建模板文件路径，优先从项目类型目录加载
    project_type_dir = os.path.join(prompts_dir, project_type)
    default_dir = os.path.join(prompts_dir, "default")

    # 尝试从项目类型目录加载
    template_path = os.path.join(project_type_dir, template_name)
    if os.path.exists(template_path):
        try:
            with open(template_path, "r", encoding="utf-8") as f:
                logging.info(f"已从 {template_path} 加载 {template_name} 模板")
                return f.read()
        except Exception as e:
            logging.warning(f"加载模板文件 {template_path} 失败: {e}")

    # 如果项目类型目录不存在，尝试从默认目录加载
    default_template_path = os.path.join(default_dir, template_name)
    if os.path.exists(default_template_path):
        try:
            with open(default_template_path, "r", encoding="utf-8") as f:
                logging.info(f"已从默认目录 {default_template_path} 加载 {template_name} 模板")
                return f.read()
        except Exception as e:
            logging.warning(f"加载默认模板文件 {default_template_path} 失败: {e}")

    logging.warning(f"未找到 {template_name} 模板文件，使用空模板")
    return ""

def create_log_callback(project, task_id: int, progress_callback=None):
    """
    创建日志回调函数

    Args:
        project_name: 项目名称
        task_id: 任务ID
        progress_callback: 进度回调函数

    Returns:
        callable: 日志回调函数
    """

    # 创建LLM日志管理器
    llm_log_manager = LLMLogManager(project, task_id)

    def log_and_callback(progress: int, title: str, content: str = ""):
        # 使用LLM日志管理器记录日志
        content = content.replace("\n", "↵ ")
        llm_log_manager.add_log_entry(f"{progress}%", title, content)

         # 调用回调函数
        if progress_callback:
            progress_callback(progress, title, content)
        else:
            print(f"{progress}% - {title}: {content[:100]}")

    return log_and_callback
    
# 清理某个任务的日志
def clear_llm_log(project, task_id: int):
    """清空某个任务的日志文件"""
    llm_log_manager = LLMLogManager(project, task_id)
    llm_log_manager.clear_logs()

def run_git_command(project,
    args: List[str],
    cwd: Optional[str] = None,
    sensitive_values: Optional[List[str]] = None,
    allow_insecure_ssl: bool = False,
):
    """运行Git命令，出错时抛出解释性异常"""
    env = os.environ.copy()
    env["GIT_TERMINAL_PROMPT"] = "0"
    if allow_insecure_ssl:
        env["GIT_SSL_NO_VERIFY"] = "1"
    try:
        subprocess.run(
            args,
            cwd=cwd,
            check=True,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
    except subprocess.CalledProcessError as e:
        stderr = e.stderr.decode("utf-8", errors="ignore") if e.stderr else str(e)
        if sensitive_values:
            for value in sensitive_values:
                if not value:
                    continue
                stderr = stderr.replace(value, "***")
                stderr = stderr.replace(quote(value, safe=""), "***")
        raise ValueError(stderr.strip() or "Git命令执行失败") from e


def extract_repo_name(git_url: str) -> str:
    """根据Git地址提取仓库名称"""
    if not git_url:
        return "project"

    # 从URL中提取仓库名
    # 支持多种格式：https://github.com/user/repo.git、**************:user/repo.git等
    if git_url.endswith(".git"):
        git_url = git_url[:-4]

    # 按斜杠或冒号分割，取最后一部分
    parts = git_url.split("/")[-1].split(":")[-1]
    return parts or "project"


def build_git_work_dir(provided_dir: str, git_url: str, data_dir: str = "./data") -> str:
    """确定Git仓库的工作目录，默认位于当前目录下的data目录"""
    repo_name = extract_repo_name(git_url)
    base_dir = data_dir

    if provided_dir:
        return provided_dir

    # 构建默认工作目录路径
    work_dir = os.path.join(base_dir, repo_name)
    return work_dir


def build_clone_url(git_url: str, token: str) -> str:
    """构造包含访问令牌的Git地址（仅对HTTP(S)有效）"""
    if not token or not git_url.lower().startswith(("http://", "https://")):
        return git_url

    # 解析URL并插入令牌，使用oauth2格式
    if "://" in git_url:
        protocol, rest = git_url.split("://", 1)
        if "@" in rest:
            # 已经包含认证信息，不再插入
            return git_url
        return f"{protocol}://oauth2:{token}@{rest}"

    return git_url


def git_clone(project, target_dir: str, git_url: str, token: str, branch: str = None):
    """
    克隆Git仓库到目标目录

    Args:
        project: 项目实例
        target_dir: 目标目录
        git_url: Git仓库地址
        token: 访问令牌
        branch: 指定分支
    """
    clone_url = build_clone_url(git_url, token)
    logging.info(f"正在克隆仓库 {clone_url} 到 {target_dir}")
    clone_args = ["git", "clone", "--single-branch"]
    # 总是添加SSL验证跳过配置，解决TLS握手问题
    clone_args.append("-c")
    clone_args.append("http.sslVerify=false")
    if branch:
        clone_args.extend(["--branch", branch])
    clone_args.extend([clone_url, target_dir])
    # 对于HTTPS URL，启用环境变量方式的SSL跳过
    allow_insecure_ssl = git_url.lower().startswith("https://")

    try:
        run_git_command(
            project,
            clone_args,
            cwd=None,
            sensitive_values=[token],
            allow_insecure_ssl=allow_insecure_ssl,
        )
        # 克隆完成后，将远程地址重设为原始地址，避免令牌存入配置
        if token and git_url.lower().startswith(("http://", "https://")):
            run_git_command(project,
                ["git", "remote", "set-url", "origin", git_url], cwd=target_dir
            )
    except ValueError as e:
        # 清理可能残留的.git目录
        shutil.rmtree(target_dir, ignore_errors=True)
        raise ValueError(f"克隆Git仓库失败: {str(e)}")


def git_checkout_branch(project, repo_dir: str, base_branch: str) -> str:
    """
    基于指定分支创建带时间戳的新分支

    Args:
        project: 项目实例
        repo_dir: 仓库目录
        base_branch: 基础分支名

    Returns:
        str: 新分支名称
    """
    branch_to_use = base_branch or "main"
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    new_branch = f"{branch_to_use}-{timestamp}"

    try:
        # 确保工作树位于基础分支
        run_git_command(project, ["git", "checkout", branch_to_use], cwd=repo_dir)
        run_git_command(project,
            ["git", "checkout", "-b", new_branch],
            cwd=repo_dir,
        )
    except ValueError as e:
        raise ValueError(f"创建新分支失败: {str(e)}")

    return new_branch


def git_commit(project, repo_dir: str, message: str, files: Optional[List[str]] = None):
    """
    提交文件到Git仓库

    Args:
        project: 项目实例
        repo_dir: 仓库目录
        message: 提交消息
        files: 要提交的文件列表，如果为None则提交所有更改
    """
    try:
        # 如果指定了文件，先添加这些文件
        if files:
            for file_path in files:
                run_git_command(project, ["git", "add", file_path], cwd=repo_dir)
        else:
            # 添加所有更改
            run_git_command(project, ["git", "add", "."], cwd=repo_dir)

        # 检查是否有待提交的更改
        run_git_command(project, ["git", "diff", "--cached", "--quiet"], cwd=repo_dir)

        # 提交更改
        run_git_command(project, ["git", "commit", "-m", message], cwd=repo_dir)
        logging.info(f"成功提交更改: {message}")

    except subprocess.CalledProcessError as e:
        if "nothing to commit" in str(e) or "no changes added to commit" in str(e):
            logging.info("没有需要提交的更改")
            return
        raise ValueError(f"提交失败: {str(e)}")


def git_push(project, repo_dir: str, remote: str = "origin", branch: str = None, log_and_callback = None):
    """
    推送提交到远程仓库

    Args:
        project: 项目实例
        repo_dir: 仓库目录
        remote: 远程仓库名称
        branch: 要推送的分支，如果为None则推送当前分支
    """
    try:
        # 如果没有指定分支，获取当前分支
        if not branch:
            result = subprocess.run(
                ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                cwd=repo_dir,
                capture_output=True,
                text=True,
                check=True
            )
            branch = result.stdout.strip()

        # 临时设置带token的remote URL用于推送
        original_url = None
        if hasattr(project, 'git_url') and hasattr(project, 'git_access_token') and project.git_access_token:
            try:
                # 获取原始remote URL
                result = subprocess.run(
                    ["git", "remote", "get-url", remote],
                    cwd=repo_dir,
                    capture_output=True,
                    text=True,
                    check=True
                )
                original_url = result.stdout.strip()

                # 设置带token的URL
                token_url = build_clone_url(project.git_url, project.git_access_token)
                run_git_command(project, ["git", "remote", "set-url", remote, token_url],
                              cwd=repo_dir, sensitive_values=[project.git_access_token])

            except subprocess.CalledProcessError:
                # 如果无法获取原始URL，继续尝试推送
                pass

        try:
            # 推送到远程仓库，如果是HTTPS且启用SSL跳过
            allow_insecure_ssl = (hasattr(project, 'git_url') and
                                 project.git_url and
                                 project.git_url.lower().startswith("https://"))

            run_git_command(project, ["git", "push", remote, branch],
                          cwd=repo_dir,
                          allow_insecure_ssl=allow_insecure_ssl,
                          sensitive_values=[project.git_access_token] if hasattr(project, 'git_access_token') else None)
            logging.info(f"成功推送分支 {branch} 到 {remote}")
            if log_and_callback:
                log_and_callback(100, "Git", f"成功推送分支 {branch} 到 {remote}")

        finally:
            # 恢复原始remote URL
            if original_url:
                try:
                    run_git_command(project, ["git", "remote", "set-url", remote, original_url], cwd=repo_dir)
                except subprocess.CalledProcessError:
                    logging.warning(f"无法恢复原始remote URL: {original_url}")

    except subprocess.CalledProcessError as e:
        raise ValueError(f"推送失败: {str(e)}")


def git_pull(project, repo_dir: str, remote: str = "origin", branch: str = None):
    """
    从远程仓库拉取最新更改

    Args:
        project: 项目实例
        repo_dir: 仓库目录
        remote: 远程仓库名称
        branch: 要拉取的分支，如果为None则拉取当前分支
    """
    try:
        # 如果没有指定分支，获取当前分支
        if not branch:
            result = subprocess.run(
                ["git", "rev-parse", "--abbrev-ref", "HEAD"],
                cwd=repo_dir,
                capture_output=True,
                text=True,
                check=True
            )
            branch = result.stdout.strip()

        # 临时设置带token的remote URL用于拉取
        original_url = None
        if hasattr(project, 'git_url') and hasattr(project, 'git_access_token') and project.git_access_token:
            try:
                # 获取原始remote URL
                result = subprocess.run(
                    ["git", "remote", "get-url", remote],
                    cwd=repo_dir,
                    capture_output=True,
                    text=True,
                    check=True
                )
                original_url = result.stdout.strip()

                # 设置带token的URL
                token_url = build_clone_url(project.git_url, project.git_access_token)
                run_git_command(project, ["git", "remote", "set-url", remote, token_url],
                              cwd=repo_dir, sensitive_values=[project.git_access_token])

            except subprocess.CalledProcessError:
                # 如果无法获取原始URL，继续尝试拉取
                pass

        try:
            # 从远程仓库拉取，如果是HTTPS且启用SSL跳过
            allow_insecure_ssl = (hasattr(project, 'git_url') and
                                 project.git_url and
                                 project.git_url.lower().startswith("https://"))

            run_git_command(project, ["git", "pull", remote, branch],
                          cwd=repo_dir,
                          allow_insecure_ssl=allow_insecure_ssl,
                          sensitive_values=[project.git_access_token] if hasattr(project, 'git_access_token') else None)
            logging.info(f"成功从 {remote} 拉取分支 {branch} 的最新更改")

        finally:
            # 恢复原始remote URL
            if original_url:
                try:
                    run_git_command(project, ["git", "remote", "set-url", remote, original_url], cwd=repo_dir)
                except subprocess.CalledProcessError:
                    logging.warning(f"无法恢复原始remote URL: {original_url}")

    except subprocess.CalledProcessError as e:
        raise ValueError(f"拉取失败: {str(e)}")


def git_get_current_branch(repo_dir: str) -> str:
    """
    获取当前分支名称

    Args:
        repo_dir: 仓库目录

    Returns:
        str: 当前分支名称
    """
    try:
        result = subprocess.run(
            ["git", "rev-parse", "--abbrev-ref", "HEAD"],
            cwd=repo_dir,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        raise ValueError(f"获取当前分支失败: {str(e)}")


def git_has_uncommitted_changes(repo_dir: str) -> bool:
    """
    检查是否有未提交的更改

    Args:
        repo_dir: 仓库目录

    Returns:
        bool: 是否有未提交的更改
    """
    try:
        # 检查是否有未提交的更改
        subprocess.run(
            ["git", "diff", "--quiet"],
            cwd=repo_dir,
            check=True
        )
        subprocess.run(
            ["git", "diff", "--cached", "--quiet"],
            cwd=repo_dir,
            check=True
        )
        return False
    except subprocess.CalledProcessError:
        return True