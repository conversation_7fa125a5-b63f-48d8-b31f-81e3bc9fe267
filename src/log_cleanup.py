#!/usr/bin/env python3
"""
日志清理模块
负责定期清理~/.claude/projects目录下的.jsonl日志文件
"""

import os
import time
import threading
import logging
from datetime import datetime, timedelta
from pathlib import Path
import gevent

def check_session_exists(work_dir, session_id, delete : bool = False):
    if not session_id:
        return False
    # 检查session_id对应的文件是否存在
    # 在用户主目录下的.claude/projects路径/项目路径下查询session_id.jsonl文件是否存在
    # 其中项目路径名为work_dir的路径分隔符替换为-，如：/mnt/d/aicode/csdk 替换为 -mnt-d-aicode-csdk
    try:
        # 获取用户主目录下的.claude/projects路径
        home_dir = Path.home()
        projects_dir = home_dir / '.claude' / 'projects'
        
        # 先将work_dir转换为绝对路径
        abs_work_dir = os.path.abspath(work_dir)
        
        # 构造项目目录名：将绝对路径的分隔符替换为-
        project_dir_name = abs_work_dir.replace(os.sep, '-')
        
        # 构造完整的session文件路径
        session_file_path = projects_dir / project_dir_name / f"{session_id}.jsonl"
        
        # 检查文件是否存在
        is_exists = session_file_path.exists()
        if delete and is_exists:
           os.remove(session_file_path)
        
        return is_exists
    except Exception as e:
        logging.error(f"检查session文件存在性时发生错误: {e}")
        return False
    
def clean_project_logs():
    """
    清理项目日志文件
    清理规则：前3天的日志只保留最新的1个文件，最近3天的日志都保留
    """
    # 获取用户主目录下的.claude/projects路径
    home_dir = Path.home()
    projects_dir = home_dir / '.claude' / 'projects'
    
    if not projects_dir.exists():
        logging.info(f"项目目录不存在: {projects_dir}")
        return
    
    # 遍历所有项目目录
    for project_dir in projects_dir.iterdir():
        if not project_dir.is_dir():
            continue
            
        # 查找所有.jsonl文件
        jsonl_files = list(project_dir.glob("*.jsonl"))
        if not jsonl_files:
            continue
            
        # 按修改时间排序
        jsonl_files.sort(key=lambda x: x.stat().st_mtime)
        
        # 计算3天前的时间
        three_days_ago = datetime.now() - timedelta(days=3)
        
        # 分离3天前和最近3天的文件
        old_files = []
        recent_files = []
        
        for file_path in jsonl_files:
            file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
            if file_mtime < three_days_ago:
                old_files.append(file_path)
            else:
                recent_files.append(file_path)
        
        # 对于3天前的文件，只保留最新的一个
        if len(old_files) > 1:
            # 按修改时间排序，保留最新的
            old_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            files_to_delete = old_files[1:]  # 除了最新的一个，其他都删除
            
            for file_to_delete in files_to_delete:
                try:
                    file_to_delete.unlink()
                    logging.info(f"已删除旧日志文件: {file_to_delete}")
                except Exception as e:
                    logging.error(f"删除文件 {file_to_delete} 失败: {e}")
def get_seccion_content(work_dir: str, session_id: str):
    return 0
def start_log_cleanup_thread():
    """
    启动日志清理线程
    """
    def cleanup_loop():
        while True:
            try:
                clean_project_logs()
            except Exception as e:
                logging.error(f"日志清理过程中发生错误: {e}")
            
            # 每2小时执行一次 (2 * 60 * 60 秒)
            gevent.sleep(2 * 60 * 60)
    
    # 创建并启动后台线程
    cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
    cleanup_thread.start()
    logging.info("日志清理线程已启动")