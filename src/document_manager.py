"""
文档管理器模块
实现需求优化和设计生成等功能
"""

import os
import uuid
import logging
from typing import Dict, Any, Optional, Callable
from datetime import datetime

try:
    from .claude.claude_agent import ClaudeAgent
except ImportError:
    from claude.claude_agent import ClaudeAgent

try:
    from .utils import load_template_by_type, create_log_callback
    from .llm.llm_agent import LLMAgent
except ImportError:
    from llm.llm_agent import LLMAgent
    from utils import load_template_by_type, create_log_callback

from config import Config


class DocumentManager:
    """文档管理器"""

    def __init__(
        self, project, log_manager=None, knowledge_manager=None, task_manager=None
    ):
        self.project = project
        self.project_name = project.name
        self.work_dir = project.work_dir
        self.provider = project.provider
        self.log_manager = log_manager
        self.knowledge_manager = knowledge_manager
        self.task_manager = task_manager
        self._load_template()

    def _load_template(self):
        # 根据项目类型加载需求和设计的模板
        self.requirement_template = load_template_by_type(
            self.project, "requirement.md"
        )
        self.design_template = load_template_by_type(self.project, "design.md")

    # 根据项目优化用户的需求
    def optimize_requirement(
        self, user_req, enable_kb: bool, progress_callback=None
    ) -> Dict[str, Any]:
        prompt = "你是一名资深产品经理 + 需求分析师。"

        result = {}
        try:
            logging.info(f"正在调用 大模型-{self.provider} 完善需求...")
            file_uuid = str(uuid.uuid4())
            req_file = os.path.join(
                self.work_dir, Config.AI_WORK_DIR, f"{file_uuid}.md"
            )
            # task_id = -1 用于记录需求优化的日志
            log_callback = create_log_callback(self.project, -1, progress_callback)

            claude_agent = ClaudeAgent(
                prompt, self.provider, self.knowledge_manager if enable_kb else None
            )

            user_req = f"""# 请结合项目或根据你的理解，对以下用户需求进行完善和优化。，并输出需求功能规格书，写入${req_file}文件中。
=== 用户需求开始 ===
```
                {user_req}
```
=== 用户需求结束 ===
            """
            if self.requirement_template:
                user_req += f"""# 输出格式：请按照下面的需求功能规格模板组织需求规格书的内容，模板中不适用本需求的章节可以忽略。
=== 需求功能规格模板开始 ===
```
                {self.requirement_template}
```
=== 需求功能规格模板结束 ===
                """

            result = claude_agent.run_agent_sync(
                self.work_dir, user_req, None, "", log_callback
            )

            if result.get("success"):
                logging.info(f"需求整理完成.")
                # 保存session_id到项目
                if "session_id" in result:
                    self.project.session_id = result["session_id"]
                # return result
                # 读取需求文档
                with open(req_file, "r") as f:
                    req_data = f.read()
                    result["message"] = req_data
                # 删除临时文件
                os.remove(req_file)
                # 写入用户的需求文档中并保存
                self.project.requirement = req_data
                self.project.save_requirement_to_file(self.knowledge_manager)
                return result
        except Exception as e:
            logging.error(f"需求整理出错: {e}")
            result["success"] = False
            result["message"] = str(e)

        return result

    # 根据用户的需求生成设计
    def gen_design(
        self, user_req, enable_kb: bool, progress_callback=None
    ) -> Dict[str, Any]:
        # 根据项目类型设置不同的专业角色和提示词
        prompt = (
            "你是一名资深系统架构师，可以根据用户需求生成详细设计，用于后续的代码生成。"
        )

        result = {}
        try:
            logging.info(f"正在调用 大模型-{self.provider} 生成设计...")
            file_uuid = str(uuid.uuid4())
            design_file = os.path.join(
                self.work_dir, Config.AI_WORK_DIR, f"{file_uuid}.md"
            )
            # task_id = -1 用于记录设计生成的日志
            log_callback = create_log_callback(self.project, -1, progress_callback)

            claude_agent = ClaudeAgent(prompt, self.provider, self.knowledge_manager if enable_kb else None)  # type: ignore

            # 构建更详细的设计生成提示词
            design_prompt = f"""# 请结合项目背景，根据以下用户需求生成详细的设计文档，并写入到"{design_file}"文件中。

=== 用户需求开始 ===
```{user_req}
```
=== 用户需求结束 ===

# 设计要求：
1. 深入理解用户需求的核心功能和业务目标
2. 生成完整、详细、可执行的技术设计方案
3. 设计方案应具备良好的架构性、可扩展性和可维护性
4. 包含必要的技术细节和实现要点
"""

            # 如果有设计模板，添加模板要求
            if self.design_template:
                design_prompt += f"""
# 输出格式要求：
请严格按照下面的设计模板组织设计文档内容，模板中不适用本需求的章节可以适当简化，但必须保持模板的整体结构和编号格式。

=== 设计模板开始 ===
```
{self.design_template}
```
=== 设计模板结束 ===

# 特别说明：
- 必须严格遵循模板中的章节编号格式（如：1.1、2.3.1等）
- 每个章节都应包含具体的、可操作的设计内容
- 技术方案要详细具体，避免泛泛而谈
- 确保设计方案的完整性和可实施性
"""

            result = claude_agent.run_agent_sync(
                self.work_dir, design_prompt, None, "", log_callback
            )

            if result.get("success"):
                logging.info(f"生成设计完成.")
                # 保存session_id到项目
                if "session_id" in result:
                    self.project.session_id = result["session_id"]
                # 读取设计文档
                with open(design_file, "r", encoding="utf-8") as f:
                    design_data = f.read()
                    result["message"] = design_data
                # 删除临时文件
                os.remove(design_file)
                # 保存设计文档到项目中
                self.project.design = design_data
                self.project.save_design_to_file(self.knowledge_manager)
                return result
        except Exception as e:
            logging.error(f"生成设计出错: {e}")
            result["success"] = False
            result["message"] = str(e)

        return result

    # 直接实现用户需求
    def run_requirement(
        self, user_req, enable_kb: bool, progress_callback=None
    ) -> Dict[str, Any]:
        """直接实现用户需求"""
        try:
            log_callback = create_log_callback(
                self.project, task_id=-1, progress_callback=progress_callback
            )
            claude_agent = ClaudeAgent(
                "你是一个全栈工程师，可完成用户提出的请求",
                self.provider,
                self.knowledge_manager if enable_kb else None,
            )
            result = claude_agent.run_agent_sync(
                self.work_dir, user_req, None, "", log_callback
            )
            # 保存session_id到项目
            if result.get("success") and "session_id" in result:
                self.project.session_id = result["session_id"]
            return result
        except Exception as e:
            return {"success": False, "message": f"执行需求失败: {str(e)}"}

    # 直接实现用户的设计
    def run_design(
        self, design_doc, enable_kb: bool, progress_callback=None
    ) -> Dict[str, Any]:
        """直接实现用户的设计"""
        try:
            log_callback = create_log_callback(
                self.project, task_id=-1, progress_callback=progress_callback
            )
            claude_agent = ClaudeAgent(
                "你是一个全栈工程师，可完成用户提出的请求",
                self.provider,
                self.knowledge_manager if enable_kb else None,
            )
            result = claude_agent.run_agent_sync(
                self.work_dir, design_doc, None, "", log_callback
            )
            # 保存session_id到项目
            if result.get("success") and "session_id" in result:
                self.project.session_id = result["session_id"]
            return result
        except Exception as e:
            return {"success": False, "message": f"执行设计失败: {str(e)}"}

    # 优化文档片段
    def optimize_document(
        self, data, enable_kb: bool, progress_callback=None
    ) -> Dict[str, Any]:
        selection = data.get("selection", "")
        doc_type = data.get("doc_type", "requirement")
        ai_action = data.get("ai_action", "润色")

        if doc_type == "requirement":
            prompt = "你是一名需求分析师。"
        elif doc_type == "design":
            prompt = "你是一名系统架构师。"
        else:
            prompt = "你是一个全栈工程师。"
        if enable_kb and self.knowledge_manager is not None:
            prompt += "\n你可以搜索文档或代码知识库进行参考，需严格遵守约束：最多查询1次知识库，如果第一次搜索没有找到相关内容，则停止搜索并基于你的知识进行处理。"

        user_content = f"""请{ai_action}以下内容，并只输出 {ai_action} 后的内容：
            {selection}
            """

        result = {}
        try:
            logging.info(f"正在调用 大模型-{self.provider} 进行文档片段完善...")

            # task_id = -1 用于文档优化的日志
            log_callback = create_log_callback(self.project, 0, progress_callback)

            llm_agent = LLMAgent(prompt, self.knowledge_manager if enable_kb else None)  # type: ignore
            result = llm_agent.run_agent_sync(
                self.work_dir,
                user_content,
                session_id=None,
                nothink=" /nothink",
                log_callback=log_callback,
            )

            if result.get("success"):
                logging.info(f"文档片段优化完成.")
                return result
        except Exception as e:
            logging.error(f"文档片段优化出错: {e}")
            result["success"] = False
            result["message"] = str(e)

        return result

    def askAI(self, data, kb_manager=None, progress_callback=None) -> Dict[str, Any]:
        selection = data.get("selection", "")
        doc_type = data.get("doc_type")
        user_input = data.get("user_input")
        deep_think = data.get("deep_think", False)

        if doc_type == "requirement":
            prompt = "你是一名需求分析师。"
        elif doc_type == "design":
            prompt = "你是一名系统架构师。"
        else:
            prompt = "你是一个全栈工程师。"
        prompt = prompt + "请根据用户选择的文本，对用户请求进行回答。"

        if kb_manager and not deep_think:
            prompt += "\n你可以搜索文档或代码知识库进行参考，需严格遵守约束：最多查询1次知识库，如果第一次搜索没有找到相关内容，则停止搜索并基于你的知识进行处理。"

        user_content = (
            f"""请针对如下文本:{selection}。\n 分析并回答如下问题: {user_input}"""
        )

        result = {}
        try:
            logging.info(f"正在调用 大模型-{self.provider} 回答问题...")

            # task_id = -1 用于文档优化的日志
            log_callback = create_log_callback(self.project, 0, progress_callback)

            if deep_think:
                user_content += "\n只需回答我的问题，不需要任何实现。\n只输出回答。"
                ai_agent = ClaudeAgent(prompt, self.provider, kb_manager if kb_manager is not None else self.knowledge_manager, "plan")  # type: ignore
            else:
                ai_agent = LLMAgent(prompt, kb_manager if kb_manager is not None else self.knowledge_manager)  # type: ignore

            result = ai_agent.run_agent_sync(
                self.work_dir,
                user_content,
                session_id=None,
                # nothink=" /nothink",
                log_callback=log_callback,
            )

            if result.get("success"):
                logging.info(f"AI处理完成.")
                # 保存session_id到项目
                if "session_id" in result:
                    self.project.session_id = result["session_id"]
                return result
        except Exception as e:
            logging.error(f"AI处理出错: {e}")
            result["success"] = False
            result["message"] = str(e)

        return result

    def new_task(self, data, enable_kb: bool, progress_callback=None) -> Dict[str, Any]:
        selection = data.get("selection", "")
        doc_type = data.get("doc_type")
        is_run = data.get("is_run", False)
        if self.task_manager is None:
            return {"success": False, "message": "TaskManager未初始化"}
        if doc_type == "requirement":
            return self.task_manager.run_requirement(
                selection, enable_kb, progress_callback, is_run
            )
        elif doc_type == "design":
            return self.task_manager.run_design(
                selection, enable_kb, progress_callback, is_run
            )
        else:
            return self.task_manager.quick_task(
                selection, enable_kb, progress_callback, is_run
            )

    def drawAI(self, data, enable_kb: bool, progress_callback=None) -> Dict[str, Any]:
        selection = data.get("selection", "")
        draw_type = data.get("draw_type")
        prompt = data.get("prompt")
        deep_think = data.get("deep_think", False)

        user_content = f"""# 要求
        根据以下文字绘制{draw_type}，并输出mermaid格式。
        {selection}
        """
        if prompt:
            user_content += f"# 补充说明\n{prompt}"

        if enable_kb and not deep_think:
            user_content += "\n# 知识库 \n 你可以搜索文档或代码知识库进行参考，需严格遵守约束：最多查询1次知识库，如果第一次搜索没有找到相关内容，则停止搜索并基于你的知识进行处理。"

        result = {}
        try:
            logging.info(f"正在调用 大模型-{self.provider} 绘图...")

            # task_id = -1 用于文档优化的日志
            log_callback = create_log_callback(self.project, 0, progress_callback)
            ai_agent = LLMAgent("", self.knowledge_manager if enable_kb else None)  # type: ignore

            result = ai_agent.run_agent_sync(
                self.work_dir,
                user_content,
                session_id=None,
                # nothink=" /nothink",
                log_callback=log_callback,
            )

            if result.get("success"):
                logging.info(f"AI处理完成.")
                return result
        except Exception as e:
            logging.error(f"AI处理出错: {e}")
            result["success"] = False
            result["message"] = str(e)

        return result

    def chatAI(
        self,
        session_id,
        user_message,
        enable_kb: bool,
        progress_callback=None,
        context_lines="",
        prev_lines="",
        next_lines="",
    ) -> Dict[str, Any]:
        """
        引导式需求生成，通过与AI往复交互，在AI引导下完成需求规格编写

        Args:
            session_id: 会话ID，用于维护多轮对话
            user_message: 用户输入的消息
            kb_manager: 知识库管理器
            progress_callback: 进度回调函数
            context_lines: 光标前的上下文内容（前10行）

        Returns:
            Dict[str, Any]: 包含success, message, is_final字段
        """

        # 获取当前项目的需求文档内容
        current_requirement = self.project.requirement or ""
        is_empty_requirement = len(current_requirement.strip()) == 0

        # 分析上下文所在章节（主要基于前10行）
        current_section = self._analyze_section(prev_lines) if prev_lines else None

        # 读取项目的 CLAUDE.md 文件内容
        project_context = ""
        claude_md_path = os.path.join(self.work_dir, "CLAUDE.md")
        if os.path.exists(claude_md_path):
            try:
                with open(claude_md_path, "r", encoding="utf-8") as f:
                    claude_content = f.read().strip()
                    if claude_content:
                        project_context = f"""
# 项目信息及约束
```{claude_content}
```
"""
            except Exception as e:
                logging.warning(f"读取项目 CLAUDE.md 文件失败: {e}")

        project_context = ""
        # 构建系统提示词
        prompt = """你是一名资深产品经理和需求分析师。你的任务是通过引导式对话帮助用户完善需求规格。

"""
        # 添加项目背景信息
        if project_context:
            prompt += project_context + "\n"

        # 场景1：项目需求为空，需要生成完整的需求规格
        if is_empty_requirement:
            prompt += """
# 当前场景：新建需求文档
项目的需求文档目前为空，你需要帮助用户创建完整的需求规格说明书。

# 工作流程
1. 理解用户的初始需求描述
2. 如果需求不够明确，提出3-5个引导性问题（功能目标、目标用户、核心功能、技术约束等）
3. 根据用户回答逐步完善理解
4. 当信息充足时，按照完整的需求模板输出需求规格说明书
"""
            if self.requirement_template:
                prompt += f"""
# 需求规格模板（请严格按照此模板组织输出）
```
{self.requirement_template}
```

# 输出规则
- **引导阶段**：提出清晰的引导问题，每次2-3个问题
- **输出阶段**：必须以"# 需求规格说明书"或"# 需求功能规格"开头，严格按照模板结构组织内容
- 模板中不适用的章节可以简化或省略，但核心章节必须包含
"""
            else:
                prompt += """
# 输出规则
- **引导阶段**：提出清晰的引导问题，每次2-3个问题
- **输出阶段**：输出完整的需求规格说明书，包含：
  - 功能概述
  - 目标用户
  - 核心功能列表
  - 功能详细说明
  - 非功能性需求
  - 约束条件
"""

        # 场景2：根据上下文章节，只输出该章节内容
        elif current_section:
            prompt += f"""
# 当前场景：补充特定章节内容
用户正在编辑需求文档的 **{current_section}** 章节。

# 前文内容（前10行）
```
{prev_lines}
```

# 后文内容（后10行）
```
{next_lines}
```

# 工作流程
1. 理解用户想在当前章节的具体位置补充什么内容
2. 分析前文已有的内容，避免重复
3. 考虑后文的走向，保持内容连贯性
4. 如果不清楚具体需求，提出1-2个针对性的问题
"""
            if self.requirement_template:
                # 提取模板中该章节的结构
                section_template = self._extract_section_template(current_section)
                if section_template:
                    prompt += f"""
# 该章节的模板参考
```
{section_template}
```
"""

            prompt += f"""
# 输出规则
- **引导阶段**：针对 {current_section} 章节的具体位置提出问题
- **输出阶段**：只输出适合插入到当前位置的内容，不要包含前后已有内容
- 使用与前文一致的代码标题级别
- 确保内容与前文和后文保持逻辑连贯
- 不要输出其他章节的内容
- 不要输出完整的文档结构
"""

        # 场景3：无法判断章节，自由输出
        else:
            prompt += """
# 当前场景：自由对话
根据用户的问题自由回答和引导。

# 编辑位置说明
用户在文档的某个位置想要补充内容，但无法确定具体章节。

# 前文内容（前10行）
```
{prev_lines}
```

# 后文内容（后10行）
```
{next_lines}
```

# 工作流程
1. 理解用户的具体问题或需求
2. 分析前后文的上下文关系
3. 如果是询问，直接回答
4. 如果是要补充内容，提供适合插入到当前位置的代码格式文本
5. 如果信息不足，提出引导性问题

# 输出规则
- 根据用户需求和上下文灵活输出
- 如果输出文档内容，使用代码格式，保持与前后文风格一致
- 补充的内容要自然衔接前后文
- 保持简洁实用
""".format(
                prev_lines=prev_lines or "（无前文）",
                next_lines=next_lines or "（无后文）",
            )

        result = {}
        try:
            logging.info(
                f"正在处理需求对话，会话ID: {session_id}, 场景: {'新建文档' if is_empty_requirement else ('章节:' + current_section if current_section else '自由对话')}"
            )

            # task_id = -1 用于需求对话的日志
            log_callback = create_log_callback(self.project, 0, progress_callback)

            # 使用LLMAgent进行对话
            llm_agent = LLMAgent(prompt, self.knowledge_manager if enable_kb else None, enable_session=True)  # type: ignore

            result = llm_agent.run_agent_sync(
                self.work_dir,
                user_message,
                session_id=session_id,
                nothink=" /nothink",
                log_callback=log_callback,
            )

            if result.get("success"):
                message = result.get("message", "")

                # 判断是否是最终输出（不同场景有不同的判断标准）
                is_final = False

                if is_empty_requirement:
                    # 场景1：需要完整文档，判断是否输出了完整需求规格
                    is_final = (
                        message.strip().startswith("# 需求规格说明书")
                        or message.strip().startswith("# 需求功能规格")
                        or ("# 需求" in message[:100] and message.count("#") >= 3)
                    )
                elif current_section:
                    # 场景2：章节补充，判断是否输出了章节内容（包含标题且内容较完整）
                    is_final = (
                        message.count("#") >= 1
                        and len(message.strip()) > 50
                        and not message.strip().endswith("?")
                    )
                else:
                    # 场景3：自由对话，如果输出了代码格式内容且较完整则认为可采纳
                    is_final = (
                        "#" in message
                        and len(message.strip()) > 100
                        and not message.strip().endswith("?")
                    )

                result["is_final"] = is_final
                result["context_info"] = {
                    "is_empty": is_empty_requirement,
                    "section": current_section,
                    "scenario": (
                        "new_doc"
                        if is_empty_requirement
                        else ("section" if current_section else "free")
                    ),
                }
                logging.info(
                    f"需求对话处理完成，is_final={is_final}, scenario={result['context_info']['scenario']}"
                )
                return result
        except Exception as e:
            logging.error(f"需求对话处理出错: {e}")
            result["success"] = False
            result["message"] = str(e)
            result["is_final"] = False

        return result

    def _analyze_section(self, context_lines: str) -> Optional[str]:
        """分析上下文所在的章节"""
        if not context_lines:
            return None

        lines = context_lines.strip().split("\n")

        # 如果上下文包含前后内容，找到中间的分界点（前后10行的交界）
        # 通常第一个空行可能是分界点，或者取中间位置
        middle_line = len(lines) // 2

        # 先从中间位置往前找最近的标题（更可能是当前章节）
        for i in range(middle_line, -1, -1):
            line = lines[i].strip()
            if line.startswith("#"):
                # 提取标题文本
                title = line.lstrip("#").strip()
                return title

        # 如果中间往前没找到，再从中间往后找
        for i in range(middle_line, len(lines)):
            line = lines[i].strip()
            if line.startswith("#"):
                title = line.lstrip("#").strip()
                return title

        return None

    def _extract_section_template(self, section_name: str) -> str:
        """从完整模板中提取特定章节的模板"""
        if not self.requirement_template or not section_name:
            return ""

        lines = self.requirement_template.split("\n")
        section_lines = []
        in_section = False
        section_level = 0

        for line in lines:
            # 检测是否是目标章节的开始
            if section_name in line and line.strip().startswith("#"):
                in_section = True
                section_level = len(line) - len(line.lstrip("#"))
                section_lines.append(line)
                continue

            if in_section:
                # 遇到同级或更高级标题，结束
                if line.strip().startswith("#"):
                    current_level = len(line) - len(line.lstrip("#"))
                    if current_level <= section_level:
                        break
                section_lines.append(line)

        return "\n".join(section_lines) if section_lines else ""

    def init_rules(self, progress_callback=None) -> Dict[str, Any]:
        """
        初始化项目规则
        通过调用 ClaudeAgent，使用 /init 请求生成项目规则
        """
        prompt = """"""

        result = {}
        claude_md_path = os.path.join(self.work_dir, "CLAUDE.md")
        claude_md_backup_path = os.path.join(self.work_dir, "CLAUDE.md.backup")
        original_content = ""

        try:
            logging.info(f"正在为项目 {self.project_name} 初始化规则...")

            # 备份 CLAUDE.md 文件
            if os.path.exists(claude_md_path):
                with open(claude_md_path, "r", encoding="utf-8") as f:
                    original_content = f.read()
                # 创建备份文件
                with open(claude_md_backup_path, "w", encoding="utf-8") as f:
                    f.write(original_content)
            # 清空 CLAUDE.md 文件
            with open(claude_md_path, "w", encoding="utf-8") as f:
                f.write("")
            logging.info("已备份 CLAUDE.md 文件")

            # task_id = -1 用于规则初始化的日志
            log_callback = create_log_callback(self.project, -1, progress_callback)

            # 使用 ClaudeAgent 处理 /init 请求
            claude_agent = ClaudeAgent(prompt, self.provider, self.knowledge_manager if self.knowledge_manager is not None else None)  # type: ignore

            result = claude_agent.run_agent_sync(
                self.work_dir, "/init 中文", None, "", log_callback
            )

            if result.get("success"):
                # 保存session_id到项目
                if "session_id" in result:
                    self.project.session_id = result["session_id"]
                # 执行完成后，提取新生成的 CLAUDE.md 内容作为结果
                try:
                    with open(claude_md_path, "r", encoding="utf-8") as f:
                        new_content = f.read().strip()
                    if new_content:
                        result["message"] = new_content
                        logging.info("已提取生成的 CLAUDE.md 内容作为规则")
                    else:
                        logging.warning("生成的 CLAUDE.md 文件为空")
                except Exception as e:
                    logging.warning(f"读取生成的 CLAUDE.md 文件失败: {e}")
            else:
                logging.error(f"项目规则初始化失败: {result.get('message')}")

        except Exception as e:
            logging.error(f"项目规则初始化出错: {e}")
            result["success"] = False
            result["message"] = str(e)

        finally:
            # 恢复原来的 CLAUDE.md 文件
            try:
                if os.path.exists(claude_md_backup_path):
                    with open(claude_md_backup_path, "r", encoding="utf-8") as f:
                        backup_content = f.read()
                    with open(claude_md_path, "w", encoding="utf-8") as f:
                        f.write(backup_content)
                    # 删除备份文件
                    os.remove(claude_md_backup_path)
                    logging.info("已恢复原始 CLAUDE.md 文件并删除备份")
                elif original_content:
                    # 如果备份文件不存在但有原始内容，直接恢复
                    with open(claude_md_path, "w", encoding="utf-8") as f:
                        f.write(original_content)
                    logging.info("已恢复原始 CLAUDE.md 文件")
            except Exception as e:
                logging.error(f"恢复 CLAUDE.md 文件失败: {e}")

        return result
