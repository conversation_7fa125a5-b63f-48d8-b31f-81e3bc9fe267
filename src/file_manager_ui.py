"""
文件管理UI模块
处理与文件管理相关的路由
"""

import os
import json
import logging
from flask import Blueprint, request, jsonify, send_file
from project_manager import ProjectManager

# 创建蓝图
file_manager_bp = Blueprint("file_manager", __name__)

# 全局变量
project_manager = None
app = None

def init_file_manager_app(pm: ProjectManager, app_instance=None):
    """设置项目管理器实例"""
    global project_manager, app
    project_manager = pm
    app = app_instance

@file_manager_bp.route("/aicode/file_manager.html")
def file_manager_html():
    """文件管理HTML"""
    return app.send_static_file("file_manager.html")

@file_manager_bp.route("/aicode/api/projects/<project_id>/files", methods=["GET"])
def api_list_files(project_id):
    """获取项目文件列表API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        path = request.args.get('path', '')
        files = file_manager.list_files(path)
        
        return jsonify({
            "success": True,
            "files": [file_info.to_dict() for file_info in files],
            "path": path
        })

    except Exception as e:
        logging.error(f"获取文件列表失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/content", methods=["GET"])
def api_get_file_content(project_id):
    """获取文件内容API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        path = request.args.get('path', '')
        if not path:
            return jsonify({"error": "文件路径不能为空"}), 400

        result = file_manager.get_file_content(path)
        if result is None:
            return jsonify({"error": "文件不存在或无法读取"}), 404

        return jsonify(result)

    except Exception as e:
        logging.error(f"获取文件内容失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/save", methods=["POST"])
def api_save_file(project_id):
    """保存文件内容API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据不能为空"}), 400

        path = data.get('path', '')
        content = data.get('content', '')

        if not path:
            return jsonify({"error": "文件路径不能为空"}), 400

        result = file_manager.save_file(path, content)
        return jsonify(result)

    except Exception as e:
        logging.error(f"保存文件失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/create", methods=["POST"])
def api_create_file(project_id):
    """创建新文件API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据不能为空"}), 400

        path = data.get('path', '')
        content = data.get('content', '')
        is_directory = data.get('is_directory', False)

        if not path:
            return jsonify({"error": "文件路径不能为空"}), 400

        if is_directory:
            result = file_manager.create_directory(path)
        else:
            result = file_manager.create_file(path, content)

        return jsonify(result)

    except Exception as e:
        logging.error(f"创建文件失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/delete", methods=["DELETE"])
def api_delete_file(project_id):
    """删除文件API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据不能为空"}), 400

        path = data.get('path', '')
        if not path:
            return jsonify({"error": "文件路径不能为空"}), 400

        result = file_manager.delete_file(path)
        return jsonify(result)

    except Exception as e:
        logging.error(f"删除文件失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/batch_delete", methods=["DELETE"])
def api_batch_delete_files(project_id):
    """批量删除文件API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据不能为空"}), 400

        paths = data.get('paths', [])
        if not paths:
            return jsonify({"error": "文件路径列表不能为空"}), 400

        success_count = 0
        failed_count = 0
        failed_files = []

        # 逐个删除文件
        for path in paths:
            try:
                result = file_manager.delete_file(path)
                if result.get('success', False):
                    success_count += 1
                else:
                    failed_count += 1
                    failed_files.append({
                        'path': path,
                        'error': result.get('message', '删除失败')
                    })
            except Exception as e:
                failed_count += 1
                failed_files.append({
                    'path': path,
                    'error': str(e)
                })

        return jsonify({
            "success": failed_count == 0,
            "message": f"批量删除完成：成功 {success_count} 个，失败 {failed_count} 个",
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_files": failed_files
        })

    except Exception as e:
        logging.error(f"批量删除文件失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/rename", methods=["POST"])
def api_rename_file(project_id):
    """重命名文件API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据不能为空"}), 400

        old_path = data.get('old_path', '')
        new_path = data.get('new_path', '')

        if not old_path or not new_path:
            return jsonify({"error": "文件路径不能为空"}), 400

        result = file_manager.rename_file(old_path, new_path)
        return jsonify(result)

    except Exception as e:
        logging.error(f"重命名文件失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/download", methods=["GET"])
def api_download_file(project_id):
    """下载文件API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        path = request.args.get('path', '')
        if not path:
            return jsonify({"error": "文件路径不能为空"}), 400

        file_path = file_manager.get_file_path(path)
        if not file_path:
            return jsonify({"error": "文件不存在"}), 404

        return send_file(file_path, as_attachment=True)

    except Exception as e:
        logging.error(f"下载文件失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/stats", methods=["GET"])
def api_get_project_stats(project_id):
    """获取项目文件统计信息API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        stats = file_manager.get_project_stats()
        return jsonify({
            "success": True,
            "stats": stats
        })

    except Exception as e:
        logging.error(f"获取项目统计失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/upload", methods=["POST"])
def api_upload_file(project_id):
    """上传文件API"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({"error": "没有文件被上传"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "文件名为空"}), 400

        # 获取目标路径（可选）
        target_path = request.form.get('path', '')

        # 构建完整的文件路径
        if target_path:
            file_path = os.path.join(target_path, file.filename)
        else:
            file_path = file.filename

        # 构建文件的完整路径
        full_file_path = os.path.join(file_manager.project_root, file_path)

        # 安全检查：确保路径在项目根目录内
        if not file_manager._is_safe_path(full_file_path):
            return jsonify({"error": "文件路径不安全"}), 400

        # 确保目录存在
        os.makedirs(os.path.dirname(full_file_path), exist_ok=True)

        # 直接保存二进制文件内容，不进行文本解码
        file.save(full_file_path)

        return jsonify({
            "success": True,
            "message": "文件上传成功",
            "file_path": file_path
        })

    except Exception as e:
        logging.error(f"文件上传失败: {e}")
        return jsonify({"error": str(e)}), 500

@file_manager_bp.route("/aicode/api/projects/<project_id>/files/download_multiple", methods=["POST"])
def api_download_multiple_files(project_id):
    """批量下载文件API - 返回ZIP压缩包"""
    try:
        import zipfile
        import io
        from flask import make_response

        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404

        file_manager = project_manager.get_file_manager(project_id)
        if not file_manager:
            return jsonify({"error": "无法获取文件管理器"}), 404

        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据不能为空"}), 400

        paths = data.get('paths', [])
        if not paths:
            return jsonify({"error": "文件路径列表不能为空"}), 400

        # 创建内存中的ZIP文件
        memory_file = io.BytesIO()
        with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for path in paths:
                file_path = file_manager.get_file_path(path)
                if file_path and os.path.isfile(file_path):
                    # 使用相对路径作为ZIP内的文件名
                    arcname = os.path.basename(path)
                    zf.write(file_path, arcname)

        memory_file.seek(0)

        # 创建响应
        response = make_response(memory_file.getvalue())
        response.headers['Content-Type'] = 'application/zip'
        response.headers['Content-Disposition'] = f'attachment; filename=files_{project_id}.zip'

        return response

    except Exception as e:
        logging.error(f"批量下载文件失败: {e}")
        return jsonify({"error": str(e)}), 500
