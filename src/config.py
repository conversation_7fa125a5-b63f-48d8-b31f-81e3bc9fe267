import os
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

class Config:
    PROMPT_PATH = os.path.join(os.path.dirname(__file__), "prompt")
    AI_WORK_DIR = os.getenv('AI_WORK_DIR', ".taskai")
    VECTOR_DB_DIR = os.getenv('VECTOR_DB_DIR',".taskai/vector_db")
    DATA_DIR = os.getenv('DATA_DIR', './data')
    
    # API超时设置 (毫秒)
    API_TIMEOUT_MS = os.getenv('API_TIMEOUT_MS', '300000')
    
    # 添加Embedding相关配置
    EMBEDDING_API_KEY = os.getenv('EMBEDDING_API_KEY')
    EMBEDDING_BASE_URL = os.getenv('EMBEDDING_BASE_URL')
    EMBEDDING_MODEL = os.getenv('EMBEDDING_MODEL')
    EMBEDDING_DIMENSION = int(os.getenv('EMBEDDING_DIMENSION', '2048'))

    # 知识库分块配置 (chunk_size 和 chunk_overlap 基于行数)
    MARKDOWN_SEPARATOR = os.getenv('MARKDOWN_SEPARATOR', '###')
    CHUNK_SIZE = int(os.getenv('CHUNK_SIZE', '100'))
    CHUNK_OVERLAP = int(os.getenv('CHUNK_OVERLAP', '10'))
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', '16777216'))  # 默认16MB
    MINERU_API = os.getenv('MINERU_API', 'https://10.0.8.180/mineru/upload')

    # 需求/设计文档自动导入知识库配置
    AUTO_IMPORT_DOCS_TO_KB = os.getenv('AUTO_IMPORT_DOCS_TO_KB', 'false').lower() == 'true'
    
    # 工具权限配置
    ALLOWED_TOOLS = os.getenv('ALLOWED_TOOLS', '').split(',') if os.getenv('ALLOWED_TOOLS') else []
    DISALLOWED_TOOLS = os.getenv('DISALLOWED_TOOLS', '').split(',') if os.getenv('DISALLOWED_TOOLS') else []
    
    # 网关地址
    GATEYWAY_URL = os.getenv('GATEYWAY_URL', '')
    DEBUG_MODE = os.getenv('DEBUG_MODE', 'false').lower() == 'true'
    
    # 动态加载LLM Providers配置
    PROVIDERS = {}
    
    # 遍历所有环境变量，查找以PROVIDER_开头的配置
    for key, value in os.environ.items():
        if key.startswith('PROVIDER_'):
            # 提取provider名称和配置项
            # 格式: PROVIDER_{NAME}_{CONFIG_KEY}
            parts = key.split('_', 2)
            if len(parts) >= 3:
                provider_name = parts[1].lower()
                config_key = parts[2].lower()
                
                if provider_name not in PROVIDERS:
                    PROVIDERS[provider_name] = {}
                    
                PROVIDERS[provider_name][config_key] = value
    
    # 如果没有配置任何provider，则使用默认配置
    if not PROVIDERS:
        PROVIDERS = {
            'local': {
                'name': '内网',
                'base_url': os.getenv('LOCAL_BASE_URL', 'https://ai.secsign.online:3006/'),
                'auth_token': os.getenv('LOCAL_AUTH_TOKEN', 'sk-NO_KEY'),
                'model': os.getenv('LOCAL_MODEL', 'glm-4.5-air'),
                'api_key': os.getenv('LOCAL_API_KEY', ''),
                'max_tokens': os.getenv('LOCAL_MAX_TOKENS', '15000')
            }
        }