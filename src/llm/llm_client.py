"""
大模型客户端
"""

import json
import re
import asyncio
import time
from typing import List, Dict, Any, Optional, Union, Callable
import openai
import logging
import httpx
import traceback
try:
    from ..config import Config
except ImportError:
    from config import Config



class LLMClient:
    """大模型客户端"""

    def __init__(self):
        # 修复解包错误：Config.PROVIDERS是字典，不是元组列表
        for provider_name, provider_config in Config.PROVIDERS.items():
            # 检查api_type是否为openai
            if provider_config.get('api_type', '').lower() == 'openai':
                self.base_url = provider_config.get('base_url')
                self.api_key = provider_config.get('api_key')
                self.model = provider_config.get('model')
                # 检查是否需要禁用SSL验证
                self.verify_ssl = provider_config.get('verify_ssl', False)
                break
        self._initialize_client()

    def _initialize_client(self):
        """初始化大模型客户端"""
        try:
            if self.base_url:
                client_kwargs = {
                    "api_key": self.api_key,
                    "timeout": 50,
                    "base_url": self.base_url.rstrip("/") + "/",
                }
                
                # 如果需要禁用SSL验证，则创建自定义http_client
                if not self.verify_ssl:
                    http_client = httpx.AsyncClient(verify=False)
                    client_kwargs["http_client"] = http_client
                
                self.client = openai.AsyncOpenAI(**client_kwargs)
                logging.info("已初始化OpenAI大模型客户端")
            else:
                raise ValueError(f"没有openai兼容的大模型提供商")

        except Exception as e:
            logging.error(f"初始化大模型客户端失败: {e} , 错误详情:\n{traceback.format_exc()}")
            raise

    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
        is_stream: bool = True,
        stream_callback: Optional[Callable] = None,
    ) -> Dict[str, Any]:
        """
        聊天完成

        Args:
            messages: 消息列表
            tools: 工具列表
            tool_choice: 工具选择策略
            stream_callback: 流式回调函数，用于实时处理流式数据

        Returns:
            Dict: 响应结果
        """
        try:            
            # 处理messages参数，确保可以被JSON序列化
            processed_messages = []
            for msg in messages:
                # 如果msg是Message对象，调用to_dict()方法
                if hasattr(msg, 'to_dict'):
                    processed_messages.append(msg.to_dict())
                # 如果msg是字典，直接添加
                elif isinstance(msg, dict):
                    processed_messages.append(msg)
                # 其他情况，转换为字符串再包装成字典
                else:
                    processed_messages.append({"role": "user", "content": str(msg)})

            kwargs = {
                "model": self.model,
                "messages": processed_messages,
                "temperature": 0.5,
                "max_tokens": 1000,
                "stream": is_stream,
            }

            # 添加工具相关参数
            if tools:
                kwargs["tools"] = tools
                if tool_choice:
                    kwargs["tool_choice"] = tool_choice

            # response = openai.chat.completions.create(**kwargs)
            response = await self.client.chat.completions.create(**kwargs)
            tool_calls = []
            if is_stream:
                content = await self.process_stream_response(
                    response, tool_calls, stream_callback
                )
            else:
                # 非流式响应
                resp_msg = response.choices[0].message
                content = resp_msg.content

                if resp_msg.tool_calls:
                    tool_calls = [
                        {
                            "id": tc.id,
                            "function": {
                                "name": tc.function.name,
                                "arguments": (
                                    json.loads(tc.function.arguments)
                                    if tc.function.arguments
                                    else ""
                                ),
                            },
                            "type": "function",
                        }
                        for tc in resp_msg.tool_calls
                    ]

            # 转换tool_calls的格式为openai兼容
            openai_tools = []
            for tc in tool_calls:
                function_data = tc.get("function")
                if function_data and function_data.get("name"):
                    openai_tools.append(tc)
                else:
                    openai_tools.append(
                        {
                            "id": tc.get("id", f"call_{int(time.time() * 1000)}"),
                            "type": "function",
                            "function": {
                                "name": tc.get("name"),
                                "arguments": f'{tc.get("arguments")}',
                            },
                        }
                    )

            content = self.remove_think(content)
            # 转换响应格式
            result = {
                # response id或者自动生成
                "id": f"{int(time.time() * 1000 )}",
                "content": content,
                "tool_calls": openai_tools,
                # "model": response.model,
                # "choices": []
            }

            # logging.debug(f"大模型响应: {choice.finish_reason}")
            return result

        except Exception as e:
            logging.error(f"大模型调用失败: {str(e)} , 错误详情:\n{traceback.format_exc()}")
            return {"error": str(e), "content": []}

    async def process_stream_response(
        self,
        response,
        tool_calls: List[Any],
        stream_callback: Optional[Callable] = None,
    ):
        # 流式响应处理
        content = ""
        stream_tool = None
        tool_args = ""
        async for chunk in response:
            # 检查choices是否存在且不为空
            if not chunk.choices or not chunk.choices[0].delta:
                continue
                
            if chunk.choices[0].delta.content:
                chunk_content = chunk.choices[0].delta.content
                # print(chunk_content)
                content += chunk_content
                # 如果有回调函数，实时调用
                if stream_callback:
                    try:
                        # 检查stream_callback是否为异步生成器
                        callback_result = stream_callback(chunk_content)
                        if hasattr(callback_result, "__aiter__"):
                            # 如果是异步生成器，需要异步迭代消费它
                            async for _ in callback_result:
                                pass
                        elif asyncio.iscoroutine(callback_result):
                            # 如果是协程，等待它完成
                            await callback_result
                    except Exception as e:
                        logging.warning(f"流式回调函数执行失败: {e} , 错误详情:\n{traceback.format_exc()}")
            # 检查choices是否存在且不为空
            elif chunk.choices and chunk.choices[0].delta.tool_calls:
                # 流式工具调用的输出
                for tc in chunk.choices[0].delta.tool_calls:
                    # print(f"**::{tc}")
                    if tc.function and tc.function.name:
                        # 新工具名称的开始
                        if stream_tool:
                            # 保存原来的stream_tool
                            stream_tool = self.parse_stream_tool(stream_tool, tool_args)
                            tool_calls.append(stream_tool)
                            stream_tool = None
                            tool_args = ""

                        stream_tool = {
                            "id": tc.id,
                            "function": {"name": tc.function.name, "arguments": None},
                            "type": "function",
                        }
                        tool_args = (
                            tc.function.arguments
                            if tc.function and tc.function.arguments
                            else ""
                        )

                    elif tc.function and tc.function.arguments:
                        tool_args += tc.function.arguments
        
        if stream_callback:
            stream_callback("<end></end>")
        if stream_tool:
            stream_tool = self.parse_stream_tool(stream_tool, tool_args)
            tool_calls.append(stream_tool)

        # llm_response.response_time = time.time() - start_time

        if not tool_calls:
            # 解析所有工具调用
            try:
                content = self._parse_tool_call(content, tool_calls, "tool_call")
                content = self._parse_json_tool_call(content, tool_calls)
            except json.JSONDecodeError as e:
                logging.error(f"解析工具调用JSON失败: {content} , 错误详情:\n{traceback.format_exc()}")
                content = f"解析工具调用JSON失败: {content}"

        return content

    def parse_stream_tool(self, stream_tool, tool_args):
        if stream_tool:
            if tool_args:
                try:
                    stream_tool["function"]["arguments"] = json.loads(tool_args.strip())
                except json.JSONDecodeError as e:
                    logging.warning(f"json解析工具调用参数失败: {tool_args}")
                    stream_tool["function"]["arguments"] = tool_args
        return stream_tool

    def remove_think(self, text):
        """
        移除字符串中所有<ai>和</ai>标签及其之间的内容。

        参数:
            text (str): 输入的字符串

        返回:
            str: 处理后的字符串
        """
        pattern = r"<think>.*?</think>"
        while True:
            new_text = re.sub(pattern, "", text, flags=re.DOTALL)
            if new_text == text:
                break
            text = new_text
        if all(c in ("\n", " ") for c in text):
            text = text.replace("\n", "").replace(" ", "")
        return text

    def _parse_tool_call(
        self, content: str, tool_calls: List[Any], pattern="tool_call"
    ) -> str:
        call_pattern = f"<{pattern}>(.*?)</{pattern}>"
        tool_calls_matches = re.findall(call_pattern, content, re.DOTALL)
        for match in tool_calls_matches:
            # 逐个解析JSON并合并
            parsed = json.loads(match.strip())
            if isinstance(parsed, list):
                tool_calls.extend(parsed)
            else:
                tool_calls.append(parsed)
            content = content.replace(f"<{pattern}>{match}</{pattern}>", "")
        return content

    def _parse_json_tool_call(self, content: str, tool_calls: List[Any]) -> str:
        # "\n\n{'tool_calls': [{'name': 'search_code', 'arguments': {'query': 'RedisConnection::run', 'top_k': 5}, 'id': 'call_3'}]}"
        # 检查如果包含tool_calls，则尝试解析为JSON工具调用
        if "tool_calls" in content:
            try:
                json_start = content.find("{")
                json_end = content.rfind("}") + 1
                if json_start == -1 or json_end == 0:
                    return content  # 未找到有效的 { 或 }
                json_content = content[json_start:json_end]
                # 单引号替换为双引号
                json_content = json_content.replace("'", '"')
                parsed = json.loads(json_content)
                json_tools = parsed.get("tool_calls")
                if json_tools is not None:
                    if isinstance(json_tools, list):
                        tool_calls.extend(json_tools)
                    else:
                        tool_calls.append(json_tools)
                    return content[:json_start]
            except (json.JSONDecodeError, Exception) as e:
                logging.warning(f"解析内联JSON tool_calls失败: {e}")
        return content
