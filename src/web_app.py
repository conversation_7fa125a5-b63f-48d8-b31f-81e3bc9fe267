#!/usr/bin/env python3
"""
AI全栈助手系统Web界面
"""

import os
import sys
import json
import threading
import logging
import time

from datetime import datetime
from flask import (
    Flask,
    request,
    jsonify,
    redirect,
    url_for,
    flash,
    send_file,
    send_from_directory,
    make_response,
    session,
    Response,
)
from werkzeug.utils import secure_filename

try:
    from .project_manager import ProjectManager,init_task_manager
    from .task_manager import TaskManager
    from .log_manager import TaskLogManager
    from .file_manager import FileManager
    from .knowledge_manager import KnowledgeManager
    from .config import Config
    from .knowledge_ui import knowledge_bp, init_knowledge_app
    from .document_ui import document_bp, init_document_app
    from .file_manager_ui import file_manager_bp, init_file_manager_app
    from .project_app import project_app_bp, init_project_app
    from .auth_manager import auth_manager, login_required
except ImportError:
    from project_manager import ProjectManager,init_task_manager
    from task_manager import TaskManager
    from log_manager import TaskLogManager
    from file_manager import FileManager
    from knowledge_manager import KnowledgeManager
    from config import Config
    from knowledge_ui import knowledge_bp, init_knowledge_app
    from document_ui import document_bp, init_document_app
    from file_manager_ui import file_manager_bp, init_file_manager_app
    from project_app import project_app_bp, init_project_app
    from auth_manager import auth_manager, login_required

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 构建模板和静态文件目录路径
template_dir = os.path.join(current_dir, "..", "templates")
static_dir = os.path.join(current_dir, "..", "static")

# 实例化Flask应用并指定模板和静态文件目录
app = Flask(__name__, template_folder=template_dir, static_folder=static_dir)
app.secret_key = "ai-task-manager-secret-key-2025"

# 全局变量
project_manager = None
g_app_port = None
g_xterm_port = None
running_tasks = {}  # 存储正在运行的任务
task_threads = {}  # 存储任务线程

# 注册知识库蓝图
app.register_blueprint(knowledge_bp)
# 注册文档管理蓝图
app.register_blueprint(document_bp)
# 注册文件管理蓝图
app.register_blueprint(file_manager_bp)
# 注册项目应用蓝图
app.register_blueprint(project_app_bp)

# ==================== 认证相关API ====================


@app.route("/aicode/api/auth/login", methods=["POST"])
def api_login():
    """用户登录API"""
    data = request.get_json()
    username = data.get("username", "").strip()
    password = data.get("password", "")

    if not username or not password:
        return jsonify({"success": False, "message": "请输入用户名和密码"}), 400

    success, message, session_id = auth_manager.authenticate(username, password)

    if success:
        # 设置cookie
        resp = make_response(jsonify({"success": True, "message": message}))
        # 设置会话ID cookie，30天过期
        resp.set_cookie(
            "session_id", session_id, max_age=30 * 24 * 60 * 60, httponly=True
        )
        session["session_id"] = session_id
        return resp
    else:
        return jsonify({"success": False, "message": message}), 401


@app.route("/aicode/api/auth/logout", methods=["POST"])
def api_logout():
    """用户登出API"""
    session_id = request.cookies.get("session_id") or session.get("session_id")

    if session_id:
        auth_manager.logout(session_id)

    resp = make_response(jsonify({"success": True, "message": "已退出登录"}))
    resp.set_cookie("session_id", "", expires=0)
    session.pop("session_id", None)
    return resp


@app.route("/aicode/api/auth/current_user", methods=["GET"])
def api_current_user():
    """获取当前用户信息"""
    session_id = request.cookies.get("session_id") or session.get("session_id")
    user_info = auth_manager.get_current_user(session_id)
    return jsonify(user_info)


@app.route("/aicode/api/auth/change_password", methods=["POST"])
@login_required
def api_change_password():
    """修改密码API"""
    data = request.get_json()
    old_password = data.get("old_password", "")
    new_password = data.get("new_password", "")

    if not old_password or not new_password:
        return jsonify({"success": False, "message": "请输入旧密码和新密码"}), 400

    success, message = auth_manager.change_password(old_password, new_password)

    if success:
        # 清除cookie
        resp = make_response(jsonify({"success": True, "message": message}))
        resp.set_cookie("session_id", "", expires=0)
        session.pop("session_id", None)
        return resp
    else:
        return jsonify({"success": False, "message": message}), 400


def init_app(app_port, xterm_port):
    """初始化应用"""
    global project_manager, g_app_port, g_xterm_port
    project_manager = ProjectManager()
    g_xterm_port = xterm_port
    g_app_port = app_port
    init_knowledge_app(project_manager, app)
    # 设置文档管理器
    init_document_app(project_manager, app)
    # 设置文件管理器
    init_file_manager_app(project_manager, app)
    # 设置项目应用管理器
    init_project_app(project_manager)    
    # 初始化任务管理器，加载所有定时任务
    init_task_manager(project_manager)

    # 创建模板目录
    template_dir = os.path.join(os.path.dirname(__file__), "..", "templates")
    static_dir = os.path.join(os.path.dirname(__file__), "..", "static")
    os.makedirs(template_dir, exist_ok=True)
    os.makedirs(static_dir, exist_ok=True)
    return app


# 静态文件路由 - 提供HTML页面
@app.route("/")
@login_required
def index():
    """首页"""
    return redirect("/aicode/")


@app.route("/aicode/")
@login_required
def aicode_index():
    """aicode前缀首页"""
    return app.send_static_file("index.html")


@app.route("/aicode/projects.html")
@login_required
def projects_html():
    """项目列表HTML"""
    return app.send_static_file("projects.html")


@app.route("/aicode/project_tasks.html")
@login_required
def project_tasks_html():
    """项目任务HTML"""
    return app.send_static_file("project_tasks.html")


@app.route("/aicode/task_llm_logs.html")
@login_required
def task_llm_logs_html():
    """任务LLM日志HTML"""
    return app.send_static_file("task_llm_logs.html")


@app.route("/aicode/config_manager.html")
@login_required
def config_manager_html():
    """配置管理HTML"""
    return app.send_static_file("config_manager.html")


@app.route("/aicode/login.html")
def login_html():
    """登录页面"""
    return app.send_static_file("login.html")


# 通用HTML文件路由 - 处理所有未明确定义的.html请求
@app.route("/aicode/<path:filename>.html")
@login_required
def html_files(filename):
    """通用HTML文件路由"""
    # 登录页面不需要保护
    if filename == "login":
        return app.send_static_file("login.html")
    return app.send_static_file(f"{filename}.html")


# ---------draw io 相关的资源映射 START--------
@app.route("/aicode/assets/<path:filename>")
def aicode_draw_assets_files(filename):
    """提供带aicode前缀的静态资源文件"""
    return send_from_directory(os.path.join(static_dir, "assets"), filename)


@app.route("/aicode/static/<path:filename>")
def aicode_static_files(filename):
    """提供带aicode前缀的静态资源文件"""
    return send_from_directory(static_dir, filename)


# ---------draw io 相关的资源映射 END--------
@app.route("/aicode/api/config/providers", methods=["GET"])
@login_required
def api_get_providers():
    """获取LLM Providers配置"""
    try:
        # 获取查询参数中的api_type
        api_type = request.args.get("api_type")

        providers_data = []
        for key, value in Config.PROVIDERS.items():
            # 如果指定了api_type，则只返回匹配的providers
            if api_type:
                provider_api_type = value.get("api_type", "").lower()
                if provider_api_type != api_type.lower():
                    continue

            providers_data.append(
                {
                    "key": key,
                    "name": value["name"],
                    "api_type": value.get("api_type", "unknown"),
                }
            )
        return jsonify(providers_data)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/aicode/api/config/data-dir", methods=["GET"])
@login_required
def api_get_data_dir():
    """获取数据目录配置"""
    return jsonify({"data_dir": Config.DATA_DIR})


@app.route("/aicode/api/config/all", methods=["GET"])
@login_required
def api_get_all_config():
    """获取所有配置"""
    try:
        # 读取.env文件内容
        env_file_path = os.path.join(os.path.dirname(__file__), "..", ".env")
        config_data = {}

        if os.path.exists(env_file_path):
            with open(env_file_path, "r", encoding="utf-8") as f:
                for line in f:
                    line = line.strip()
                    # 跳过空行和注释行
                    if not line or line.startswith("#"):
                        continue

                    # 解析键值对
                    if "=" in line:
                        key, value = line.split("=", 1)
                        config_data[key] = value

        return jsonify(config_data)
    except Exception as e:
        logging.error(f"读取配置文件失败: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/aicode/api/config/save", methods=["POST"])
@login_required
def api_save_config():
    """保存配置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "无效的配置数据"}), 400

        # 写入.env文件
        env_file_path = os.path.join(os.path.dirname(__file__), "..", ".env")

        # 备份原文件
        if os.path.exists(env_file_path):
            backup_path = env_file_path + ".backup"
            import shutil

            shutil.copy2(env_file_path, backup_path)

        # 按类别组织配置
        provider_configs = {}  # PROVIDER_* 配置
        other_configs = {}  # 其他配置

        for key, value in data.items():
            if key.startswith("PROVIDER_"):
                provider_configs[key] = value
            else:
                other_configs[key] = value

        # 读取原文件内容以保留注释和结构
        original_lines = []
        if os.path.exists(env_file_path):
            with open(env_file_path, "r", encoding="utf-8") as f:
                original_lines = f.readlines()

        # 生成新文件内容
        new_lines = []

        # 保留文件头部的注释
        in_provider_section = False
        for line in original_lines:
            stripped_line = line.strip()
            if not stripped_line or stripped_line.startswith("#"):
                # 保留注释和空行
                new_lines.append(line)
            elif stripped_line.startswith("PROVIDER_"):
                # 遇到第一个provider配置，标记进入provider区域
                in_provider_section = True
                break
            else:
                # 其他配置之前的注释
                new_lines.append(line)

        # 添加provider配置，按provider分组
        if provider_configs:
            # 提取所有provider名称
            provider_names = set()
            for key in provider_configs.keys():
                parts = key.split("_")
                if len(parts) >= 2:
                    provider_names.add(parts[1])

            # 按provider名称排序并添加配置
            for provider_name in sorted(provider_names):
                # 添加空行分隔（如果不是文件开头）
                if new_lines and new_lines[-1].strip() != "":
                    new_lines.append("\n")

                # 添加该provider的所有配置项
                provider_keys = [
                    k
                    for k in provider_configs.keys()
                    if k.startswith(f"PROVIDER_{provider_name}_")
                ]
                for key in sorted(provider_keys):  # 按配置项名称排序
                    new_lines.append(f"{key}={provider_configs[key]}\n")

        # 添加其他配置
        if other_configs:
            # 添加分隔行（如果已有内容）
            if new_lines and new_lines[-1].strip() != "":
                new_lines.append("\n")

            # 添加其他配置项
            for key in sorted(other_configs.keys()):
                new_lines.append(f"{key}={other_configs[key]}\n")

        # 如果原文件中有一些我们没有处理的尾部注释，尝试保留
        if original_lines:
            last_provider_line_index = -1
            for i in range(len(original_lines) - 1, -1, -1):
                line = original_lines[i].strip()
                if line.startswith("PROVIDER_"):
                    last_provider_line_index = i
                    break

            # 如果找到了provider配置，检查后面是否有注释需要保留
            if last_provider_line_index >= 0:
                for i in range(last_provider_line_index + 1, len(original_lines)):
                    line = original_lines[i]
                    stripped_line = line.strip()
                    if stripped_line and not stripped_line.startswith("#"):
                        # 遇到非注释的配置行，说明是其他配置，这些我们已经处理过了
                        break

        # 写入文件
        with open(env_file_path, "w", encoding="utf-8", newline="\n") as f:
            f.writelines(new_lines)

        return jsonify({"success": True, "message": "配置保存成功"})
    except Exception as e:
        logging.error(f"保存配置文件失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500


# API路由
@app.route("/aicode/api/projects", methods=["GET", "POST"])
@login_required
def api_projects():
    """项目API"""
    if request.method == "GET":
        projects = project_manager.list_projects()
        # xterm_url
        # 获取请求路径的主机名和端口号
        port = "80"
        if ":" in request.host:
            host, port = request.host.split(":")
        else:
            host = request.host
        if int(port) != int(g_app_port):
            # 如果请求路径端口号与app启动的端口不一致，说明是通过代理转发的
            # 则终端链接也使用代理的端口
            # 注：需要与代理转发逻辑一致
            xterm_url = f"/aiterm/xterm.html"
        else:
            xterm_url = f"http://{host}:{g_xterm_port}/aiterm/xterm.html"

        # 加载INDEX_TITLE
        index_title = os.getenv("INDEX_TITLE", "AI全栈助手系统")

        # 获取当前运行的Agent数量
        try:
            from claude.claude_agent import get_running_agents_count

            running_agents_count = get_running_agents_count()
        except ImportError:
            # 兼容不同导入路径
            try:
                from .claude.claude_agent import get_running_agents_count

                running_agents_count = get_running_agents_count()
            except ImportError:
                running_agents_count = 0

        # 修改返回结构
        projects_data = [
            project.to_dict(include_file=False, extra_fields={"xterm_url": xterm_url})
            for project in projects
        ]
        return jsonify(
            {
                "index_title": index_title,
                "projects": projects_data,
                "running_agents_count": running_agents_count,
            }
        )

    elif request.method == "POST":
        data = request.get_json()
        try:
            # 验证必填字段
            name = data.get("name", "").strip()
            work_dir = data.get("work_dir", "").strip()

            if not name or not work_dir:
                return (
                    jsonify(
                        {"success": False, "message": "项目名称和工作目录不能为空"}
                    ),
                    400,
                )

            project_id = project_manager.create_project(
                name=name,
                work_dir=work_dir,
                description=data.get("description", ""),
                requirement=data.get("requirement", ""),
                provider=data.get("provider", "local"),
                project_type=data.get("project_type", "新产品"),
                rules_constraint=data.get("rules_constraint", ""),
                design=data.get("design", ""),
                git_managed=data.get("git_managed", 0),
                git_url=data.get("git_url", ""),
                git_access_token=data.get("git_access_token", ""),
                git_branch=data.get("git_branch", ""),
                exclude_patterns=data.get("exclude_patterns", ""),
                include_patterns=data.get("include_patterns", ""),
            )
            return jsonify({"success": True, "project_id": project_id})
        except Exception as e:
            return jsonify({"success": False, "message": str(e)}), 400


@app.route("/aicode/api/projects/<project_id>", methods=["GET", "PUT", "DELETE"])
@login_required
def api_project(project_id):
    """单个项目API"""
    if request.method == "GET":
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"error": "项目不存在"}), 404
        task_manager = project_manager.get_task_manager(project_id)
        if task_manager:
            project.run_state = task_manager.get_run_state()

        return jsonify(project.to_dict())

    elif request.method == "PUT":
        data = request.get_json()
        success = project_manager.update_project(
            project_id,
            name=data.get("name"),
            description=data.get("description"),
            work_dir=data.get("work_dir"),
            requirement=data.get("requirement"),
            provider=data.get("provider"),
            rules_constraint=data.get("rules_constraint"),
            project_type=data.get("project_type"),
            design=data.get("design"),
            git_managed=data.get("git_managed"),
            exclude_patterns=data.get("exclude_patterns"),
            include_patterns=data.get("include_patterns"),
        )
        if success:
            return jsonify({"success": True})
        else:
            return jsonify({"success": False, "message": "项目不存在"}), 404

    elif request.method == "DELETE":
        success = project_manager.delete_project(project_id, delete_files=False)
        if success:
            return jsonify({"success": True})
        else:
            return jsonify({"success": False, "message": "项目不存在"}), 404


@app.route("/aicode/api/projects/<project_id>/rules", methods=["PUT"])
@login_required
def api_project_rules(project_id):
    """规则保存"""
    if request.method == "PUT":
        data = request.get_json()
        rules_constraint = data.get("rules_constraint", "")
        success = project_manager.update_project(
            project_id, rules_constraint=rules_constraint
        )
        if success:
            return jsonify({"success": True})
        else:
            return jsonify({"success": False, "message": "项目不存在"}), 404


@app.route("/aicode/api/projects/<project_id>/summary", methods=["GET"])
@login_required
def api_project_summary(project_id):
    """获取项目摘要信息API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    try:
        summary = project_manager.get_project_summary(project_id)
        return jsonify(summary)
    except Exception as e:
        return jsonify({"error": f"获取项目摘要失败: {e}"}), 500


@app.route("/aicode/api/projects/<project_id>/generate_tasks", methods=["POST"])
@login_required
def api_generate_project_tasks(project_id):
    """为项目生成任务"""
    try:
        data = request.get_json() or {}
        num_tasks = data.get("num_tasks")
        doc_type = data.get("doc_type")
        special_instruction = data.get("special_instruction")
        requirement = data.get("requirement")
        mode = data.get("mode", "override")  # 获取任务生成模式，默认为覆盖模式
        async_thread = data.get("async_thread", False)  # 是否异步执行
        keep_session = data.get("keep_session", True)  # 是否保持会话，默认为True

        result = project_manager.generate_tasks_for_project(
            project_id,
            num_tasks,
            doc_type,
            special_instruction,
            requirement,
            mode,
            async_thread,
            keep_session,
        )
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/aicode/api/projects/<project_id>/run_tasks", methods=["POST"])
@login_required
def api_run_project_tasks(project_id):
    """运行项目任务"""
    try:
        data = request.get_json() or {}
        parallel_mode = data.get("parallel_mode", False)
        async_thread = data.get("async_thread", False)  # 是否异步执行

        result = project_manager.run_project_tasks(
            project_id, parallel_mode, async_thread
        )
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/aicode/api/projects/<project_id>/import_to_kb", methods=["POST"])
@login_required
def api_import_to_kb(project_id):
    """导入文档到知识库"""
    try:
        data = request.get_json() or {}
        doc_type = data.get("doc_type")  # 'requirement' 或 'design'

        if not doc_type or doc_type not in ["requirement", "design"]:
            return jsonify({"success": False, "message": "无效的文档类型"}), 400

        # 获取项目
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"success": False, "message": "项目不存在"}), 404

        # 获取知识库管理器
        knowledge_manager = project_manager.get_knowledge_manager(project_id)
        if not knowledge_manager:
            return jsonify({"success": False, "message": "知识库管理器未初始化"}), 500

        # 根据文档类型调用相应的保存方法
        if doc_type == "requirement":
            project.import_requirement_to_kb(knowledge_manager)
        elif doc_type == "design":
            project.import_design_to_kb(knowledge_manager)

        return jsonify(
            {"success": True, "message": f"{doc_type}文档已成功导入到知识库"}
        )
    except Exception as e:
        logging.error(f"导入文档到知识库失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/aicode/api/projects/<project_id>/tasks", methods=["GET", "POST", "DELETE"])
@login_required
def api_project_tasks(project_id):
    """获取项目任务列表API（支持分页和搜索）"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"success": False, "message": "项目不存在"}), 404
    task_manager = project_manager.get_task_manager(project_id)
    if request.method == "GET":
        # 获取查询参数
        title_search = request.args.get("title", "").strip()
        status_filter = request.args.get("status", "").strip()
        page = request.args.get("page", 1, type=int)
        page_size = request.args.get("page_size", 10, type=int)

        # 限制每页最大数量
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 10

        # 获取任务信息
        all_tasks = []
        if task_manager:
            try:
                all_tasks = task_manager.list_tasks()
            except Exception as e:
                return jsonify({"success": False, "message": f"加载任务失败: {e}"}), 500

        # 应用搜索和过滤
        filtered_tasks = []
        for task in all_tasks:
            # 标题搜索
            if title_search:
                task_title = (task.get("title") or task.get("name") or "").lower()
                if title_search.lower() not in task_title:
                    continue

            # 状态过滤
            if status_filter:
                if task.get("status") != status_filter:
                    continue

            filtered_tasks.append(task)

        # 计算分页
        total_count = len(filtered_tasks)
        total_pages = (total_count + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size

        # 获取当前页任务
        paginated_tasks = filtered_tasks[start_idx:end_idx]

        return jsonify(
            {
                "tasks": paginated_tasks,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1,
                },
            }
        )
    elif request.method == "POST":
        data = request.get_json()
        try:
            result = project_manager.add_task_to_project(
                project_id,
                title=data.get("title"),
                description=data.get("description", ""),
                dependencies=data.get("dependencies", []),
                test_strategy=data.get("testStrategy", ""),
                keep_session=data.get("keep_session", False),
                stype=data.get("stype"),
                schedule=data.get("schedule"),
            )

            # 如果需要立即执行
            if result.get("success") and data.get("execute_immediately"):
                task_id = result["task_id"]
                async_thread = data.get("async_thread", False)  # 是否异步执行
                run_result = project_manager.run_single_task(
                    project_id, task_id, async_thread
                )
                result["execution_result"] = run_result

            return jsonify(result)
        except Exception as e:
            return jsonify({"error": f"添加任务失败: {e}"}), 500
    elif request.method == "DELETE":
        result = project_manager.delete_all_tasks(project_id)
        return jsonify(result)


@app.route(
    "/aicode/api/projects/<project_id>/tasks/<task_id>",
    methods=["GET", "PUT", "DELETE"],
)
@login_required
def api_project_task(project_id, task_id):
    """项目任务API - 获取、更新或删除任务"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    task_manager = project_manager.get_task_manager(project_id)
    if not task_manager:
        return jsonify({"error": "无法获取TaskManager"}), 404

    try:
        if request.method == "GET":
            # 获取单个任务信息
            task = task_manager.get_task(int(task_id))
            if task:
                return jsonify({"success": True, "task": task.to_dict()})
            else:
                return jsonify({"error": "任务不存在"}), 404

        elif request.method == "PUT":
            # 更新任务
            data = request.get_json()
            result = project_manager.update_project_task(
                project_id,
                task_id,
                title=data.get("title"),
                description=data.get("description", ""),
                testStrategy=data.get("testStrategy", ""),
                dependencies=data.get("dependencies", []),
                keep_session=data.get("keep_session"),
                schedule=data.get("schedule", None)
            )
            return jsonify(result)

        elif request.method == "DELETE":
            # 删除任务
            result = project_manager.delete_task(project_id, int(task_id))
            return jsonify(result)

    except Exception as e:
        if request.method == "GET":
            return jsonify({"error": f"获取任务失败: {e}"}), 500
        elif request.method == "PUT":
            return jsonify({"error": f"更新任务失败: {e}"}), 500
        else:
            return jsonify({"error": f"删除任务失败: {e}"}), 500


@app.route("/aicode/api/projects/<project_id>/tasks/<task_id>/logs", methods=["GET"])
@login_required
def api_get_task_logs(project_id, task_id):
    """获取任务日志API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    try:
        logs = project_manager.get_task_logs(project_id, task_id)
        return jsonify({"logs": logs})
    except Exception as e:
        return jsonify({"error": f"获取日志失败: {e}"}), 500


@app.route(
    "/aicode/api/projects/<project_id>/tasks/<task_id>/llm-logs", methods=["GET"]
)
@login_required
def api_get_task_llm_logs(project_id, task_id):
    """获取任务LLM交互日志API（支持分页和增量获取）"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    try:
        from src.log_manager import get_task_llm_logs

        # 分页参数
        limit = request.args.get("limit", 50, type=int)
        offset = request.args.get("offset", 0, type=int)
        since_timestamp = request.args.get("since")  # 获取指定时间戳之后的日志
        since_sequence = request.args.get(
            "since_sequence", type=int
        )  # 获取指定序号之后的日志（增量获取）

        # 获取所有日志
        all_logs = get_task_llm_logs(project, task_id, limit=None)

        # 为日志添加序号
        total_logs = len(all_logs)
        for i, log in enumerate(all_logs):
            # 序号从1开始计算
            log["sequence_number"] = i + 1

        # 处理序号过滤（增量获取）- 优先级高于时间戳过滤
        if since_sequence is not None:
            # 只返回序号大于since_sequence的日志
            filtered_logs = []
            for log in all_logs:
                if log["sequence_number"] > since_sequence:
                    filtered_logs.append(log)
            all_logs = filtered_logs
        # 处理时间戳过滤
        elif since_timestamp:
            filtered_logs = []
            for log in all_logs:
                if log["timestamp"] > since_timestamp:
                    filtered_logs.append(log)
            all_logs = filtered_logs

        # 如果是增量获取模式（since_sequence参数存在），直接返回所有新日志
        if since_sequence is not None:
            return jsonify(
                {
                    "success": True,
                    "logs": all_logs,  # 增量日志已经是从旧到新的顺序
                    "total_count": len(all_logs),
                    "has_more": False,  # 增量模式不需要分页
                }
            )

        # 处理分页（从最新到最旧，所以需要反转）
        all_logs.reverse()  # 变为最新的在前

        total_count = len(all_logs)
        logs = all_logs[offset : offset + limit]

        # 将当前页的日志重新反转，以确保返回的一页数据是从旧到新排列
        logs.reverse()

        # 判断是否还有更多数据
        has_more = offset + limit < total_count

        return jsonify(
            {
                "success": True,
                "logs": logs,
                "total_count": total_count,
                "has_more": has_more,
                "offset": offset,
                "limit": limit,
            }
        )
    except Exception as e:
        logging.error(f"获取LLM日志失败: {e}")
        return jsonify({"error": f"获取LLM日志失败: {e}"}), 500


@app.route("/aicode/api/projects/<project_id>/tasks/<task_id>/chat", methods=["POST"])
@login_required
def api_task_chat(project_id, task_id):
    """任务聊天API - 流式输出"""
    try:
        data = request.get_json()
        logging.info(
            f"收到聊天请求: project_id={project_id}, task_id={task_id}, 请求数据: {data}"
        )

        project = project_manager.get_project(project_id)
        if not project:
            logging.error(f"项目不存在: {project_id}")
            return jsonify({"error": "项目不存在"}), 404

        if not data or "message" not in data:
            return jsonify({"error": "缺少消息内容"}), 400

        message = data["message"].strip()
        if not message:
            return jsonify({"error": "消息内容不能为空"}), 400

        # 获取任务管理器
        task_manager = project_manager.get_task_manager(project_id)
        if not task_manager:
            logging.error("无法获取TaskManager")
            return jsonify({"error": "无法获取TaskManager"}), 500

        # 如果task_id == -1，则检查project是否有这个Task，没有则添加
        if int(task_id) == -1:
            # 检查是否已经存在task_id为-1的任务
            task = task_manager.get_task(-1)
            if not task:
                # 添加新任务
                task_manager.add_task(
                    title="临时对话任务",
                    description="用于临时对话的虚拟任务",
                    priority="medium",
                    stype="chat",
                    status="disable",
                    task_id=-1,
                )
            task = task_manager.get_task(-1)
            if not task.session_id:
                task.session_id = project.session_id

    except Exception as e:
        logging.error(f"处理聊天请求失败: {e}")
        return jsonify({"error": f"处理请求失败: {str(e)}"}), 500

    def generate_response():
        """生成流式响应"""
        try:
            # 使用队列存储进度数据
            import queue
            import threading

            progress_queue = queue.Queue()

            # 创建进度回调函数
            def chat_callback(progress: int, title: str, content: str = ""):
                try:
                    data_str = json.dumps(
                        {
                            "type": "progress",
                            "progress": progress,
                            "title": title,
                            "content": content,
                            "timestamp": datetime.now().isoformat(),
                        },
                        ensure_ascii=False,
                    )
                    progress_queue.put(data_str)
                    # print(f"推送进度数据: {data_str}")
                except Exception as e:
                    logging.error(f"创建进度数据失败: {e}", exc_info=True)

            # 在单独的线程中运行任务
            result_container = {}
            exception_container = {}

            def run_task():
                try:
                    result = task_manager.run_chat(
                        int(task_id), message, progress_callback=chat_callback
                    )
                    result_container["result"] = result
                except Exception as e:
                    exception_container["exception"] = e

            task_thread = threading.Thread(target=run_task)
            task_thread.start()

            # 在等待任务完成的同时，持续发送进度更新
            while task_thread.is_alive():
                try:
                    # 非阻塞地从队列中获取数据
                    data_str = progress_queue.get_nowait()
                    yield f"data: {data_str}\n\n"
                except queue.Empty:
                    # 如果队列为空，短暂休眠以避免过度占用CPU
                    import time

                    time.sleep(0.1)

            # 等待线程结束
            task_thread.join()

            # 处理剩余的进度数据
            while not progress_queue.empty():
                try:
                    data_str = progress_queue.get_nowait()
                    yield f"data: {data_str}\n\n"
                except queue.Empty:
                    break

            # 检查是否有异常
            if "exception" in exception_container:
                raise exception_container["exception"]

            # 获取结果
            result = result_container.get("result", {})

            if result.get("success"):
                # 发送最终响应
                yield f"data: {json.dumps({'type': 'complete', 'response': '', 'timestamp': datetime.now().isoformat()}, ensure_ascii=False)}\n\n"
            else:
                error_msg = result.get("message", "聊天失败")
                yield f"data: {json.dumps({'type': 'error', 'error': error_msg, 'timestamp': datetime.now().isoformat()}, ensure_ascii=False)}\n\n"

        except Exception as e:
            logging.error(f"流式聊天生成失败: {e}", exc_info=True)
            yield f"data: {json.dumps({'type': 'error', 'error': str(e), 'timestamp': datetime.now().isoformat()}, ensure_ascii=False)}\n\n"

    return Response(
        generate_response(),
        mimetype="text/event-stream",
        headers={"Cache-Control": "no-cache", "X-Accel-Buffering": "no"},
    )

@app.route("/aicode/api/projects/<project_id>/tasks/<task_id>/chat", methods=["DELETE"])
@login_required
def api_task_chat_clean(project_id, task_id):
    """清理任务会话的session、日志记录"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            logging.error(f"项目不存在: {project_id}")
            return jsonify({"error": "项目不存在"}), 404

        # 获取任务管理器
        task_manager = project_manager.get_task_manager(project_id)
        if not task_manager:
            logging.error("无法获取TaskManager")
            return jsonify({"error": "无法获取TaskManager"}), 500
        
        result = task_manager.clean_task_session(int(task_id))
        return jsonify(result)

    except Exception as e:
        logging.error(f"清理会话失败: {e}")
        return jsonify({"error": f"清理会话失败: {str(e)}"}), 500
    

@app.route("/aicode/api/projects/<project_id>/tasks/reset", methods=["POST"])
@login_required
def api_reset_project_tasks(project_id):
    """重置项目所有任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    try:
        result = project_manager.reset_project_tasks(project_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": f"重置任务失败: {e}"}), 500


@app.route("/aicode/api/projects/<project_id>/stop_execution", methods=["POST"])
@login_required
def api_stop_project_execution(project_id):
    """停止项目执行API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    try:
        result = project_manager.stop_project_execution(project_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": f"停止执行失败: {e}"}), 500


@app.route("/aicode/api/projects/<project_id>/tasks/<task_id>/run", methods=["POST"])
@login_required
def api_run_single_task(project_id, task_id):
    """运行单个任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    try:
        async_thread = (
            request.get_json().get("async_thread", False)
            if request.get_json()
            else False
        )
        result = project_manager.run_single_task(project_id, int(task_id), async_thread)
        return jsonify(result)
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/aicode/api/projects/<project_id>/tasks/<task_id>/toggle", methods=["POST"])
@login_required
def api_toggle_task_status(project_id, task_id):
    """切换任务启用/禁用状态API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    try:
        result = project_manager.toggle_task_status(project_id, int(task_id))
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": f"切换任务状态失败: {e}"}), 500


# ==================== 子任务管理API ====================


@app.route(
    "/aicode/api/projects/<project_id>/tasks/<task_id>/subtasks",
    methods=["GET", "POST"],
)
@login_required
def api_task_subtasks(project_id, task_id):
    """获取或创建子任务"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    task_manager = project_manager.get_task_manager(project_id)
    task = task_manager.get_task(int(task_id))
    if not task:
        return jsonify({"error": "任务不存在"}), 404

    if request.method == "GET":
        # 获取子任务列表
        try:
            subtasks = task.list_subtasks()
            return jsonify({"success": True, "subtasks": subtasks})
        except Exception as e:
            return jsonify({"error": f"获取子任务失败: {e}"}), 500

    elif request.method == "POST":
        # 创建子任务
        try:
            data = request.get_json()
            name = data.get("name")
            description = data.get("description", "")
            checklist = data.get("checklist", [])

            if not name:
                return jsonify({"error": "子任务名称不能为空"}), 400

            subtask_id = task.add_subtask(name, description, checklist)
            task_manager.save_tasks()

            return jsonify(
                {"success": True, "message": "子任务创建成功", "subtask_id": subtask_id}
            )
        except Exception as e:
            return jsonify({"error": f"创建子任务失败: {e}"}), 500


@app.route(
    "/aicode/api/projects/<project_id>/tasks/<task_id>/subtasks/<subtask_id>",
    methods=["GET", "PUT", "DELETE"],
)
@login_required
def api_task_subtask(project_id, task_id, subtask_id):
    """获取、更新或删除子任务"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    task_manager = project_manager.get_task_manager(project_id)
    task = task_manager.get_task(int(task_id))
    if not task:
        return jsonify({"error": "任务不存在"}), 404

    if request.method == "GET":
        # 获取子任务详情
        try:
            subtask = task.get_subtask(subtask_id)
            if not subtask:
                return jsonify({"error": "子任务不存在"}), 404

            return jsonify({"success": True, "subtask": subtask.to_dict()})
        except Exception as e:
            return jsonify({"error": f"获取子任务失败: {e}"}), 500

    elif request.method == "PUT":
        # 更新子任务
        try:
            data = request.get_json()
            success = task.update_subtask(subtask_id, **data)

            if not success:
                return jsonify({"error": "子任务不存在"}), 404

            task_manager.save_tasks()
            return jsonify({"success": True, "message": "子任务更新成功"})
        except Exception as e:
            return jsonify({"error": f"更新子任务失败: {e}"}), 500

    elif request.method == "DELETE":
        # 删除子任务
        try:
            success = task.delete_subtask(subtask_id)

            if not success:
                return jsonify({"error": "子任务不存在"}), 404

            task_manager.save_tasks()
            return jsonify({"success": True, "message": "子任务删除成功"})
        except Exception as e:
            return jsonify({"error": f"删除子任务失败: {e}"}), 500


@app.route(
    "/aicode/api/projects/<project_id>/tasks/<task_id>/subtasks/smart_split",
    methods=["POST"],
)
@login_required
def api_smart_split_task(project_id, task_id):
    """智能分解任务为子任务"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    task_manager = project_manager.get_task_manager(project_id)
    task = task_manager.get_task(int(task_id))
    if not task:
        return jsonify({"error": "任务不存在"}), 404

    try:
        # 调用智能分解方法
        result = task_manager.smart_split_task(int(task_id))
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": f"智能分解失败: {e}"}), 500


@app.route(
    "/aicode/api/projects/<project_id>/tasks/<task_id>/subtasks/delete_all",
    methods=["DELETE"],
)
@login_required
def api_delete_all_subtasks(project_id, task_id):
    """删除任务的所有子任务"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    task_manager = project_manager.get_task_manager(project_id)
    task = task_manager.get_task(int(task_id))
    if not task:
        return jsonify({"error": "任务不存在"}), 404

    try:
        subtask_count = len(task.subtasks)
        if subtask_count == 0:
            return jsonify({"success": True, "message": "当前没有子任务"})

        # 清空所有子任务
        task.subtasks.clear()
        task_manager.save_tasks()

        return jsonify({"success": True, "message": f"已删除 {subtask_count} 个子任务"})
    except Exception as e:
        return jsonify({"error": f"删除失败: {e}"}), 500


@app.route("/aicode/api/projects/<project_id>/files/content", methods=["GET"])
@login_required
def api_get_file_content(project_id):
    """获取文件内容API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"error": "项目不存在"}), 404

    file_path = request.args.get("path")
    if not file_path:
        return jsonify({"error": "文件路径不能为空"}), 400

    try:
        full_path = os.path.join(project.work_dir, file_path)

        # 安全检查：确保文件在项目目录内
        if not os.path.abspath(full_path).startswith(os.path.abspath(project.work_dir)):
            return jsonify({"error": "无效的文件路径"}), 400

        if not os.path.exists(full_path) or not os.path.isfile(full_path):
            return jsonify({"error": "文件不存在"}), 404

        # 检查文件大小（限制为1MB）
        if os.path.getsize(full_path) > 1024 * 1024:
            return jsonify({"error": "文件过大，无法预览"}), 400

        # 尝试读取文件内容
        try:
            with open(full_path, "r", encoding="utf-8") as f:
                content = f.read()
            encoding = "utf-8"
        except UnicodeDecodeError:
            try:
                with open(full_path, "r", encoding="gbk") as f:
                    content = f.read()
                encoding = "gbk"
            except UnicodeDecodeError:
                return jsonify({"error": "无法解码文件内容"}), 400

        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        return jsonify(
            {
                "content": content,
                "encoding": encoding,
                "extension": ext,
                "size": os.path.getsize(full_path),
                "path": file_path,
            }
        )

    except Exception as e:
        return jsonify({"error": f"读取文件失败: {e}"}), 500


# 文件管理API
@app.route("/aicode/api/projects/<project_id>/files", methods=["GET"])
@login_required
def api_list_files(project_id):
    """获取项目文件列表"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"success": False, "message": "项目不存在"}), 404

        path = request.args.get("path", "")

        # 检查项目目录是否存在
        if not os.path.exists(project.directory):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"项目目录不存在: {project.directory}",
                    }
                ),
                404,
            )

        file_manager = FileManager(project.directory)
        files = file_manager.list_files(path)

        return jsonify({"success": True, "files": [file.to_dict() for file in files]})

    except Exception as e:
        logging.error(f"获取文件列表失败: {e}")
        return (
            jsonify({"success": False, "message": f"获取文件列表失败: {str(e)}"}),
            500,
        )


@app.route("/aicode/api/projects/<project_id>/files/preview")
@login_required
def api_preview_file(project_id):
    """预览文件内容"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"success": False, "message": "项目不存在"})

        path = request.args.get("path", "")
        if not path:
            return jsonify({"success": False, "message": "文件路径不能为空"})

        file_manager = FileManager(project.directory)
        result = file_manager.get_file_content(path)

        if result is None:
            return jsonify({"success": False, "message": "文件不存在或无法读取"})

        return jsonify(result)

    except Exception as e:
        return jsonify({"success": False, "message": str(e)})


@app.route("/aicode/api/projects/<project_id>/files/download")
@login_required
def api_download_file(project_id):
    """下载文件"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"success": False, "message": "项目不存在"})

        path = request.args.get("path", "")
        if not path:
            return jsonify({"success": False, "message": "文件路径不能为空"})

        file_manager = FileManager(project.directory)
        file_path = file_manager.get_file_path(path)

        if not file_path:
            return jsonify({"success": False, "message": "文件不存在"})

        filename = os.path.basename(path)
        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        return jsonify({"success": False, "message": str(e)})


@app.route("/aicode/api/projects/<project_id>/files/stats")
@login_required
def api_file_stats(project_id):
    """获取项目文件统计"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({"success": False, "message": "项目不存在"})

        file_manager = FileManager(project.directory)
        stats = file_manager.get_project_stats()

        return jsonify({"success": True, "stats": stats})

    except Exception as e:
        return jsonify({"success": False, "message": str(e)})


# ==================== 定时任务API ====================

@app.route("/aicode/api/projects/<project_id>/tasks/<task_id>/schedule/status", methods=["GET"])
@login_required
def api_get_scheduled_task_status(project_id, task_id):
    """获取定时任务状态"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({"success": False, "message": "项目不存在"}), 404

    try:
        task_manager = project_manager.get_task_manager(project_id)
        status = task_manager.get_scheduled_task_status(int(task_id))

        return jsonify({"success": True, "status": status})

    except Exception as e:
        return jsonify({"success": False, "message": f"获取定时任务状态失败: {str(e)}"}), 500


if __name__ == "__main__":
    # 启动应用
    app.run(host="0.0.0.0", port=5005, debug=True)
