"""
任务管理器模块
实现任务的创建、管理、执行和监控功能
"""

import os
import json
import uuid
import shutil
import threading
import time
import asyncio
import logging
import uuid
import re
import ast
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from croniter import croniter
from knowledge_manager import KnowledgeManager
from config import Config
from log_manager import LLMLogManager
import ast
try:
    from .claude.claude_agent import ClaudeAgent
    from .utils import load_template_by_type, create_log_callback, clear_llm_log
    from .log_cleanup import check_session_exists
except ImportError:
    from claude.claude_agent import ClaudeAgent
    from utils import (
        load_template_by_type,
        create_log_callback,
        git_push,
        clear_llm_log,
    )
    from log_cleanup import check_session_exists

class SubTask:
    """子任务类"""

    def __init__(
        self,
        id: str,
        name: str,
        description: str = "",
        checklist: List[str] = None,  # type: ignore
        status: str = "pending",
    ):
        self.id = id
        self.name = name
        self.description = description
        self.checklist = checklist or []
        self.status = status  # pending, in_progress, completed, failed
        self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "checklist": self.checklist,
            "status": self.status,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SubTask":
        subtask = cls(
            id=data["id"],
            name=data["name"],
            description=data.get("description", ""),
            checklist=data.get("checklist", []),
            status=data.get("status", "pending"),
        )
        subtask.created_at = data.get("created_at", subtask.created_at)
        subtask.updated_at = data.get("updated_at", subtask.updated_at)
        return subtask


class Task:
    """任务类"""

    def __init__(
        self,
        id: int,
        title: str,
        description: str,
        priority: str = "medium",
        dependencies: List[int] = None,  # type: ignore
        status: str = "pending",
        details: str = "",
        testStrategy: str = "",
        stype=None,
        keep_session: bool = False,
        schedule: str = None,  # cron表达式，如 "0 */6 * * *" 表示每6小时执行一次
    ):
        self.id = id
        self.title = title
        self.description = description
        self.priority = priority
        self.dependencies = dependencies or []
        self.status = status  # pending, running, completed, failed, disable
        self.details = details  # 任务详细信息
        self.testStrategy = testStrategy  # 测试策略
        self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
        self.result = ""
        self.execution_time = 0  # 添加执行时间字段
        self.session_id = None
        # 该任务的后续请求
        self.next_requests = []
        # 任务类型：需求、设计、BUG、其他
        self.stype = stype
        # 是否保持会话
        self.keep_session = keep_session
        # 定时任务设置（cron表达式）
        self.schedule = schedule
        # 子任务列表
        self.subtasks: Dict[str, SubTask] = {}

    def to_dict(self) -> Dict[str, Any]:
        data = {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "priority": self.priority,
            "dependencies": self.dependencies,
            "status": self.status,
            "details": self.details,
            "testStrategy": self.testStrategy,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "result": self.result,
            "session_id": self.session_id,
            "next_requests": self.next_requests,
            "stype": self.stype,
            "keep_session": self.keep_session,
            "schedule": self.schedule,
            "subtasks": [subtask.to_dict() for subtask in self.subtasks.values()],
        }
        # 只有当执行时间存在时才添加到字典中
        if self.execution_time is not None:
            data["execution_time"] = self.execution_time
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Task":
        # 兼容两种格式：新格式使用id，旧格式使用task_id
        id = data.get("id")
        if id is None:
            # 处理旧格式
            id = data.get("task_id", 0)
        task = cls(
            id=id,  # type: ignore
            title=data["title"],
            description=data["description"],
            priority=data.get("priority", "medium"),
            dependencies=data.get("dependencies", []),
            status=data.get("status", "pending"),
            details=data.get("details", ""),
            testStrategy=data.get("testStrategy", ""),
            keep_session=data.get("keep_session", False),
            schedule=data.get("schedule"),
        )
        task.created_at = data.get("created_at", task.created_at)
        task.updated_at = data.get("updated_at", task.updated_at)
        task.result = data.get("result", "")
        task.session_id = data.get("session_id")
        task.execution_time = data.get("execution_time")  # 从数据中获取执行时间
        task.next_requests = data.get("next_requests", [])
        task.stype = data.get("stype", None)

        # 加载子任务
        subtasks_data = data.get("subtasks", [])
        for subtask_data in subtasks_data:
            subtask = SubTask.from_dict(subtask_data)
            task.subtasks[subtask.id] = subtask

        return task

    def add_subtask(self, name: str, description: str = "", checklist: List[str] = None) -> str:  # type: ignore
        """添加子任务"""
        subtask_id = str(uuid.uuid4())
        subtask = SubTask(
            id=subtask_id,
            name=name,
            description=description,
            checklist=checklist or [],
        )
        self.subtasks[subtask_id] = subtask
        self.updated_at = datetime.now().isoformat()
        return subtask_id

    def update_subtask(self, subtask_id: str, **kwargs) -> bool:
        """更新子任务"""
        subtask = self.subtasks.get(subtask_id)
        if not subtask:
            return False

        for key, value in kwargs.items():
            if hasattr(subtask, key):
                setattr(subtask, key, value)

        subtask.updated_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
        return True

    def delete_subtask(self, subtask_id: str) -> bool:
        """删除子任务"""
        if subtask_id not in self.subtasks:
            return False

        del self.subtasks[subtask_id]
        self.updated_at = datetime.now().isoformat()
        return True

    def get_subtask(self, subtask_id: str) -> Optional[SubTask]:
        """获取子任务"""
        return self.subtasks.get(subtask_id)

    def list_subtasks(self) -> List[Dict[str, Any]]:
        """列出所有子任务"""
        return [subtask.to_dict() for subtask in self.subtasks.values()]
    def has_schedule_config(self) -> bool:
        return self.schedule is not None and self.schedule.strip() != ""    
    def completed(self):
        if self.has_schedule_config():
            self.status = "in_progress"
        else:
            self.status = "completed"


class TaskManager:
    """任务管理器"""

    def __init__(
        self, project, log_manager=None, knowledge_manager: KnowledgeManager = None  # type: ignore
    ):
        self.project = project
        self.knowledge_manager = knowledge_manager
        self.project_name = project.name
        self.work_dir = project.work_dir
        self.provider = project.provider
        self.project_type = project.project_type
        self.log_manager = log_manager

        self.meta = {}
        self.stop_event = threading.Event()
        self.max_workers = 3  # 并发任务数

        # 从任务文件加载任务
        self.tasks: Dict[int, Task] = {}
        self.task_file_name = os.path.join(
            self.work_dir, f"{Config.AI_WORK_DIR}/task.json"
        )

        # 初始化Claude配置
        self._setup_claude_config(project.rules_constraint)
        self.is_running = False
        self.run_agents = []  # 记录当前运行的ClaudeAgent实例列表
        self.agent_lock = threading.Lock()  # 保护curr_agent列表的线程锁

        # 定时任务相关
        self.scheduled_tasks: Dict[int, threading.Thread] = {}  # 存储定时任务的线程
        self.scheduled_tasks_stop: Dict[int, threading.Event] = {}  # 存储定时任务的停止事件
        self.scheduled_tasks_lock = threading.Lock()  # 保护定时任务字典的线程锁
        self.load_tasks()

    def _setup_claude_config(self, rules_constraint: str = None):  # type: ignore
        """设置Claude配置文件 - 完成TODO任务"""
        try:
            # 创建.claude目录
            claude_dir = os.path.join(self.work_dir, ".claude")
            os.makedirs(claude_dir, exist_ok=True)

            # 复制CLAUDE.md
            claude_md_src = os.path.join(os.getcwd(), "prompts", "CLAUDE.md")
            claude_md_dst = os.path.join(self.work_dir, "CLAUDE.md")
            if os.path.exists(claude_md_src):
                shutil.copy2(claude_md_src, claude_md_dst)

            # 复制后替换CLAUDE.md中的work_dir
            with open(claude_md_dst, "r", encoding="utf-8") as f:
                claude_md = f.read()
                claude_md = claude_md.replace("{work_dir}", self.work_dir)
                # 如果项目有规则约束，则替换规则约束占位符
                if rules_constraint:
                    claude_md = claude_md.replace(
                        "{rules_constraint}", f"# 项目规则约束\n{rules_constraint}"
                    )
                else:
                    claude_md = claude_md.replace("{rules_constraint}", "")

            # 写入CLAUDE.md
            with open(claude_md_dst, "w", encoding="utf-8") as f:
                f.write(claude_md)

            # 复制settings.local.json
            settings_src = os.path.join(os.getcwd(), "prompts", "settings.local.json")
            with open(settings_src, "r", encoding="utf-8") as f:
                settings_json = f.read()
                # 替换settings.local.json中的{CLAUDE_CODE_MAX_OUTPUT_TOKENS}
                if self.provider == "local":
                    settings_json = settings_json.replace(
                        "{CLAUDE_CODE_MAX_OUTPUT_TOKENS}", "15000"
                    )
                else:
                    settings_json = settings_json.replace(
                        "{CLAUDE_CODE_MAX_OUTPUT_TOKENS}", "25000"
                    )

            settings_dst = os.path.join(claude_dir, "settings.local.json")
            with open(settings_dst, "w", encoding="utf-8") as f:
                f.write(settings_json)

        except Exception as e:
            self.log_event(
                "setup_error",
                f"设置Claude配置失败: {e}",
                level="warning",
            )

    def load_tasks(self):
        """从文件加载任务"""
        try:
            if not os.path.exists(self.task_file_name):
                return {"success": True , "message": "任务文件不存在"}

            # 先停止所有现有的定时任务线程
            self._stop_all_scheduled_tasks()
            
            # 清空现有任务
            self.tasks.clear()

            with open(self.task_file_name, "r", encoding="utf-8") as f:
                file_content = f.read()

            # 首先尝试使用标准JSON解析
            try:
                data = json.loads(file_content)
            except json.JSONDecodeError:
                data = ast.literal_eval(file_content)

            # 加载任务
            for task_data in data.get("tasks", []):
                task = Task.from_dict(task_data)
                self.tasks[task.id] = task
                if not task.has_schedule_config() and task.status == "in_progress":
                    task.status = "stopped"

            self.meta = data.get("meta", {})
            
            self._start_all_scheduled_tasks()
            
            return {"success": True}
        except Exception as e:
            logging.error(f"加载任务文件失败: {e}")
            return {"success": False, "message": f"加载任务文件失败:{e}"}

    def get_previous_task_session_id(self, current_task_id: int) -> Optional[str]:
        """获取指定任务的上一个任务的session_id"""
        try:
            # 获取所有任务并按ID排序
            sorted_tasks = sorted(self.tasks.values(), key=lambda t: t.id)

            # 找到当前任务在排序列表中的索引
            current_index = None
            for i, task in enumerate(sorted_tasks):
                if task.id == current_task_id:
                    current_index = i
                    break

            # 如果找不到当前任务或者当前任务是第一个任务，返回None
            if current_index is None or current_index == 0:
                return None

            # 获取上一个任务
            previous_task = sorted_tasks[current_index - 1]

            # 如果上一个任务有session_id，返回它
            if previous_task.session_id:
                logging.info(
                    f"任务 {current_task_id} 使用上一个任务 {previous_task.id} 的会话ID: {previous_task.session_id}"
                )
                return previous_task.session_id

            # 如果上一个任务没有session_id, 返回None
            return None

        except Exception as e:
            logging.error(f"查找上一个任务的session_id时出错: {e}")
            return None

    def save_tasks(self):
        """保存任务到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.task_file_name), exist_ok=True)
            # 准备任务数据
            tasks_data = {
                "meta": self.meta,
                "tasks": [task.to_dict() for task in self.tasks.values()],
            }

            # 保存到文件
            with open(self.task_file_name, "w", encoding="utf-8") as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)
            
            #logging.info(f"保存任务文件成功: {self.task_file_name}")

        except Exception as e:
            logging.error(f"保存任务文件失败: {e}")

    def get_task(self, task_id: int) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)

    def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任务(从文件加载)"""
        try:
            if not self.tasks:
                result = self.load_tasks()
                if not result.get("success"):
                    raise Exception(result.get("message"))
            tasks_list = []
            for task in self.tasks.values():
                if task.id != -1:
                    task_dict = task.to_dict()
                    if task.has_schedule_config():
                        schedule_status = self.get_scheduled_task_status(task.id)
                        task_dict.update(schedule_status)
                    tasks_list.append(task_dict)
            return tasks_list
        except Exception as e:
            raise e
    
    def todolist(self) -> str:
        """列出所有任务的描述，用于系统提示词"""        
        content = f"""
        # 任务列表及完成情况
        用户需求 {self.project.description} 已拆分为如下任务：
        """            
        for task in self.tasks.values():
            if task.id != -1:
                content += f"\n- [{'已完成' if task.status == 'completed' else ''}] {task.title} - {task.description}"
        return content

    def add_task(
        self,
        title: str,
        description: str,
        priority: str = "medium",
        dependencies: List[int] = None,  # type: ignore
        stype=None,
        status="pending",
        task_id=None,
        schedule: str = None,
    ) -> int:
        """添加新任务"""
        try:
            # 使用递增的数字ID而不是UUID
            existing_ids = [
                int(task.id) for task in self.tasks.values() if str(task.id).isdigit()
            ]
            if not task_id:
                task_id = max(existing_ids) + 1 if existing_ids else 1

            # 创建任务对象
            task = Task(
                id=task_id,
                title=title,
                description=description,
                priority=priority,
                dependencies=dependencies or [],
                status=status,
                stype=stype,
                schedule=schedule,
            )

            # 添加到任务列表
            self.tasks[task_id] = task

            # 保存任务
            self.save_tasks()

            # 注意：定时任务现在需要手动启动，不再自动启动
            if schedule and schedule.strip():
                self.log_event(
                    "schedule_configured",
                    f"任务配置为定时任务: {task.title} ({schedule})",
                    task_id=task_id,
                    level="info"
                )

            self.log_event(
                "task_added",
                f"添加新任务: {title}",
                task_id=task_id,
            )

            return task_id

        except Exception as e:
            logging.error(f"添加任务失败: {e}")
            raise e

    def update_task(self, task_id: int, **kwargs) -> bool:
        """更新任务"""
        task = self.tasks.get(int(task_id))
        if not task:
            return False

        # 只有pending状态的任务可以修改基本信息
        # if task.status != "pending" and any(k in kwargs for k in ['title', 'description', 'priority', 'dependencies']):
        #     return False

        # 检查是否修改了schedule
        old_schedule = task.schedule
        schedule_changed = 'schedule' in kwargs and kwargs['schedule'] != old_schedule

        for key, value in kwargs.items():
            if hasattr(task, key):
                setattr(task, key, value)

        task.updated_at = datetime.now().isoformat()
        self.save_tasks()

        # 如果修改了定时任务属性，先停止当前的定时线程
        if schedule_changed:
            self.stop_scheduled_task(task_id)
            task.status = "pending"

        return True

    def update_task_status(
        self, task_id: int, status: str, result: str = None
    ) -> bool:
        """更新任务状态"""
        task = self.tasks.get(task_id)
        if not task:
            return False
        old_status = task.status
        task.status = status
        task.updated_at = datetime.now().isoformat()

        if result is not None:
            task.result = result
        self.save_tasks()

        self.log_event(
            "task_status_changed",
            f"任务状态变更: {old_status} -> {status}",
            task_id=task_id,
        )

        return True

    def delete_task(self, task_id: int) -> bool:
        """删除任务（检查依赖关系）"""
        task = self.tasks.get(task_id)
        if not task:
            return False

        # 检查是否有其他任务依赖此任务
        dependent_tasks = [t for t in self.tasks.values() if task_id in t.dependencies]
        if dependent_tasks:
            return False  # 有依赖任务，不能删除

        del self.tasks[task_id]
        self.save_tasks()

        self.log_event(
            "task_deleted",
            f"删除任务: {task.title}",
            task_id=task_id,
        )

        return True

    def reset_all_tasks(self) -> dict[str, Any]:
        """重置所有任务状态"""
        try:
            self.stop_execution()
            for task_id, task in self.tasks.items():
                self._reset_task_status(task_id, save=True)

            # 重置meta数据
            self.meta["summary"] = ""

            # 保存更改
            self.save_tasks()

            logging.info(f"项目 {self.project_name} 的所有任务已重置")
            return {"success": True, "message": "项目所有任务已重置"}

        except Exception as e:
            logging.error(f"重置任务失败: {e}")
            return {"success": False, "message": f"重置任务失败: {e}"}

    def _reset_task_status(self, task_id: int, save: bool = True) -> bool:
        """重置任务状态"""
        task = self.tasks.get(int(task_id))
        if not task:
            return False
        task.status = "pending"
        task.result = ""
        task.updated_at = datetime.now().isoformat()
        task.execution_time = 0
        task.session_id = None
        if task.has_schedule_config():
            self.stop_scheduled_task(task_id)

        # 重置所有子任务状态
        for subtask in task.subtasks.values():
            subtask.status = "pending"
            subtask.updated_at = datetime.now().isoformat()

        llm_log_manager = LLMLogManager(self.project, task.id)
        # 清空之前的日志
        llm_log_manager.clear_logs()
        if save:
            self.save_tasks()

        self.log_event(
            "task_reset",
            f"重置任务状态: {task.title}",
            task_id=task_id,
        )

        return True

    def get_run_state(self):
        with self.agent_lock:
            return len(self.run_agents)  # 返回当前运行的ClaudeAgent数量

    def delete_all(self):
        # 先重置任务状态
        result = self.reset_all_tasks()
        if not result["success"]:
            return result
        self.tasks.clear()
        self.save_tasks()

    def can_task_run(self, task_id: int) -> bool:
        """检查任务是否可以运行（依赖关系检查）"""
        task = self.tasks.get(int(task_id))
        if not task:
            return False

        # 如果任务被禁用，则不能运行
        if task.status == "disable":
            return False

        # 检查所有依赖任务是否已完成
        for dep_id in task.dependencies:
            dep_task = self.tasks.get(int(dep_id))
            if dep_task and (
                dep_task.status == "disable" or dep_task.status != "completed"
            ):
                return False

        return True

    def load_prompt(self, prompt_file) -> str:
        with open(prompt_file, "r") as f:
            prompt = f.read()
        return prompt
    def gen_tasks(
        self,
        user_req,
        num_tasks=None,
        knowledge_base=None,
        progress_callback=None,
        mode="override",
        keep_session=True,
    ) -> Dict[str, Any]:
        # 如果是追加模式，备份当前任务列表
        if mode == "append":
            # 备份当前任务列表
            backup_tasks = {
                task_id: task.to_dict() for task_id, task in self.tasks.items()
            }
            max_task_id = max(
                [task_id for task_id in self.tasks.keys() if isinstance(task_id, int)],
                default=0,
            )
        else:
            # 覆盖模式，重置所有任务
            self.reset_all_tasks()
            self.tasks = {}
            backup_tasks = {}
            max_task_id = 0

        result = {}
        try:
            # 根据项目类型加载gen_task.md模板
            prompt = load_template_by_type(self.project, "gen_task.md")
            # 替换提示词中的占位符
            prompt = prompt.replace("{task_file_name}", self.task_file_name)
            # user_req = prompt + user_req
            prompt = prompt.replace("{user_req}", user_req)

            if num_tasks:
                prompt = prompt.replace(
                    "{num_tasks}", f"严格创建{num_tasks}个任务，编号从1到{num_tasks}"
                )
            else:
                prompt = prompt.replace(
                    "{num_tasks}",
                    "根据需求/设计复杂性创建最佳数量的任务 - 使用您的判断力确定多少任务是合适的",
                )
            
            prompt = prompt + f"""
            # 任务文件格式检查
            任务文件生成完毕，检查 {self.task_file_name} 是否符合json格式.
            """

            logging.info("正在调用 Agent 进行任务拆解...")
            # task_id = -1 用于记录创建任务的日志
            log_callback = create_log_callback(self.project, -1, progress_callback)

            claude_agent = ClaudeAgent(
                "你是一个需求任务拆解助手", self.provider, self.knowledge_manager
            )

            # 如果任务文件已经存在，则删除
            if os.path.exists(self.task_file_name):
                os.remove(self.task_file_name)
            result = claude_agent.run_agent_sync(
                self.work_dir, prompt, None, "", log_callback
            )

            if result.get("success"):
                # 重新加载新生成的任务
                self.load_tasks()

                # 为所有生成的任务设置keep_session属性
                for task_id, task in self.tasks.items():
                    if isinstance(task_id, int):  # 只处理数字ID的任务
                        task.keep_session = keep_session
                self.save_tasks()

                logging.info(f"任务拆解完成，共{len(self.tasks)}个任务，keep_session={keep_session}")

                # 如果是追加模式，需要重新编号新任务并追加到现有任务中
                if mode == "append" and backup_tasks:
                    # 为新任务重新编号（从最大任务ID开始）
                    new_tasks = {}
                    for task_id, task in self.tasks.items():
                        if isinstance(task_id, int):
                            new_task_id = max_task_id + task_id
                            task.id = new_task_id
                            new_tasks[new_task_id] = task

                    # 合并旧任务和新任务
                    self.tasks = {
                        task_id: Task.from_dict(task_data)
                        for task_id, task_data in backup_tasks.items()
                    }
                    self.tasks.update(new_tasks)

                    # 保存合并后的任务列表
                    self.save_tasks()

                # 保存session_id到项目
                if "session_id" in result:
                    self.project.session_id = result["session_id"]

                # 从生成的任务中提取meta信息并处理通用需求
                self._extract_common_requirements()

                # 提取任务，并持久化到work_dir的./tasks/tasks.json文件
                return result
        except Exception as e:
            logging.error(f"任务拆解出错: {e}")
            result["success"] = False
            result["message"] = str(e)

        return result

    def _extract_common_requirements(self):
        """
        从任务生成结果中提取通用需求并写入CLAUDE.md
        """
        try:
            # 读取任务文件
            if os.path.exists(self.task_file_name):
                with open(self.task_file_name, "r", encoding="utf-8") as f:
                    task_data = json.load(f)

                # 提取meta中的通用需求
                common_requirements = []
                if "meta" in task_data and "commonRequirements" in task_data["meta"]:
                    common_requirements = task_data["meta"]["commonRequirements"]

                if common_requirements:
                    self._write_common_requirements_to_claude_md(common_requirements)
                    logging.info(
                        f"已提取{len(common_requirements)}个通用需求并写入CLAUDE.md"
                    )

        except Exception as e:
            logging.error(f"提取通用需求时出错: {e}")

    def _write_common_requirements_to_claude_md(self, common_requirements):
        """
        将通用需求写入项目的CLAUDE.md文件

        Args:
            common_requirements: 通用需求列表
        """
        claude_md_path = os.path.join(self.work_dir, "CLAUDE.md")

        # 构建通用需求内容
        common_requirements_content = "\n\n## 通用需求配置\n\n"
        common_requirements_content += "以下是从任务生成中提取的通用需求配置，将在项目执行中应用到各个功能模块：\n\n"

        for req in common_requirements:
            if isinstance(req, dict) and "name" in req and "description" in req:
                common_requirements_content += f"### {req['name']}\n"
                common_requirements_content += f"{req['description']}\n\n"
            elif isinstance(req, str):
                common_requirements_content += f"- {req}\n"

        # 读取现有的CLAUDE.md内容
        existing_content = ""
        if os.path.exists(claude_md_path):
            with open(claude_md_path, "r", encoding="utf-8") as f:
                existing_content = f.read()

        # 合并内容
        if existing_content:
            # 检查是否已经存在通用需求配置部分
            if "## 通用需求配置" in existing_content:
                # 更新现有的通用需求配置
                lines = existing_content.split("\n")
                start_idx = -1
                end_idx = -1

                for i, line in enumerate(lines):
                    if "## 通用需求配置" in line:
                        start_idx = i
                    elif (
                        start_idx != -1
                        and line.startswith("## ")
                        and "通用需求配置" not in line
                    ):
                        end_idx = i
                        break

                if start_idx != -1:
                    if end_idx == -1:
                        # 通用需求配置在文件末尾
                        existing_content = (
                            "\n".join(lines[:start_idx]) + common_requirements_content
                        )
                    else:
                        # 通用需求配置在文件中间
                        existing_content = (
                            "\n".join(lines[:start_idx])
                            + common_requirements_content
                            + "\n".join(lines[end_idx:])
                        )
                else:
                    existing_content += common_requirements_content
            else:
                # 添加通用需求配置到文件末尾
                existing_content += common_requirements_content
        else:
            # 创建新的CLAUDE.md文件
            existing_content = "# 项目配置文档\n\n" + common_requirements_content

        # 写入文件
        with open(claude_md_path, "w", encoding="utf-8") as f:
            f.write(existing_content)

        logging.info(f"已更新项目CLAUDE.md文件，添加了通用需求配置")

    def run_single_task(
        self, task_id: int, task_summary: str, progress_callback=None, session_id=None
    ) -> Dict[str, Any]:
        """重新运行或新运行单个任务"""
        task = self.tasks.get(task_id)
        if not task:
            return {"success": False, "message": "任务不存在"}
        if not self.can_task_run(task_id):
            return {"success": False, "message": "任务依赖未满足，无法运行"}

        result = {}
        start_time = time.time()  # 记录开始时间
        try:
            task_summary = None  # 暂时不用总结功能  # type: ignore
            # if not task_summary:
            #     task_summary = self._summarize_completed_tasks()

            system_prompt = self.get_system_prompt(task)

            if session_id:
                session_id_to_use = session_id
            else:
                # 检查是否需要查找上一个任务的session_id
                session_id_to_use = task.session_id
                if not session_id_to_use and task.keep_session:
                    session_id_to_use = self.get_previous_task_session_id(task.id)
                    if session_id_to_use:
                        logging.info(
                            f"任务 {task.id} (keep_session=True) 使用上一个任务的会话ID: {session_id_to_use}"
                        )

            if not check_session_exists(self.work_dir, session_id_to_use):
                session_id_to_use = None

            log_and_callback = create_log_callback(
                self.project, task.id, progress_callback
            )
            def log_session_callback(progress: int, title: str, content: str = "", new_session_id = None):
                log_and_callback(progress, title, content)
                if new_session_id:
                    task.session_id = new_session_id
                    self.save_tasks()

            # 检查是否有子任务
            if task.subtasks and len(task.subtasks) > 0:
                # 有子任务，顺序执行子任务
                result = self._run_task_with_subtasks(
                    task,
                    task_summary,
                    system_prompt,
                    session_id=session_id_to_use,
                    log_and_callback=log_session_callback,
                )
            else:
                # 没有子任务，执行任务描述
                result = self._run_task_without_subtasks(
                    task,
                    task_summary,
                    system_prompt,
                    session_id=session_id_to_use,
                    log_and_callback=log_session_callback,
                )

            if result.get("success") and task.testStrategy:
                # 在同一个会话中运行验证策略检查
                result = self._run_task_agent(
                    system_prompt,
                    f"验证任务是否完成,并解决问题：\n {task.testStrategy}",
                    log_session_callback,
                    session_id=task.session_id,
                    task=task,
                )

            if result.get("success"):
                # 提交代码
                if self.project.git_managed > 0:
                    user_req = f"""
                    # 提交变更
                    只将本次任务所修改的代码提交到Git仓库，不要推送。
                    """
                    result = self._run_task_agent(
                        "",
                        user_req,
                        session_id=task.session_id,
                        log_and_callback=log_session_callback,
                        task=task,
                    )

                if self.project.git_managed == 2 and result.get("success"):
                    # 在项目目录下运行 git push origin
                    git_push(self.project, self.project.work_dir, branch = None, log_and_callback=log_session_callback)

            return result
        except Exception as e:
            logging.error(f"任务{task.title}执行失败: {e}")
            return {"success": False, "message": f"任务执行失败: {e}"}
        finally:
            # 计算执行时间（即使失败也要记录）
            end_time = time.time()
            execution_time = end_time - start_time
            if task.execution_time is None:
                task.execution_time = 0
            task.execution_time += execution_time  # 累计执行时间

            self.save_tasks()

    def _run_task_without_subtasks(
        self,
        task: Task,
        task_summary: str,
        system_prompt: str,
        session_id=None,
        log_and_callback=None,
    ) -> Dict[str, Any]:
        """执行没有子任务的任务"""
        # 检查任务状态==进行中或失败，且有session_id，则发送:继续执行
        if task.stype == "feature":
            # 构建任务执行请求
            user_req = f"""用户需求被拆解为多个任务执行。"""

            if task_summary:
                user_req += f"""\n已经完成的任务总结如下：
                {task_summary}
                """
            user_req += f"""\n
            本次请完成下面这个任务。
            # 任务详情
            任务ID: {task.id}
            任务标题: {task.title}
            任务描述: {task.description}
            """
        else:
            # 非功能任务，直接使用任务的描述做为请求
            user_req = task.description

        # if task.status != "pending":
        #     if check_session_exists(self.work_dir, session_id):
        #         user_req = f"继续执行任务"

        return self._run_task_agent(
            system_prompt,
            user_req,
            log_and_callback=log_and_callback,
            session_id=session_id,
            task=task,
        )

    def _run_task_agent(
        self,
        system_prompt: str,
        user_request: str,
        session_id=None,
        log_and_callback=None,
        task: Task = None,
    ) -> Dict[str, Any]:
        """运行agent任务"""
        result = {}
        current_agent = None  # 用于跟踪当前执行的agent
        try:
            if task:
                task.status = "in_progress"
                task.updated_at = datetime.now().isoformat()
                self.save_tasks()

            current_agent = ClaudeAgent(
                system_prompt, self.provider, self.knowledge_manager
            )
            # 添加agent到运行列表（加锁保护）
            with self.agent_lock:
                self.run_agents.append(current_agent)
            result = current_agent.run_agent_sync(
                self.work_dir, user_request, session_id, "", log_and_callback
            )
        except Exception as e:
            log_and_callback(100,"Error", f"任务-[{task.title if task else '' }]运行出错: {e}")
            result["success"] = False
            result["message"] = str(e)
        finally:
            if task:
                task.result = result.get("message")
                if result.get("session_id"):
                    task.session_id = result.get("session_id")
                if result.get("success"):
                    task.completed()
                elif result.get("message") == "stopped":
                    task.status = "stopped"
                else:
                    task.status = "failed"
                    task.result = result.get("message")
                logging.info(f"任务-[{task.title}]执行完成，保存状态: {task.status}")
                self.save_tasks()
            # 从列表中移除当前已完成的agent（加锁保护）
            if current_agent:
                with self.agent_lock:
                    if current_agent in self.run_agents:
                        self.run_agents.remove(current_agent)

        return result

    def _run_task_with_subtasks(
        self,
        task: Task,
        task_summary: str,
        system_prompt: str,
        session_id=None,
        log_and_callback=None,
    ) -> Dict[str, Any]:
        """执行有子任务的任务 - 顺序执行所有子任务，共用一个会话ID"""
        # 获取子任务列表并排序（按创建时间或ID）
        subtasks_list = sorted(task.subtasks.values(), key=lambda st: st.created_at)

        # 查找第一个未完成的子任务
        start_index = 0
        for i, subtask in enumerate(subtasks_list):
            if subtask.status != "completed":
                start_index = i
                break
        if start_index >= len(subtasks_list):
            task.completed()
            return {"success": False, "message": "所有子任务均已完成."}

        try:
            task.status = "in_progress"
            task.updated_at = datetime.now().isoformat()
            self.save_tasks()
            # 从第一个未完成的子任务开始执行
            for idx, subtask in enumerate(subtasks_list[start_index:], start_index + 1):
                subtask.status = "in_progress"
                self.save_tasks()

                user_req = f"""
                主任务已经被拆分为{len(subtasks_list)}个子任务，本次请完成如下子任务：
                ## 子任务 {idx}: {subtask.name}
                {subtask.description}
                
                ## 主任务描述
                {task.description}
                """

                if subtask.checklist:
                    user_req += f"""
                ## 检查项
                完成后请确认以下检查项：
                """
                    for check_item in subtask.checklist:
                        user_req += f"""- {check_item}
                """

                # 执行子任务
                result = self._run_task_agent(
                    system_prompt,
                    user_req,
                    session_id=session_id,
                    log_and_callback=log_and_callback,
                    task=None,
                )
                # 更新session_id
                session_id = result.get("session_id")
                task.session_id = session_id

                if not result.get("success"):
                    subtask.status = "failed"
                    task.status = "failed"
                    task.result = result.get("message")
                    return result

                subtask.status = "completed"
                self.save_tasks()

            # 所有任务成功完成
            task.completed()
            return {
                "success": True,
                "message": "所有子任务已完成",
                "session_id": session_id,
            }

        except Exception as e:
            logging.error(f"任务-[{task.title}]运行出错: {e}")
            task.status = "failed"
            task.result = str(e)
            return {"success": False, "message": str(e)}
        finally:
            self.save_tasks()    

    def get_task_logs(self, task_id: int) -> List[str]:
        """获取任务运行日志"""
        try:
            llm_log_manager = LLMLogManager(self.project, task_id)

            # 获取LLM日志
            log_entries = llm_log_manager.get_logs(100)

            if not log_entries:
                return ["暂无日志记录"]

            # 转换为字符串格式
            logs = []
            for entry in log_entries:
                log_line = f"[{entry['timestamp']}] {entry['progress']} - {entry['log_type']}: {entry['content']}"
                logs.append(log_line)

            return logs

        except Exception as e:
            logging.error(f"获取任务日志失败: {e}")
            return [f"获取日志失败: {e}"]

    # 与前端UI交互式的运行方法
    def run_chat(
        self, task_id: int, chat_req: str, progress_callback=None
    ) -> Dict[str, Any]:
        """
        运行聊天交互
        优化后的方法，更适合实时对话场景
        """
        start_time = time.time()
        task = None
        current_agent = None  # 用于跟踪当前执行的agent

        try:
            task = self.tasks.get(task_id)
            if not task:
                return {"success": False, "message": "任务不存在"}

            # 创建日志回调函数
            log_and_callback = create_log_callback(
                self.project, task.id, progress_callback
            )

            # 初始化Claude Agent
            current_agent = ClaudeAgent("", self.provider, self.knowledge_manager)
            # 添加agent到运行列表（加锁保护）
            with self.agent_lock:
                self.run_agents.append(current_agent)

            session_id = task.session_id
            if not check_session_exists(self.work_dir, session_id):
                session_id = None
            # 执行AI对话
            result = current_agent.run_agent_sync(
                self.work_dir, chat_req, session_id, "", log_and_callback
            )

            # 处理返回结果
            if result.get("success"):
                # 记录AI响应
                ai_response = result.get("message", "")

                # 更新任务信息
                # task.result = ai_response
                if result.get("session_id"):
                    task.session_id = result.get("session_id")

                return {
                    "success": True,
                    "message": ai_response,
                    "session_id": result.get("session_id"),
                    "task_id": task.id,
                }
            else:
                # 处理失败情况
                error_msg = result.get("message", "AI处理失败")
                task.result = f"处理失败: {error_msg}"

                return {"success": False, "message": error_msg, "error": error_msg}

        except Exception as e:
            error_msg = f"聊天过程中出错: {str(e)}"
            logging.error(f"任务-[{task.title if task else 'Unknown'}]聊天出错: {e}")

            # 记录错误日志
            if task:
                task.result = error_msg

            return {"success": False, "message": error_msg, "error": error_msg}

        finally:
            # 更新执行时间（但不影响任务状态）
            if task:
                end_time = time.time()
                execution_time = end_time - start_time
                if task.execution_time is None:
                    task.execution_time = 0
                task.execution_time += execution_time  # 累计执行时间

                # 保存任务状态（但不改变状态字段）
                self.save_tasks()

            # 从列表中移除当前已完成的agent（加锁保护）
            if current_agent:
                with self.agent_lock:
                    if current_agent in self.run_agents:
                        self.run_agents.remove(current_agent)

    def clean_task_session(self, task_id: int):
        task = self.tasks.get(task_id)
        if not task:
            return {"success": False, "message": "任务会话不存在"}
        
        # 清理claude会话
        check_session_exists(self.work_dir, task.session_id, delete=True)
        
        # 清理日志
        llm_log_manager = LLMLogManager(self.project, task.id)
        # 清空之前的日志
        llm_log_manager.clear_logs()
        
        task.session_id = None
        self.save_tasks()
        return {"success": True, "message": "任务会话已清理"}
        
    def stop_execution(self):
        """停止任务执行"""
        self.stop_event.set()
        self.is_running = False
        # 停止所有运行的ClaudeAgent（加锁保护）
        with self.agent_lock:
            for agent in self.run_agents:
                agent.stop()  # 停止所有运行的ClaudeAgent
            self.run_agents = []  # 清空列表

        self.log_event(
            "execution_stopped",
            "任务执行已停止",
            level="warning",
        )

    def _summarize_completed_tasks(self) -> str:
        """总结已完成的任务"""
        completed_tasks = [
            task for task in self.tasks.values() if task.status in ["completed"]
        ]

        if not completed_tasks:
            return ""
        final_summary = self.meta.get("summary", "")
        if final_summary:
            final_summary = ""
            # return final_summary

        batch_summary = []
        task_content = ""
        if self.provider == "local":
            max_batch_size = 50 * 1024
        else:
            max_batch_size = 100 * 1024
        for task in completed_tasks:
            task_content += f"任务ID:{task.id}\n任务描述:{task.description}\n\n任务执行结果:{task.result}\n"
            if len(task_content.encode("utf-8")) > max_batch_size:
                batch_summary.append(self._summarize_task_batch(task_content))
                task_content = ""
        if task_content:
            batch_summary.append(self._summarize_task_batch(task_content))

        if len(batch_summary) > 1:
            # 合并多个总结
            final_summary = self._summarize_task_batch(batch_summary)
        else:
            final_summary = batch_summary[0]

        if self.meta:
            self.meta["summary"] = final_summary
        self.save_tasks()

        return final_summary

    def _summarize_task_batch(self, task_content: str, progress_callback=None) -> str:
        """总结一批任务"""
        try:
            claude_agent = ClaudeAgent(
                "你是一个任务总结助手，可以把已经完成的任务进行总结，用于指导下一个任务的执行",
                self.provider,
                self.knowledge_manager,
            )

            user_req = f"""请对以下已完成的任务进行总结：
            
            {task_content}
            """
            # 请提供简洁的总结，包括：
            # 1. 项目的简介、主要架构
            # 2. 已经完成的任务介绍
            # 3. 对后续任务的建议

            # 使用单独会话
            result = claude_agent.run_agent_sync(
                self.work_dir, user_req, None, "", progress_callback
            )

            if result.get("success"):
                summary = result["message"]
                return summary

        except Exception as e:
            logging.error(f"任务总结出错: {e}")
            result["message"] = f"任务总结出错: {e}"

        return result.get("message")

    def auto_run_tasks(
        self, parallel_mode: bool = False, progress_callback: Callable = None
    ) -> Dict[str, Any]:
        """自动运行任务 - 完成所有TODO任务"""
        # if self.is_running:
        #     return {"success": False, "message": "任务正在运行中"}
        self.is_running = True
        self.stop_event.clear()

        self.log_event(
            "execution_started",
            f"开始执行任务 ({'并行' if parallel_mode else '顺序'}模式)",
            level="info",
        )

        try:
            if parallel_mode:
                return self._run_tasks_parallel(progress_callback)
            else:
                return self._run_tasks_sequential(progress_callback)
        finally:
            self.is_running = False

    def _run_tasks_sequential(
        self, progress_callback: Callable = None
    ) -> Dict[str, Any]:
        """顺序执行任务"""
        executed_count = 0

        # 按优先级排序
        # self.tasks.sort(key=lambda t: {"high": 3, "medium": 2, "low": 1}.get(t.priority, 2), reverse=True)
        sorted_tasks = sorted(self.tasks.values(), key=lambda t: t.id)
        for task in sorted_tasks:
            if self.stop_event.is_set():
                break
            # 跳过禁用状态的任务和定时任务
            if (
                task.status == "disable"
                or task.status == "completed"
                or not self.can_task_run(task.id)
                or task.has_schedule_config()  # 跳过定时任务
            ):
                continue

            result = self.run_single_task(task.id, None, progress_callback)
            executed_count += 1

            if not result.get("success") and not self.stop_event.is_set():
                # 任务失败，记录日志
                self.log_event(
                    "task_failed",
                    f"任务执行失败: {task.title}",
                    task_id=task.id,
                    level="error",
                )
                return result

        return {
            "success": True,
            "message": f"顺序执行完成，共执行{executed_count}个任务",
            "executed_count": executed_count,
        }

    def _run_tasks_parallel(self, progress_callback: Callable = None) -> Dict[str, Any]:
        raise Exception("Parallel execution not implemented")

    def quick_task(
        self,
        description: str,
        enable_kb: bool,
        progress_callback: Callable = None,
        is_run: bool = True,
    ) -> Dict[str, Any]:
        """快速任务：一句话描述需求，系统自动生成任务并启动运行"""
        try:
            # 生成任务
            task_id = self.add_task(
                title=f"快速任务: {description[:20]}...",
                description=description,
                priority="high",
                stype="other",
            )

            self.log_event(
                "quick_task_created",
                f"创建快速任务: {description}",
                task_id=task_id,
            )

            if is_run:
                # 立即执行
                return self.run_single_task(
                    task_id=task_id,
                    task_summary=None,
                    progress_callback=progress_callback,
                )
            else:
                return {"success": True, "message": "任务创建成功"}

        except Exception as e:
            return {"success": False, "message": f"快速任务创建失败: {str(e)}"}

    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        total = len(self.tasks)
        pending = len([t for t in self.tasks.values() if t.status == "pending"])
        running = len([t for t in self.tasks.values() if t.status == "running"])
        completed = len([t for t in self.tasks.values() if t.status == "completed"])
        failed = len([t for t in self.tasks.values() if t.status == "failed"])

        return {
            "total": total,
            "pending": pending,
            "running": running,
            "completed": completed,
            "failed": failed,
            "completion_rate": (completed / total * 100) if total > 0 else 0,
        }

    def export_tasks(self) -> Dict[str, Any]:
        """导出任务数据"""
        return {
            "req_id": self.project_name,
            "tasks": [task.to_dict() for task in self.tasks.values()],
            "statistics": self.get_task_statistics(),
            "exported_at": datetime.now().isoformat(),
        }

    def get_task_logs(self, limit: int = 50):
        """获取任务日志"""
        if not self.log_manager:
            return []

        try:
            # 从日志管理器获取日志，使用req_id作为过滤条件
            logs = self.log_manager.get_task_logs(limit=limit)
            # 过滤与当前需求相关的日志
            filtered_logs = [
                log
                for log in logs
                if log.get("req_id") == self.project_name
                or log.get("task_id") == self.project_name
            ]
            return filtered_logs[:limit]
        except Exception as e:
            self.log_event(
                "log_fetch_error",
                f"获取日志失败: {e}",
                level="error",
            )
            return []

    # 直接实现用户需求
    def run_requirement(
        self,
        user_req,
        enable_kb: bool,
        progress_callback=None,
        is_run: bool = True,
    ) -> Dict[str, Any]:
        return self.quick_task(user_req, enable_kb, progress_callback, is_run = is_run)

    # 直接实现用户的设计
    def run_design(
        self,
        design_doc,
        enable_kb: bool,
        progress_callback=None,
        is_run: bool = True,
    ) -> Dict[str, Any]:
        return self.quick_task(design_doc, enable_kb, progress_callback, is_run)

    def toggle_task_status(self, task_id: int, state: str = "completed") -> bool:
        """切换任务的启用/禁用状态"""
        task = self.tasks.get(int(task_id))
        if not task:
            return False

        # 切换任务状态：完成和待执行
        if task.has_schedule_config():
          self.stop_scheduled_task(task_id)
          task.status = "pending"
        elif task.status == "completed":
            task.session_id = None
            task.status = "pending"
            task.execution_time = 0
            task.result = None
            # 清理这个任务的日志
            clear_llm_log(self.project, task_id)
        else:
            task.status = "completed"

        task.updated_at = datetime.now().isoformat()
        self.save_tasks()

        self.log_event(
            "task_status_toggled",
            f"任务状态切换: {task.title} -> {task.status}",
            task_id=task_id,
        )

        return True

    def smart_split_task(self, task_id: int) -> Dict[str, Any]:
        """智能分解任务为子任务"""
        task = self.tasks.get(int(task_id))
        if not task:
            return {"success": False, "message": "任务不存在"}

        try:
            # 获取所有任务列表，用于提供上下文
            all_tasks = [t for t in self.tasks.values() if t.id != -1]
            task_list_str = "\n".join([f"{t.id}: {t.title}" for t in all_tasks])

            # 构建智能分解的prompt
            user_req = f"""请分析以下任务，将其拆解为合理的子任务。

首先，这是项目中现有的所有任务列表：
{task_list_str}

现在需要拆解的任务是（任务ID: {task.id}）：
任务标题: {task.title}
任务描述: {task.description}

请根据任务复杂度将任务拆解为3-5个具体的子任务，每个子任务应该包含：
1. 子任务名称（简短明确）
2. 子任务描述（详细说明要做什么）
3. 检查项列表（完成该子任务需要检查的要点，3个以内）

注意事项：
1. 子任务的范围不应超出当前任务的功能要求
2. 子任务应该是当前任务的组成部分，而不是独立的新任务
3. 不要创建与任务列表中已有任务重复或相似的子任务
4. 子任务应该是可执行且具体的步骤

请以JSON格式返回，格式如下：
```json
{{
  "subtasks": [
    {{
      "name": "子任务名称",
      "description": "子任务描述",
      "checklist": ["检查项1", "检查项2", "检查项3"]
    }}
  ]
}}
```

注意：只返回JSON数据，不要有其他说明文字。"""

            # 使用Claude Agent进行智能分解
            claude_agent = ClaudeAgent(
                "你是一个任务分解专家，擅长将复杂任务拆解为可执行的子任务",
                self.provider,
                self.knowledge_manager,
            )

            log_call_back = create_log_callback(self.project, -1)
            result = claude_agent.run_agent_sync(
                self.work_dir, user_req, None, "", log_callback = log_call_back
            )

            if not result.get("success"):
                return {
                    "success": False,
                    "message": f"智能分解失败: {result.get('message', '未知错误')}",
                }

            # 解析返回的JSON
            message = result.get("message", "")

            # 尝试从代码块中提取JSON
            import re

            json_match = re.search(r"```json\s*(\{.*?\})\s*```", message, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个消息
                json_str = message

            try:
                subtasks_data = json.loads(json_str)
                subtasks = subtasks_data.get("subtasks", [])

                # 创建子任务
                created_count = 0
                for subtask_data in subtasks:
                    name = subtask_data.get("name")
                    description = subtask_data.get("description", "")
                    checklist = subtask_data.get("checklist", [])

                    if name:
                        task.add_subtask(name, description, checklist)
                        created_count += 1

                self.save_tasks()

                return {
                    "success": True,
                    "message": f"成功创建{created_count}个子任务",
                    "count": created_count,
                }

            except json.JSONDecodeError as e:
                logging.error(f"解析智能分解结果失败: {e}\n返回内容: {message}")
                return {
                    "success": False,
                    "message": f"解析智能分解结果失败，请稍后重试",
                }

        except Exception as e:
            logging.error(f"智能分解任务失败: {e}")
            return {"success": False, "message": f"智能分解失败: {str(e)}"}

    def get_system_prompt(self, task: Task) -> str:
        """
        根据不同的任务类型返回不同的系统提示词

        Returns:
            str: 系统提示词
        """
        if task.stype == "bug":
            return (
                f"""你是一位专业的软件调试专家和BUG修复工程师，正在为项目"{self.work_dir}"定位和修复软件缺陷。""",
            )

        prompt_templates = {
            "代码重构": f"""你是一位专业的软件架构师和代码重构专家，正在为项目"{self.work_dir}"进行代码重构工作。""",
            "PMO": f"""你是一位经验丰富的项目管理专家，正在为项目"{self.work_dir}"处理PMO（用户提出的项目需求）相关需求。""",
            "产品迭代": f"""你是一位资深的产品开发专家，正在为项目"{self.work_dir}"设计和开发新功能。""",
            "代码分析": f"""你是一位资深的代码审查专家和技术分析师，正在对项目"{self.work_dir}"进行代码分析。""",
            "BUG修复": f"""你是一位专业的软件调试专家和BUG修复工程师，正在为项目"{self.work_dir}"定位和修复软件缺陷。""",
            "其他": f"""你是一位全栈开发专家，正在处理项目"{self.work_dir}"的各类任务。""",
        }

        # 返回对应任务类型的提示词，如果没有匹配则返回默认的"其他"提示词
        return prompt_templates.get(self.project_type, prompt_templates["其他"])

    def log_event(
        self, event_type: str, message: str, task_id: int = None, level: str = "info"
    ):
        """统一的日志记录方法"""
        if self.log_manager:
            self.log_manager.log_event(
                self.project_name,
                event_type,
                message,
                task_id=task_id,
                level=level,
            )

    def start_scheduled_task(self, task_id: int) -> bool:
        """启动定时任务"""
        task = self.tasks.get(task_id)
        if not task or not task.schedule:
            return False

        # 先停止该任务已启动的线程
        self.stop_scheduled_task(task_id)
        
        with self.scheduled_tasks_lock:
            # 创建停止事件
            stop_event = threading.Event()
            self.scheduled_tasks_stop[task_id] = stop_event

            # 创建并启动定时任务线程
            task_thread = threading.Thread(
                target=self._run_scheduled_task,
                args=(task_id, stop_event),
                daemon=True
            )
            task.status = "in_progress"
            self.save_tasks()
            self.scheduled_tasks[task_id] = task_thread
            task_thread.start()
            return True

    def stop_scheduled_task(self, task_id: int) -> bool:
        """停止定时任务"""
        with self.scheduled_tasks_lock:
            if task_id not in self.scheduled_tasks:
                return False

            # 发送停止信号
            stop_event = self.scheduled_tasks_stop.get(task_id)
            if stop_event:
                stop_event.set()

            # 等待线程结束
            task_thread = self.scheduled_tasks[task_id]
            if task_thread.is_alive():
                task_thread.join(timeout=5)

            # 清理资源
            del self.scheduled_tasks[task_id]
            if task_id in self.scheduled_tasks_stop:
                del self.scheduled_tasks_stop[task_id]

            task = self.tasks.get(task_id)
            if task:
                self.log_event(
                    "scheduled_task_stopped",
                    f"定时任务停止: {task.title}",
                    task_id=task_id,
                    level="info"
                )
            return True
    def _run_scheduled_task(self, task_id: int, stop_event: threading.Event):
        """定时任务执行线程"""
        task = self.tasks.get(task_id)
        if not task or not task.schedule:
            return

        try:
            # 创建cron迭代器
            cron = croniter(task.schedule, datetime.now())

            while not stop_event.is_set():
                # 计算下次执行时间
                next_time = cron.get_next(datetime)
                current_time = datetime.now()

                # 计算等待时间（秒）
                wait_seconds = (next_time - current_time).total_seconds()

                if wait_seconds <= 0:
                    # 如果已经过了执行时间，计算下一次
                    continue

                # 等待到执行时间或停止信号
                if stop_event.wait(wait_seconds):
                    break

                # 检查任务是否仍然存在且是定时任务
                task = self.tasks.get(task_id)
                if not task or not task.schedule:
                    break

                if stop_event.is_set():
                    break
                # 执行任务
                self.log_event(
                    "scheduled_task_executing",
                    f"开始执行定时任务: {task.title}",
                    task_id=task_id,
                    level="info"
                )

                # 确定使用的session_id
                session_id_to_use = None
                if task.keep_session:
                    session_id_to_use = task.session_id
                # 执行任务
                result = self.run_single_task(task_id, None, None, session_id_to_use)

                self.log_event(
                    "scheduled_task_completed",
                    f"定时任务执行完成: {task.title} - {'成功' if result.get('success') else '失败'}",
                    task_id=task_id,
                    level="info" if result.get("success") else "error"
                )
                if not result.get("success"):
                    break

        except Exception as e:
            task.status = "failed"
            task.result = f"定时任务执行出错: {task.title if task else 'Unknown'} - {str(e)}"
            self.save_tasks()
        finally:
            # 清理停止事件
            with self.scheduled_tasks_lock:
                if task_id in self.scheduled_tasks_stop:
                    del self.scheduled_tasks_stop[task_id]
                if task_id in self.scheduled_tasks:
                    del self.scheduled_tasks[task_id]
    def get_scheduled_task_status(self, task_id: int) -> Dict[str, Any]:
        """获取定时任务状态"""
        task = self.tasks.get(task_id)
        if not task:
            return {}
        with self.scheduled_tasks_lock:
            is_running = task_id in self.scheduled_tasks and self.scheduled_tasks[task_id].is_alive()

        return {
            "has_schedule_config": True,
            "is_running": is_running,
            "schedule": task.schedule,
            "next_run_time": self._get_next_run_time(task.schedule) if is_running else None
        }

    def _get_next_run_time(self, schedule: str) -> Optional[str]:
        """获取下次执行时间"""
        try:
            cron = croniter(schedule, datetime.now())
            next_time = cron.get_next(datetime)
            return next_time.isoformat()
        except Exception:
            return None

    def _stop_all_scheduled_tasks(self):
        """停止所有定时任务"""
        with self.scheduled_tasks_lock:
            task_ids = list(self.scheduled_tasks.keys())

        for task_id in task_ids:
            self.stop_scheduled_task(task_id)

    def _start_all_scheduled_tasks(self):
        """重启所有已启用的定时任务（在加载任务后调用）"""
        for task_id, task in self.tasks.items():
            if task.status == "in_progress" and task.has_schedule_config():
                # 启动进行中状态(失败、停止、完成)的定时任务线程
                self.start_scheduled_task(task_id)
