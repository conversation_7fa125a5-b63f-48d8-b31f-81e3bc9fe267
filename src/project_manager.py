import os
import json
import shutil
import logging
import subprocess
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from urllib.parse import urlparse, urlunparse, quote

from task_manager import TaskManager
from knowledge_manager import KnowledgeManager
from log_manager import TaskLogManager
from document_manager import DocumentManager
from file_manager import FileManager
from config import Config
from utils import (
    run_git_command,
    git_clone,
    git_checkout_branch,
    git_commit,
    git_push,
    git_pull,
    extract_repo_name,
    build_git_work_dir,
    build_clone_url,
)


class Project:
    """项目类"""

    def __init__(
        self,
        project_id: str,
        name: str,
        work_dir: str,
        description: str = "",
        requirement: str = "",
        provider: str = "local",
        rules_constraint: str = "",
        project_type: str = "新产品",
        design: str = "",
        git_managed: int = 0,
        exclude_patterns: str = "",
        include_patterns: str = "",
        git_url: str = "",
        git_access_token: str = "",
        git_branch: str = "",
    ):
        self.project_id = project_id
        self.name = name
        self.work_dir = work_dir
        self.description = description
        self.requirement = requirement  # 项目需求
        self.design = design  # 项目设计文档
        self.provider = provider  # LLM provider: local, zhipu, claude
        self.rules_constraint = rules_constraint  # 规则约束
        self.project_type = (
            project_type  # 项目类型：代码重构、PMO、新功能、代码分析、其他
        )
        self.git_managed = (
            git_managed  # Git管理：0-无git管理 1-提交代码 2-提交代码并推送
        )
        self.exclude_patterns = (
            exclude_patterns  # 额外的目录和文件排除模式，逗号分隔的字符串
        )
        self.include_patterns = (
            include_patterns  # 包含的目录和文件模式，逗号分隔的字符串
        )
        self.git_url = git_url
        self.git_access_token = git_access_token
        self.git_branch = git_branch
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        self.tasks_generated = False  # 是否已生成任务
        self.task_manager_session = None  # TaskManager会话ID
        self.session_id = None  # Claude Agent会话ID，内部使用，不显示在前端
        self.run_state = False

    @property
    def directory(self) -> str:
        """获取项目目录路径"""
        return self.work_dir

    def to_dict(self, include_file=True, extra_fields=None) -> Dict[str, Any]:
        result = {
            "project_id": self.project_id,
            "name": self.name,
            "work_dir": self.work_dir,
            "description": self.description,
            "provider": self.provider,
            "rules_constraint": self.rules_constraint,
            "requirement": self.requirement if include_file else None,
            "design": self.design if include_file else None,
            "project_type": self.project_type,
            "git_managed": self.git_managed,
            "exclude_patterns": self.exclude_patterns,
            "include_patterns": self.include_patterns,
            "git_url": self.git_url,
            "git_access_token": self.git_access_token,
            "git_branch": self.git_branch,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "run_state": self.run_state,
            "tasks_generated": self.tasks_generated,
            "task_manager_session": self.task_manager_session,
            "session_id": self.session_id,  # 添加session_id字段
        }
        
        if extra_fields and isinstance(extra_fields, dict):
            result.update(extra_fields)
            
        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Project":
        project = cls(
            project_id=data["project_id"],
            name=data["name"],
            work_dir=data["work_dir"],
            description=data.get("description", ""),
            provider=data.get("provider", "local"),
            rules_constraint=data.get("rules_constraint", ""),  # 添加规则约束属性
            project_type=data.get("project_type", "新产品"),  # 添加项目类型属性
            git_managed=data.get("git_managed", 0),  # 添加Git管理属性，默认为0
            exclude_patterns=data.get("exclude_patterns", ""),  # 添加排除模式属性
            include_patterns=data.get("include_patterns", ""),  # 添加包含模式属性
            git_url=data.get("git_url", ""),
            git_access_token=data.get("git_access_token", ""),
            git_branch=data.get("git_branch", ""),
        )
        project.created_at = data.get("created_at", project.created_at)
        project.updated_at = data.get("updated_at", project.updated_at)
        project.tasks_generated = data.get("tasks_generated", False)
        project.session_id = data.get("session_id", None)  # 添加session_id字段
        return project

    def _get_ai_work_dir(self) -> str:
        """获取AI工作目录路径"""
        ai_work_dir = os.path.join(self.work_dir, Config.AI_WORK_DIR)
        os.makedirs(ai_work_dir, exist_ok=True)
        return ai_work_dir

    def _get_requirement_file_path(self) -> str:
        """获取需求文件路径"""
        return os.path.join(self._get_ai_work_dir(), "requirement.md")

    def _get_design_file_path(self) -> str:
        """获取设计文件路径"""
        return os.path.join(self._get_ai_work_dir(), "design.md")

    def _import_document_to_kb(self, knowledge_manager, file_path: str, doc_name: str, doc_title: str):
        """将文档导入到知识库

        Args:
            knowledge_manager: KnowledgeManager实例
            file_path: 文档文件路径
            doc_name: 文档名称
            doc_title: 文档标题
        """
        try:
            from knowledge_manager import extract_file_content

            # 检查文档是否已存在
            existing_doc = knowledge_manager.get_document(doc_name)

            if existing_doc:
                # 文档已存在，删除旧版本
                logging.info(f"检测到已存在的{doc_title}，将更新到知识库")
                knowledge_manager.delete_document(doc_name, delete_physical_file=False)

            # 创建文档元数据
            doc_meta = {
                "name": doc_name,
                "title": doc_title,
                "file_path": file_path,
                "status": "processing",
                "size": os.path.getsize(file_path),
                "type": ".md",
                "chunk_count": 0,
            }

            # 添加文档到元数据
            knowledge_manager.documents_meta[doc_name] = doc_meta
            knowledge_manager._save_documents_meta()

            # 提取文件内容
            content = extract_file_content(file_path, knowledge_manager.file_converter)
            if content:
                # 添加到知识库
                knowledge_manager.add_document(doc_meta, content)
                logging.info(f"✅ {doc_title}已成功导入知识库")
            else:
                logging.error(f"⚠️ 无法提取{doc_title}内容")
                doc_meta["status"] = "failed"
                knowledge_manager._save_documents_meta()

        except Exception as e:
            raise e
    def import_requirement_to_kb(self, knowledge_manager):
        requirement_file = self._get_requirement_file_path()
        return self._import_document_to_kb(knowledge_manager, requirement_file,
                        "requirement.md",
                        "需求文档")
    
    def import_design_to_kb(self, knowledge_manager):
        design_file = self._get_design_file_path()
        return self._import_document_to_kb(knowledge_manager, design_file,
                        "design.md",
                        "设计文档")

    def save_requirement_to_file(self, knowledge_manager=None):
        """将需求保存到独立的.md文件中

        Args:
            knowledge_manager: KnowledgeManager实例，用于自动导入到知识库
        """
        try:
            if self.requirement is not None:
                requirement_file = self._get_requirement_file_path()
                with open(requirement_file, "w", encoding="utf-8") as f:
                    f.write(self.requirement)

                # 如果启用了自动导入知识库功能，则导入文档
                if Config.AUTO_IMPORT_DOCS_TO_KB and knowledge_manager:
                    self.import_requirement_to_kb(knowledge_manager)
        except Exception as e:
            logging.error(f"Failed to save requirement : {e}")
            return ""

    def save_design_to_file(self, knowledge_manager=None):
        """将设计保存到独立的.md文件中

        Args:
            knowledge_manager: KnowledgeManager实例，用于自动导入到知识库
        """
        try:
            if self.design is not None:
                design_file = self._get_design_file_path()
                with open(design_file, "w", encoding="utf-8") as f:
                    f.write(self.design)

                # 如果启用了自动导入知识库功能，则导入文档
                if Config.AUTO_IMPORT_DOCS_TO_KB and knowledge_manager:
                    self.import_design_to_kb(knowledge_manager)
        except Exception as e:
            logging.error(f"Failed to save design : {e}")
            return ""

    def load_requirement_from_file(self) -> str:
        """从文件加载需求内容"""
        try:
            requirement_file = self._get_requirement_file_path()
            if os.path.exists(requirement_file):
                with open(requirement_file, "r", encoding="utf-8") as f:
                    return f.read()
            return self.requirement  # 如果文件不存在，返回内存中的值
        except Exception as e:
            logging.error(f"Failed to load requirement from file: {e}")
            return ""

    def load_design_from_file(self) -> str:
        """从文件加载设计内容"""
        try:
            design_file = self._get_design_file_path()
            if os.path.exists(design_file):
                with open(design_file, "r", encoding="utf-8") as f:
                    return f.read()
            return self.design  # 如果文件不存在，返回内存中的值
        except Exception as e:
            logging.error(f"Failed to load requirement from file: {e}")
            return ""


class ProjectManager:
    """项目管理器"""

    def __init__(self, data_dir: Optional[str] = None):
        if not data_dir:
            data_dir = os.path.abspath(Config.DATA_DIR)
        else:
            data_dir = os.path.abspath(data_dir)

        self.data_dir = data_dir
        self.projects_file = os.path.join(data_dir, "projects.json")
        self.task_managers = {}
        self.knowledge_managers = {}
        self.document_managers = {}
        self.file_managers = {}

        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)

        # 初始化数据文件
        self._init_data_files()

        # 加载数据
        self._load_projects()
        self.log_manager = TaskLogManager(data_dir)

    def _init_data_files(self):
        """初始化数据文件"""
        if not os.path.exists(self.projects_file):
            with open(self.projects_file, "w", encoding="utf-8") as f:
                json.dump({"projects": []}, f, indent=2, ensure_ascii=False)

    def _load_projects(self) -> Dict[str, Project]:
        """加载项目数据"""
        try:
            with open(self.projects_file, "r", encoding="utf-8") as f:
                data = json.load(f)

            projects = {}
            for project_data in data.get("projects", []):
                project = Project.from_dict(project_data)
                # 从文件加载需求和设计内容
                project.requirement = project.load_requirement_from_file()
                project.design = project.load_design_from_file()
                projects[project.project_id] = project

            self.projects = projects
        except Exception as e:
            print(f"加载项目数据失败: {e}")
            return {}

    def _save_projects(self):
        """保存项目数据"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.projects_file), exist_ok=True)

            data = {
                "projects": [
                    project.to_dict(False) for project in self.projects.values()
                ],
                "meta": {
                    "total_projects": len(self.projects),
                    "last_updated": datetime.now().isoformat(),
                },
            }
            with open(self.projects_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存项目数据失败: {e}")

  
    def _build_git_work_dir(self, provided_dir: str, git_url: str) -> str:
        """确定Git仓库的工作目录，默认位于当前目录下的data目录"""
        return build_git_work_dir(provided_dir, git_url, self.data_dir)

    def _build_clone_url(self, git_url: str, token: str) -> str:
        """构造包含访问令牌的Git地址（仅对HTTP(S)有效）"""
        return build_clone_url(git_url, token)


    def _clone_repository(
        self, project:Project, target_dir: str, git_url: str, token: str, branch: str
    ):
        """克隆Git仓库到目标目录"""
        git_clone(project, target_dir, git_url, token, branch)

    def _checkout_timestamp_branch(self, project, repo_dir: str, base_branch: str) -> str:
        """基于指定分支创建带时间戳的新分支"""
        return git_checkout_branch(project, repo_dir, base_branch)

    def _setup_git_project(
        self,
        project:Project,
        work_dir: str,
        git_url: str,
        git_access_token: str,
        git_branch: str,
    ) -> Tuple[str, str]:
        """初始化Git管理项目的工作目录并返回目录与新分支名称"""
        target_dir = self._build_git_work_dir(work_dir, git_url)
        os.makedirs(target_dir, exist_ok=True)

        if os.listdir(target_dir):
            raise ValueError("Git管理项目的工作目录必须为空")

        base_branch = (git_branch or "main").strip() or "main"
        try:
            self._clone_repository(project, target_dir, git_url, git_access_token, base_branch)
            new_branch = self._checkout_timestamp_branch(project, target_dir, base_branch)
        except Exception:
            shutil.rmtree(target_dir, ignore_errors=True)
            raise
        return target_dir, new_branch

    def create_project(
        self,
        name: str,
        work_dir: str,
        description: str = "",
        requirement: str = "",
        provider: str = "local",
        project_type: str = "新产品",
        rules_constraint: str = "",
        design: str = "",
        git_managed: int = 0,
        exclude_patterns: str = "",
        include_patterns: str = "",
        git_url: str = "",
        git_access_token: str = "",
        git_branch: str = "",
    ) -> str:
        """创建新项目"""
        # 使用时间戳作为项目ID而不是UUID
        import time

        project_id = str(int(time.time() * 1000))

        git_url = (git_url or "").strip()
        git_access_token = (git_access_token or "").strip()
        git_branch = (git_branch or "").strip()

        if git_managed and not git_url:
            raise ValueError("启用Git管理时必须提供Git地址")

        project = Project(
            project_id,
            name,
            work_dir,
            description,
            requirement,
            provider,
            rules_constraint,
            project_type=project_type,
            design=design,
            git_managed=git_managed,
            exclude_patterns=exclude_patterns,
            include_patterns=include_patterns,
            git_url=git_url,
            git_access_token=git_access_token,
            git_branch=git_branch,
        )
        
        if git_managed and git_url:
            work_dir, git_branch = self._setup_git_project(project,
                work_dir,
                git_url,
                git_access_token,
                git_branch,
            )
        else:
            if not work_dir:
                raise ValueError("工作目录不能为空")
            os.makedirs(work_dir, exist_ok=True)


        # 先将项目添加到projects字典，这样才能获取knowledge_manager
        self.projects[project_id] = project

        # 获取knowledge_manager以便自动导入知识库
        km = self.get_knowledge_manager(project_id)

        # 保存需求和设计到独立文件
        project.save_requirement_to_file(km)
        project.save_design_to_file(km)

        self._save_projects()
        return project_id

    def get_project(self, project_id: str) -> Optional[Project]:
        """获取项目"""
        if self.projects:
            self._load_projects()
        return self.projects.get(project_id)

    def list_projects(self) -> List[Project]:
        """列出所有项目"""
        if self.projects:
            self._load_projects()
        return list(self.projects.values())

    def update_project(
        self,
        project_id: str,
        name: str = None,
        description: str = None,
        work_dir: str = None,
        requirement: str = None,
        provider: str = None,
        rules_constraint: str = None,
        project_type: str = None,
        design: str = None,
        git_managed: int = None,
        exclude_patterns: str = None,
        include_patterns: str = None,
        git_url: str = None,
        git_access_token: str = None,
        git_branch: str = None,
    ) -> bool:
        """更新项目"""
        project = self.projects.get(project_id)
        if not project:
            return False

        if name is not None:
            project.name = name
        if description is not None:
            project.description = description
        if work_dir is not None:
            project.work_dir = work_dir
        if requirement is not None:
            # 保存需求到独立文件
            project.requirement = requirement
            # 获取knowledge_manager以便自动导入知识库
            km = self.get_knowledge_manager(project_id)
            project.save_requirement_to_file(km)
        if design is not None:
            # 保存设计到独立文件
            project.design = design
            # 获取knowledge_manager以便自动导入知识库
            km = self.get_knowledge_manager(project_id)
            project.save_design_to_file(km)
        if provider is not None:
            project.provider = provider
        if rules_constraint is not None:
            project.rules_constraint = rules_constraint
        if project_type is not None:
            project.project_type = project_type
        if git_managed is not None:
            project.git_managed = git_managed
        if exclude_patterns is not None:
            project.exclude_patterns = exclude_patterns
        if include_patterns is not None:
            project.include_patterns = include_patterns
        if git_url is not None:
            project.git_url = git_url
        if git_access_token is not None:
            project.git_access_token = git_access_token
        if git_branch is not None:
            project.git_branch = git_branch

        project.updated_at = datetime.now().isoformat()
        self._save_projects()

        task_manager = self.get_task_manager(project_id)
        if task_manager:
            self.task_managers.pop(project_id)

        return True

    def delete_project(self, project_id: str, delete_files: bool = False) -> bool:
        """删除项目"""
        project = self.projects.get(project_id)
        if not project:
            return False

        task_manager = self.get_task_manager(project_id)
        if task_manager.get_run_state():
            logging.warning("项目正在运行，请先停止运行项目")
            return False
        task_manager.delete_all()

        # 删除项目文件夹（如果指定）
        if delete_files and os.path.exists(project.work_dir):
            try:
                import shutil

                shutil.rmtree(project.work_dir)
            except Exception as e:
                print(f"删除项目文件夹失败: {e}")

        # 删除项目记录
        del self.projects[project_id]
        self._save_projects()
        return True

    # 项目文档管理方法
    def get_document_manager(self, project_id: str) -> Optional[DocumentManager]:
        """为项目创建DocumentManager实例"""
        project = self.projects.get(project_id)
        if not project:
            return None

        document_manager = self.document_managers.get(project_id)
        if not document_manager:
            knowledge_manager = self.get_knowledge_manager(project_id)
            task_manager = self.get_task_manager(project_id)
            # 创建DocumentManager实例
            document_manager = DocumentManager(
                project, self.log_manager, knowledge_manager, task_manager
            )
            self.document_managers[project_id] = document_manager
        return document_manager

    # 项目文件管理方法
    def get_file_manager(self, project_id: str) -> Optional[FileManager]:
        """为项目创建FileManager实例"""
        project = self.projects.get(project_id)
        if not project:
            return None

        file_manager = self.file_managers.get(project_id)
        if not file_manager:
            # 创建FileManager实例
            file_manager = FileManager(project.work_dir)
            self.file_managers[project_id] = file_manager
        return file_manager

    # 项目任务管理方法
    def get_task_manager(self, project_id: str) -> Optional[TaskManager]:
        """为项目创建TaskManager实例"""
        project = self.projects.get(project_id)
        if not project:
            return None

        task_manager = self.task_managers.get(project_id)
        if not task_manager:
            knowledge_manager = self.get_knowledge_manager(project_id)
            # 创建TaskManager实例，传入provider和project_type参数
            task_manager = TaskManager(project, self.log_manager, knowledge_manager)
            self.task_managers[project_id] = task_manager
        else:
            # 更新TaskManager的provider
            task_manager.provider = project.provider
            task_manager.project_type = project.project_type
            task_manager._setup_claude_config(project.rules_constraint)
        return task_manager

    def generate_tasks_for_project(
        self, project_id: str, num_tasks: int = None, doc_type: str = "requirement",
        special_instruction: str = None, requirement: str = None, mode: str = "override",
        async_thread : bool = False, keep_session: bool = True
    ) -> Dict[str, Any]:
        """为项目生成任务"""
        project = self.projects.get(project_id)
        if not project:
            return {"success": False, "message": "项目不存在"}

        task_manager = self.get_task_manager(project_id)
        if not task_manager:
            return {"success": False, "message": "无法创建TaskManager"}

        # 生成任务
        if requirement:
            user_req = requirement
        elif doc_type == "requirement":
            if not project.requirement.strip():
                return {"success": False, "message": "项目需求文档为空，请先设置项目需求"}

            user_req = f"""
            项目名称: {project.name}
            项目需求: {project.requirement}
            项目类型: {project.project_type}
            """
        else:
            if not project.design.strip():
                return {"success": False, "message": "项目设计文档为空，请先编制项目设计"}
            user_req = f"""
            项目名称: {project.name}
            项目设计: {project.design}
            项目类型: {project.project_type}
            """

        # 如果有特殊说明，追加到用户请求后面
        if special_instruction and special_instruction.strip():
            user_req += f"""

特殊要求:
{special_instruction.strip()}
"""

        # 如果需要异步执行，则在后台线程中运行任务生成
        if async_thread:
            import threading
            
            def run_gen_tasks():
                try:
                    result = task_manager.gen_tasks(user_req, num_tasks, mode=mode, keep_session=keep_session)
                    
                    if result.get("success"):
                        # 更新项目状态
                        project.tasks_generated = True
                        project.updated_at = datetime.now().isoformat()
                        self._save_projects()
                        
                    # 发送通知或记录日志表示任务生成已完成
                    self.log_manager.log_event(
                        project_id,
                        "task_generation_completed",
                        f"异步任务生成完成: {'成功' if result.get('success') else '失败'}",
                        level="info" if result.get('success') else "error"
                    )
                except Exception as e:
                    self.log_manager.log_event(
                        project_id,
                        "task_generation_error",
                        f"异步任务生成异常: {str(e)}",
                        level="error"
                    )
            
            thread = threading.Thread(target=run_gen_tasks)
            thread.daemon = True
            thread.start()
            
            # 异步模式立即返回
            return {"success": True, "message": "任务生成已在后台启动"}
        else:
            # 同步执行
            result = task_manager.gen_tasks(user_req, num_tasks, mode=mode, keep_session=keep_session)

            if result.get("success"):
                # 更新项目状态
                project.tasks_generated = True
                project.updated_at = datetime.now().isoformat()
                self._save_projects()

            return result

    def run_project_tasks(
        self, project_id: str, parallel_mode: bool = False, async_thread: bool = False
    ) -> Dict[str, Any]:
        """运行项目的任务"""
        project = self.projects.get(project_id)
        if not project:
            return {"success": False, "message": "项目不存在"}

        # 获取项目任务管理器检查任务列表是否为空
        task_manager = self.get_task_manager(project_id)
        if not task_manager or not task_manager.tasks:
            return {"success": False, "message": "项目尚未生成任务"}

        # 如果需要异步执行，则在后台线程中运行任务
        if async_thread:
            import threading
            
            def run_tasks():
                try:
                    result = task_manager.auto_run_tasks(parallel_mode=parallel_mode)
                    
                    if result.get("success"):
                        # 更新项目状态
                        project.updated_at = datetime.now().isoformat()
                        self._save_projects()
                    
                    # 发送通知或记录日志表示任务运行已完成
                    self.log_manager.log_event(
                        project.name,
                        "task_execution_completed",
                        f"异步任务运行完成: {'成功' if result.get('success') else '失败'}",
                        level="info" if result.get('success') else "error"
                    )
                except Exception as e:
                    self.log_manager.log_event(
                        project.name,
                        "task_execution_error",
                        f"异步任务运行异常: {str(e)}",
                        level="error"
                    )
            
            thread = threading.Thread(target=run_tasks)
            thread.daemon = True
            thread.start()
            
            # 异步模式立即返回
            return {"success": True, "message": "任务运行已在后台启动"}
        else:
            # 同步执行
            result = task_manager.auto_run_tasks(parallel_mode=parallel_mode)

            if result.get("success"):
                # 更新项目状态
                project.updated_at = datetime.now().isoformat()
                self._save_projects()

            return result

    def stop_project_execution(self, project_id: str) -> Dict[str, Any]:
        """停止项目任务执行"""
        project = self.projects.get(project_id)
        if not project:
            return {"success": False, "message": "项目不存在"}

        task_manager = self.get_task_manager(project_id)
        if not task_manager:
            return {"success": False, "message": "无法创建TaskManager"}

        # 停止任务执行
        try:
            task_manager.stop_execution()
            return {"success": True, "message": "已发送停止执行命令"}
        except Exception as e:
            return {"success": False, "message": f"停止执行失败: {str(e)}"}

    def get_project_summary(self, project_id: str) -> Dict[str, Any]:
        """获取项目摘要"""
        project = self.projects.get(project_id)
        if not project:
            return {}

        # 确保获取最新的需求和设计内容
        requirement_content = (
            project.requirement or project.load_requirement_from_file()
        )
        design_content = project.design or project.load_design_from_file()

        summary = {
            "project_id": project_id,
            "name": project.name,
            "description": project.description,
            "requirement": requirement_content,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            "tasks_generated": project.tasks_generated,
            "total_tasks": 0,
            "completed_tasks": 0,
        }

        # 统计任务数量（如果有TaskManager）
        if project.tasks_generated:
            task_manager = self.get_task_manager(project_id)
            if task_manager and os.path.exists(task_manager.task_file_name):
                try:
                    with open(task_manager.task_file_name, "r", encoding="utf-8") as f:
                        tasks_data = json.load(f)
                    tasks = tasks_data.get("tasks", [])
                    summary["total_tasks"] = len(tasks)
                    summary["completed_tasks"] = len(
                        [t for t in tasks if t.get("status") == "completed"]
                    )
                except:
                    pass

        return summary

    def run_single_task(self, project_id: str, task_id: int, async_thread: bool = False) -> Dict[str, Any]:
        """运行项目中的单个任务"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return {"success": False, "message": "无法获取TaskManager"}

            task = task_manager.get_task(task_id)
            if task.has_schedule_config():
                success = task_manager.start_scheduled_task(task_id)
                if success:
                    return {"success": True, "message": "定时任务已启动"}
                else:
                    return {"success": False, "message": "定时任务启动失败"}
            # 如果需要异步执行，则在后台线程中运行任务
            if async_thread:
                import threading
                
                def run_task():
                    try:
                        result = task_manager.run_single_task(task_id, None)
                        # 记录执行结果
                        self.log_manager.log_event(
                            project_id,
                            "task_execution_completed",
                            f"异步任务执行完成: 任务 {task_id} {'成功' if result.get('success') else '失败'}",
                            task_id=task_id,
                            level="info" if result.get('success') else "error"
                        )
                    except Exception as e:
                        self.log_manager.log_event(
                            project_id,
                            "task_execution_error",
                            f"异步任务执行异常: 任务 {task_id}, 错误: {str(e)}",
                            task_id=task_id,
                            level="error"
                        )
                
                thread = threading.Thread(target=run_task)
                thread.daemon = True
                thread.start()
                
                # 异步模式立即返回
                return {"success": True, "message": "任务已在后台启动执行"}
            else:
                # 同步执行
                result = task_manager.run_single_task(task_id, None)
                return result

        except Exception as e:
            return {"success": False, "message": str(e)}

    def get_task_logs(self, project_id: str, task_id: str) -> List[str]:
        """获取项目任务的运行日志"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return ["无法获取TaskManager"]

            return task_manager.get_task_logs(task_id)

        except Exception as e:
            return [f"获取日志失败: {e}"]

    def reset_project_tasks(self, project_id: str) -> dict[str, Any]:
        """重置项目的所有任务"""
        task_manager = self.get_task_manager(project_id)
        if not task_manager:
            return {"success": False, "message": "无法获取TaskManager"}

        return task_manager.reset_all_tasks()

    def add_task_to_project(
        self,
        project_id: str,
        title: str,
        description: str,
        dependencies: List[str] = None,
        test_strategy: str = "",
        keep_session: bool = False,
        stype: str = None,
        schedule: str = None,
    ) -> Dict[str, Any]:
        """为项目添加新任务"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return {"success": False, "message": "无法获取TaskManager"}

            # 创建任务时传入test_strategy参数
            task_id = task_manager.add_task(title, description, "medium", dependencies, stype)

            # 更新任务的testStrategy、keep_session和schedule属性
            update_data = {}
            if test_strategy:
                update_data['testStrategy'] = test_strategy
            if keep_session:
                update_data['keep_session'] = keep_session
            if schedule:
                update_data['schedule'] = schedule

            if update_data:
                task_manager.update_task(task_id, **update_data)

            return {"success": True, "task_id": task_id}

        except Exception as e:
            return {"success": False, "message": str(e)}

    def update_project_task(
        self, project_id: str, task_id: int, **kwargs
    ) -> Dict[str, Any]:
        """更新项目任务

        Args:
            project_id: 项目ID
            task_id: 任务ID
            **kwargs: 任务属性，支持以下参数：
                - title: 任务标题
                - description: 任务描述
                - priority: 优先级 (low/medium/high)
                - status: 状态 (pending/completed/blocked)
                - dependencies: 依赖任务列表
                - testStrategy: 测试策略
                - keep_session: 是否保持会话
                - stype: 任务类型
                - state: 任务状态
                - schedule: 定时任务cron表达式

        Returns:
            包含success和message的字典
        """
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return {"success": False, "message": "无法获取TaskManager"}

            success = task_manager.update_task(task_id, **kwargs)
            if success:
                return {"success": True, "message": "任务更新成功"}
            else:
                return {"success": False, "message": "任务更新失败"}

        except Exception as e:
            return {"success": False, "message": str(e)}

    def delete_task(self, project_id: str, task_id: int) -> Dict[str, Any]:
        """删除项目任务"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return {"success": False, "message": "无法获取TaskManager"}

            # 尝试删除任务
            success = task_manager.delete_task(task_id)
            if success:
                return {"success": True, "message": "任务删除成功"}
            else:
                return {
                    "success": False,
                    "message": "任务删除失败，任务不存在或有依赖关系",
                }

        except Exception as e:
            return {"success": False, "message": str(e)}

    def delete_all_tasks(self, project_id: str) -> Dict[str, Any]:
        """删除项目的所有任务"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return {"success": False, "message": "无法获取TaskManager"}

            return task_manager.delete_all()            

        except Exception as e:
            return {"success": False, "message": str(e)}

    def toggle_task_status(self, project_id: str, task_id: int) -> Dict[str, Any]:
        """切换任务启用/禁用状态"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return {"success": False, "message": "无法获取TaskManager"}

            success = task_manager.toggle_task_status(task_id)
            if success:
                return {"success": True, "message": "任务状态切换成功"}
            else:
                return {"success": False, "message": "任务状态切换失败"}

        except Exception as e:
            return {"success": False, "message": str(e)}

    # 知识库管理方法
    def get_knowledge_manager(self, project_id: str) -> Optional[KnowledgeManager]:
        """为项目创建TaskManager实例"""
        project = self.projects.get(project_id)
        if not project:
            return None

        knowledge_manager = self.knowledge_managers.get(project_id)
        if not knowledge_manager:
            # 创建KnowledgeManager实例，传入provider和project_type参数
            knowledge_manager = KnowledgeManager(project, self.log_manager)
            self.knowledge_managers[project_id] = knowledge_manager
        return knowledge_manager

def init_task_manager(project_manager:ProjectManager):
    projects = project_manager.list_projects()
    for project in projects:
        project_manager.get_task_manager(project.project_id)