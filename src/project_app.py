#!/usr/bin/env python3
"""
项目应用访问路由
支持访问项目work_dir下的静态文件，包括HTML、JS、CSS等
自动搜索项目首页HTML文件
"""

import os
import logging
from flask import Blueprint, send_from_directory, abort, redirect, request
from project_manager import ProjectManager
try:
    from .config import Config
except ImportError:
    from config import Config

# 创建蓝图
project_app_bp = Blueprint("project_app", __name__)

# 在应用初始化时会被设置
project_manager = None

def init_project_app(pm: ProjectManager):
    """设置项目管理器实例"""
    global project_manager
    project_manager = pm


def find_index_html(work_dir: str) -> str:
    """
    在项目work_dir中搜索首页HTML文件
    优先级顺序：
    1. index.html
    2. index.htm
    3. main.html
    4. home.html
    5. 第一个找到的.html文件

    :param work_dir: 项目工作目录
    :return: 相对于work_dir的HTML文件路径，如果未找到返回None
    """
    if not os.path.exists(work_dir) or not os.path.isdir(work_dir):
        return None

    # 优先级文件名列表
    priority_files = ['index.html', 'index.htm', 'main.html', 'home.html']

    # 首先在根目录查找优先级文件
    for filename in priority_files:
        file_path = os.path.join(work_dir, filename)
        if os.path.isfile(file_path):
            return filename

    # 如果根目录没有，搜索子目录（只搜索第一层）
    try:
        for item in os.listdir(work_dir):
            item_path = os.path.join(work_dir, item)
            if os.path.isdir(item_path):
                for filename in priority_files:
                    file_path = os.path.join(item_path, filename)
                    if os.path.isfile(file_path):
                        return os.path.join(item, filename)
    except (OSError, PermissionError) as e:
        logging.error(f"搜索目录时出错: {e}")

    # 如果还没找到，找第一个HTML文件（根目录）
    try:
        for item in os.listdir(work_dir):
            if item.endswith('.html') or item.endswith('.htm'):
                file_path = os.path.join(work_dir, item)
                if os.path.isfile(file_path):
                    return item
    except (OSError, PermissionError) as e:
        logging.error(f"搜索HTML文件时出错: {e}")

    # 搜索第一层子目录中的HTML文件
    try:
        for item in os.listdir(work_dir):
            item_path = os.path.join(work_dir, item)
            if os.path.isdir(item_path):
                for sub_item in os.listdir(item_path):
                    if sub_item.endswith('.html') or sub_item.endswith('.htm'):
                        file_path = os.path.join(item_path, sub_item)
                        if os.path.isfile(file_path):
                            return os.path.join(item, sub_item)
    except (OSError, PermissionError) as e:
        logging.error(f"搜索子目录HTML文件时出错: {e}")

    return None


@project_app_bp.route("/aicode/project_app/<project_id>/")
@project_app_bp.route("/aicode/project_app/<project_id>/<path:filename>")
def serve_project_app(project_id, filename=None):
    """
    访问项目应用的路由

    :param project_id: 项目ID
    :param filename: 文件路径（相对于项目work_dir）
    :return: 文件内容或错误
    """
    global project_manager

    if not project_manager:
        logging.error("项目管理器未初始化")
        abort(500, description="服务器内部错误：项目管理器未初始化")

    # 获取项目信息
    project = project_manager.get_project(project_id)
    if not project:
        logging.warning(f"项目不存在: {project_id}")
        abort(404, description="项目不存在")

    work_dir = project.work_dir

    # 验证work_dir存在
    if not os.path.exists(work_dir) or not os.path.isdir(work_dir):
        logging.warning(f"项目工作目录不存在: {work_dir}")
        abort(404, description="项目工作目录不存在")

    # 如果没有指定文件名，尝试查找首页
    if not filename:
        index_file = find_index_html(work_dir)
        if index_file:
            # 如果是网关路由到本系统，需要加网关路由地址
            base_url = Config.GATEYWAY_URL
            redirect_url = f"{base_url}/aicode/project_app/{project_id}/{index_file}"
            return redirect(redirect_url)
        else:
            # 如果没有找到HTML文件，返回目录列表
            abort(404, description="未找到项目首页HTML文件")

    # 安全检查：防止路径遍历攻击
    # 规范化路径
    abs_work_dir = os.path.abspath(work_dir)
    requested_file = os.path.abspath(os.path.join(work_dir, filename))

    # 确保请求的文件在work_dir内
    if not requested_file.startswith(abs_work_dir):
        logging.warning(f"路径遍历攻击尝试: {filename}")
        abort(403, description="禁止访问")

    # 检查文件是否存在
    if not os.path.isfile(requested_file):
        logging.warning(f"文件不存在: {requested_file}")
        abort(404, description="文件不存在")

    try:
        # 发送文件 - 直接使用 send_from_directory 从工作目录发送
        # 这样可以正确处理子目录中的文件
        return send_from_directory(work_dir, filename)
    except Exception as e:
        logging.error(f"发送文件失败: {e}")
        abort(500, description="服务器内部错误：无法读取文件")
