import re
import os
import time
from claude_agent_sdk import (
    ClaudeSDKClient,
    ClaudeAgentOptions,
    ResultMessage,
    create_sdk_mcp_server,
    tool,
)
from claude_agent_sdk.types import (
    Message,
    SystemMessage,
    AssistantMessage,
    TextBlock,
    UserMessage,
    ThinkingBlock,
    ToolUseBlock,
    ToolResultBlock,
    ToolPermissionContext,
    PermissionResultAllow,
    PermissionResultDeny,
    StreamEvent,
    HookMatcher,
    HookContext,
    HookInput,
    HookJSONOutput,
)
from typing import Dict, List, Optional, Any, Callable, Union
import logging
import json
from asgiref.sync import async_to_sync
from knowledge_manager import KnowledgeManager
import threading

# 导入配置
try:
    from ..config import Config
except ImportError:
    from config import Config

# 全局运行Agent计数器和锁
_running_agents_count = 0
_running_agents_lock = threading.RLock()


def get_running_agents_count():
    """获取当前运行的Agent数量"""
    global _running_agents_count
    with _running_agents_lock:
        return _running_agents_count


class ClaudeAgent:
    """ClaudeCode 智能体"""

    def __init__(
        self,
        system_prompt: str,
        provider_key="local",
        knowledge_manager: KnowledgeManager = None,
        permission_mode: str = "bypassPermissions",
    ):
        self.system_prompt = system_prompt
        self.knowledge_manager = knowledge_manager
        self.stop_evnet = None
        self.provider = provider_key
        self.permission_mode = permission_mode
        # 设置API超时时间 (毫秒)
        os.environ["API_TIMEOUT_MS"] = Config.API_TIMEOUT_MS
        self.set_model_config(provider_key)

    def set_model_config(self, provider_key):
        """根据provider名称设置模型配置"""
        provider_config = Config.PROVIDERS.get(provider_key, {})

        # 设置环境变量
        os.environ["ANTHROPIC_BASE_URL"] = provider_config.get("base_url", "")
        os.environ["ANTHROPIC_AUTH_TOKEN"] = provider_config.get("auth_token", "")
        os.environ["ANTHROPIC_MODEL"] = provider_config.get("model", "")
        os.environ["ANTHROPIC_API_KEY"] = provider_config.get("api_key", "")
        os.environ["CLAUDE_CODE_MAX_OUTPUT_TOKENS"] = provider_config.get("max_tokens", "15000")
        os.environ["ANTHROPIC_SMALL_FAST_MODEL"] = provider_config.get("fast_model", "")
        os.environ["ANTHROPIC_DEFAULT_SONNET_MODEL"] = provider_config.get("sonnect_model", "")
        os.environ["ANTHROPIC_DEFAULT_OPUS_MODEL"] = provider_config.get("opus_model", "")
        os.environ["ANTHROPIC_DEFAULT_HAIKU_MODEL"] = provider_config.get("haiku_model", "")

    def run_agent_sync(
        self, work_dir, user_req, session_id=None, nothink="", log_callback=None
    ) -> Dict[str, Any]:
        run_sync = async_to_sync(self.run_agent)
        return run_sync(work_dir, user_req, session_id, nothink, log_callback)

    async def run_agent(
        self, work_dir, user_req, session_id=None, nothink="", log_callback=None
    ) -> Dict[str, Any]:
        global _running_agents_count

        # 增加运行中的Agent计数
        with _running_agents_lock:
            _running_agents_count += 1
        try:
            # 重置为原始provider
            self.set_model_config(self.provider)

            while True:
                result = await self._run_agent(
                    work_dir, user_req, session_id, nothink, log_callback
                )
                if result.get("success"):
                    return result
                if self.stop_evnet:
                    return result

                logging.error(f"{self.provider}-任务执行出错: {result.get('message')}")
                session_id = result.get("session_id")

                # 检查是否有降级provider
                provider_config = Config.PROVIDERS.get(self.provider, {})
                fallback_provider = provider_config.get("fallback", "")

                if fallback_provider and fallback_provider in Config.PROVIDERS:
                    logging.info(f"切换到降级模型: {fallback_provider}")
                    self.provider = fallback_provider
                    self.set_model_config(self.provider)
                    sleep_time = int(provider_config.get("fallback_sleep", 0))
                    if sleep_time > 0:
                        logging.info(f"等待{sleep_time}秒后继续尝试")
                        import gevent

                        gevent.sleep(sleep_time)
                    continue  # 继续重试循环
                else:
                    # 没有降级模型或降级模型不存在，直接返回错误
                    return result
        finally:
            # 减少运行中的Agent计数
            with _running_agents_lock:
                _running_agents_count -= 1

    def stop(self):
        self.stop_evnet = True

    async def _run_agent(
        self, work_dir, user_req, session_id=None, nothink="", progress_callback=None
    ) -> Dict[str, Any]:

        if session_id and session_id.strip().lower() == "none":
            session_id = None

        new_session_id = session_id
        def print_message_content(message_count, title, content: Any, sys_session_id: str = None):
            if progress_callback is None:
                return
            if sys_session_id:
                new_session_id = sys_session_id
                if progress_callback.__code__.co_argcount == 4:
                    progress_callback(message_count, title, content, new_session_id)
                return
            if isinstance(content, list):
                try:
                    for block in content:
                        if isinstance(block, TextBlock):
                            progress_callback(message_count, title, f"💬 {block.text}")
                        elif isinstance(block, ThinkingBlock):
                            progress_callback(
                                message_count, title, f"🤔 {block.thinking}"
                            )
                        elif isinstance(block, ToolUseBlock):
                            input_str = f"{block.input}"  # ⏎ ¶
                            progress_callback(
                                message_count, title, f"🔧 {block.name}, {input_str}"
                            )
                        elif isinstance(block, ToolResultBlock):
                            tool_rst = f"{block.content}"
                            progress_callback(message_count, title, f"📊 {tool_rst}")
                except Exception as e:
                    progress_callback(message_count, title, f"解析消息ERROR: {e}")
            else:
                progress_callback(message_count, title, content)

        result = {"is_error": False}
        message_count = 0
        options = self._create_options(work_dir, session_id, self.knowledge_manager)

        # 在对话中跟踪使用情况
        async def track_usage(message):
            if isinstance(message, AssistantMessage) and hasattr(message, "usage"):
                print(f"消息 ID: {message['id']}")
                print(f"使用情况: {message['usage']}")

        async with ClaudeSDKClient(options) as client:
            print_message_content(0, "User", f"📝 {user_req}")
            await client.query(f"{user_req} {nothink}")
            async for message in client.receive_response():
                message_count += 1
                if self.stop_evnet:
                    await client.interrupt()
                    result["is_error"] = True
                    result["message"] = "stopped"
                    result["session_id"] = new_session_id
                    break 
                if isinstance(message, SystemMessage):
                    if message.data.get("session_id"):
                        print_message_content(message_count, "System", str(message.data), message.data.get("session_id"))
                elif isinstance(message, AssistantMessage):
                    # Print Claude's text responses
                    print_message_content(message_count, "Assistant", message.content)
                elif isinstance(message, UserMessage):
                    print_message_content(message_count, "Agent", message.content)
                elif isinstance(message, StreamEvent):
                    # Print Claude's text responses
                    for event in message.event:
                        print_message_content(message_count, "Stream", f"🌊 {event}")
                elif isinstance(message, ResultMessage):
                    print_message_content(
                        100,
                        "Result",
                        f"✅ Task completed! Duration: {message.duration_ms/1000} seconds. \n 结果：{message.result}",
                    )
                    result["is_error"] = message.is_error
                    result["message"] = f"{message.result}"
                    new_session_id = message.session_id

        result["success"] = not result["is_error"]
        result["session_id"] = new_session_id
        return result

    def _create_options(
        self, work_dir, session_id, knowledge_manager: KnowledgeManager = None
    ):

        # ******文件权限检查工具,限制只能读写work_dir文件******
        async def file_permission_handler(
            tool_name: str, input_data: dict, context: ToolPermissionContext
        ) -> PermissionResultAllow | PermissionResultDeny:
            """工具权限的自定义逻辑。"""
            # print(f"{tool_name} 权限请求")

            # 阻止读写工作目录之外的文件
            if tool_name in ["Write", "Read", "mcp__read__read_file"]:
                file_path = input_data.get("file_path", "")
                real_path = os.path.abspath(file_path)
                # real_dir = os.path.dirname(real_path)
                if not real_path.startswith(work_dir):
                    print(f"⚠️ {tool_name} 不能读写文件: {real_path}")
                    return PermissionResultDeny(
                        behavior="deny",
                        message=f"只能读写工作目录: {work_dir}.",
                        interrupt=False,
                    )
            # 允许其他所有操作
            # 返回的Allo，依然错误：Tool permission request failed: [↵   {↵     "code": "invalid_union",
            return PermissionResultAllow()

        options = ClaudeAgentOptions(
            system_prompt=self.system_prompt,
            resume=session_id,
            # max_turns=200,
            cwd=work_dir,
            setting_sources=["project"],
            permission_mode=self.permission_mode,  # "acceptEdits" if self.provider == "local" else "bypassPermissions",
            # mcp_servers=self.mcp_servers,
            disallowed_tools= Config.DISALLOWED_TOOLS,
            extra_args={
                # "verbose": None,
                # "output-format": "stream-json",
                # "mcp-debug": None,
                # "dangerously-skip-permissions": None
            },
            can_use_tool=file_permission_handler,  # bypassPermissions模式不会调用can_use_tool
            # hooks={
            #     'PreToolUse': [
            #         #HookMatcher(matcher='Bash', hooks=[validate_file_path_command]),
            #         HookMatcher(hooks=[validate_file_path_command]),  # 适用于所有工具
            #     ]
            # }
        )
        # 添加环境变量中配置的allowed_tools
        if Config.ALLOWED_TOOLS:
            options.allowed_tools.extend(Config.ALLOWED_TOOLS)
        if Config.PROVIDERS.get(self.provider, {}).get("read_tool"):
            # ******文件读取工具, 处理大文件的批量读取******
            mcp_read_file = create_sdk_mcp_server(
                name="read_file",
                version="2.0.0",
                tools=[read_file],
            )
            options.mcp_servers["read"] = mcp_read_file
            options.allowed_tools.append("mcp__read__read_file")
            options.disallowed_tools.append("Read")

        options = self._add_rag_tools(options, knowledge_manager)

        return options

    def _add_rag_tools(
        self, options: ClaudeAgentOptions, knowledge_manager: KnowledgeManager = None
    ):
        rag_tools = []
        kb = None
        # ******文档知识库检索工具******
        if knowledge_manager:
            kb = knowledge_manager.get_or_create_knowledge_base("kb_documents")
        if kb and kb.chunk_count > 0:

            @tool(
                "search_documents",
                f"""检索项目文档知识库的工具。
            当您需要了解项目知识、优化需求内容、生成设计、生成任务时，可以调用此工具搜索与项目相关的知识。
            返回值说明:
                List[Dict[str, Any]]: 多个文档块，每个文档块包含以下字段：
                    - id: 块ID
                    - content: 块内容
                    - score : 块内容的匹配得分
                    - metadata : 块的元数据
            """,
                {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "检索关键词"},
                        "limit": {"type": "integer", "description": "可返回的文档块数"},
                    },
                    "required": ["query"],
                },
            )
            async def search_documents(args: dict[str, Any]) -> List[Dict[str, Any]]:
                logging.info(f"调用search_documents工具:{args}")
                result = knowledge_manager.search_documents(
                    args.get("query"), args.get("limit", 5)
                )
                return result

            rag_tools.append(search_documents)

        # ******代码知识库检索工具******
        kb = None
        if knowledge_manager:
            kb = knowledge_manager.get_or_create_knowledge_base("kb_code")
        if kb and kb.chunk_count > 0:

            @tool(
                "search_codes",
                f"""检索项目代码库的工具。
            当您需要了解项目各功能的代码实现、参考/复用已有代码的功能实现、编写详细设计方案等场景时，可以调用此工具搜索与项目相关的代码。
            返回值说明:
                List[Dict[str, Any]]: 多个代码块，每个代码块包含以下字段：
                    - id: 块ID
                    - content: 块内容
                    - score : 块内容的匹配得分
                    - metadata : 块的元数据
            """,
                {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "检索关键词"},
                        "limit": {"type": "integer", "description": "返回的代码块数"},
                    },
                    "required": ["query"],
                },
            )
            async def search_codes(args: dict[str, Any]) -> List[Dict[str, Any]]:
                logging.info(f"调用search_codes工具:{args}")
                result = knowledge_manager.search_code(
                    args.get("query"), args.get("limit", 5)
                )
                return result

            rag_tools.append(search_codes)

        if len(rag_tools) > 0:
            mcp_rag = create_sdk_mcp_server(
                name="search_rag",
                version="2.0.0",
                tools=rag_tools,
            )
            options.mcp_servers["rag"] = mcp_rag
            options.allowed_tools.append("mcp__rag__search_rag")
        return options


@tool(
    "read_file",
    f"""读取单个文件内容的工具.
当您需要查看文件内容时，请使用此工具。它会返回文件的文本内容以及相关的元信息。
返回值说明:
      Dict[str, Any]: 包含以下键值的字典
        - total_lines (int): 文件的总行数
        - start_line (int): 本次读取的起始行号（与传入的offset相同）
        - end_line (int): 本次实际读取的结束行号
        - content (List[str]): 实际读取到的行内容列表
""",
    {
        "type": "object",
        "properties": {
            "file_path": {"type": "string", "description": "单个文件的完整路径"},
            "offset": {"type": "integer", "description": "从第几行开始读取，默认为1"},
            "maxline": {
                "type": "integer",
                "description": "最多读取多少行，默认1000,最大不能超过1000行",
            },
            "encode": {"type": "string", "description": "文件编码格式，默认为utf-8"},
        },
        "required": ["file_path"],
    },
)
async def read_file(args: dict[str, Any]) -> dict[str, Any]:
    """
    读取文件内容的工具
    """
    try:
        file_path = args.get("file_path")
        # 检查文件路径是否有空格 逗号
        if " " in file_path or "," in file_path:
            raise ValueError("只能读取一个文件")

        offset: int = args.get("offset", 1)
        maxline: int = args.get("maxline", 1000)
        if maxline > 1000:
            raise ValueError("maxline 不能超过 1000")
        encode: str = args.get("encode", "utf-8")
        if os.path.isdir(file_path):
            raise ValueError(f"{file_path} 是目录名")
        if not os.path.isfile(file_path):
            raise ValueError(f"文件不存在: {file_path}")

        with open(file_path, "r", encoding=encode) as f:
            lines = f.readlines()

        total_lines = len(lines)

        # 限制读取内容在10KB以内
        MAX_BYTES = 10 * 1024  # 20KB
        accumulated_bytes = 0
        actual_end_line = offset - 1

        start = offset - 1
        for i in range(start, min(start + maxline, total_lines)):
            line_bytes = len(lines[i].encode("utf-8"))
            if accumulated_bytes + line_bytes > MAX_BYTES:
                break
            accumulated_bytes += line_bytes
            actual_end_line = i + 1  # 转换为1基索引

        out_lines = lines[start:actual_end_line]

        result = {
            "total_lines": total_lines,
            "start_line": offset,
            "end_line": actual_end_line,
            "content": [line for line in out_lines],
        }
        return {"content": [{"type": "text", "text": f"{result}"}]}
    except Exception as e:
        return {"content": [{"type": "text", "text": f"文件读取失败：{e}"}]}


# async def validate_file_path_hook(
#     input_data: dict[str, Any],
#     tool_use_id: str | None,
#     context: HookContext
# ) -> HookJSONOutput:
#     """验证并阻止危险的 bash 命令。"""
#     if input_data['tool_name'] == 'Read' or input_data['tool_name'] == 'Write' or input_data['tool_name'] == 'mcp__read__read_file':
#
#         file_path = input_data.get("file_path", "")
#         if file_path.startswith("/system/"):
#             return {
#                 'hookSpecificOutput': {
#                     'hookEventName': 'PreToolUse',
#                     'permissionDecision': 'deny',
#                     'permissionDecisionReason': '危险命令已阻止'
#                 }
#             }
#     return {}
