FROM ubuntu:22.04
ENV DEBIAN_FRONTEND=noninteractive

#  给 tzdata 预置时区值（这里用上海）
ENV TZ=Asia/Shanghai
RUN echo "tzdata tzdata/Areas select Asia" | debconf-set-selections && \
    echo "tzdata tzdata/Zones/Asia select Shanghai" | debconf-set-selections

# 替换apt源为阿里云镜像
RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list

# 更新包列表并安装所需软件
RUN apt-get update && apt-get install -y \
    locales curl wget  zip  vim nano  net-tools netcat tzdata fonts-noto-cjk \
    cmake make maven  npm git \
    python3 python3-pip  gcc g++ openjdk-17-jdk golang-go \
    && npm install -g n && n 20 && hash -r \
    && npm install @musistudio/claude-code-router -g \
    && npm install -g @anthropic-ai/claude-code \
    && sed -i 's/# zh_CN.UTF-8 UTF-8/zh_CN.UTF-8 UTF-8/' /etc/locale.gen \
    && locale-gen zh_CN.UTF-8

ENV LANG=zh_CN.UTF-8 \
    LANGUAGE=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8

# 配置pip源为阿里云镜像
RUN  pip3 config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && pip install uv 

# 创建 ailab 用户
RUN useradd -m -s /bin/bash ailab

# 创建工作目录
WORKDIR /aicode

# 更改工作目录所有权为 ailab 用户
RUN chown -R ailab:ailab /aicode

COPY requirements.txt ./
# 安装Python依赖
RUN pip3 install -r requirements.txt

# 复制项目文件
COPY src/ ./src/
COPY static/ ./static/
COPY run_app.py ./
COPY prompts ./prompts

# 复制.env.example到/.env
COPY .env.example .env
# 复制scripts到scripts目录
COPY scripts/ ./scripts/
# 建立scripts/config.json软链接 到用户目录的: .claude-code-router/config.json
RUN mkdir /opt/data && mkdir /home/<USER>/.claude-code-router/ && ln -s /aicode/scripts/config.json /home/<USER>/.claude-code-router/config.json && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' > /etc/timezone

# 更改目录所有权
RUN chown -R ailab:ailab /opt/data && chown -R ailab:ailab /home/<USER>/.claude-code-router/

# 切换到 ailab 用户
USER ailab

# 暴露端口
EXPOSE 5005 5006

# 设置默认启动命令
CMD ["/aicode/scripts/start-server.sh"]

# docker build -t aicode:20251103 .
# 镜像导出并压缩
# docker save aicode:20251103 | gzip > aicode_20251103.tar.gz
# 测试验证
# docker run -d --name aicode -p 5005:5005 -p 5006:5006  --add-host ai.secsign.online:***********  aicode:20251103

# 镜像导入
# zcat aicode_20251103.tar.gz | docker load

# 映射数据目录运行
# docker run -d --name aicode -v /opt/data:/opt/data -p 5005:5005 -p 5006:5006 --cpus="0.5" -m="16G" --add-host ai.secsign.online:*********** --add-host git.sansec.cn:*********  aicode:20251103

# 映射代码目录运行
# docker run -d --name aicode -v /opt/aicode:/aicode -v /opt/aicode/data:/opt/data -v /opt/data/.claude:/home/<USER>/.claude -p 5005:5005 -p 5006:5006 --cpus="0.5" -m="16G" --add-host ai.secsign.online:*********** --add-host git.sansec.cn:*********  aicode:20251103

# 推送到远程镜像仓库
# /etc/docker/daemon.json 
#{
#  "insecure-registries": ["************"]
#}
# 更名并推送：
# docker tag aicode:20251103 ************/ailab/aicode:20251103
# docker push ************/ailab/aicode:20251103
# 标记为最新镜像并上传
# docker tag ************/ailab/aicode:20251103 ************/ailab/aicode:latest
# docker push ************/ailab/aicode:latest