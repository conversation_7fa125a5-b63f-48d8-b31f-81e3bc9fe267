#!/usr/bin/env python3
"""
JavaScript逆向解析工具
用于解析混淆的JavaScript代码，提升可读性
"""

import re
import json
import sys
import os
from typing import Dict, List, Tuple, Optional
import ast

class JSDeobfuscator:
    def __init__(self):
        self.variable_map = {}
        self.function_map = {}
        self.string_map = {}
        self.indent_level = 0

    def deobfuscate(self, input_file: str, output_file: str = None) -> str:
        """
        主函数：逆向解析混淆的JavaScript代码
        """
        print(f"开始解析文件: {input_file}")

        # 读取输入文件
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败: {e}")
            return ""

        return self.deobfuscate_content(content, output_file)

    def deobfuscate_content(self, content: str, output_file: str = None) -> str:
        """
        逆向解析内容
        """
        print("开始逆向解析内容...")

        # 逐步逆向解析
        result = self._preprocess(content)
        result = self._extract_strings(result)
        result = self._rename_variables(result)
        result = self._format_code(result)
        result = self._add_comments(result)

        # 输出结果
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(result)
                print(f"解析结果已保存到: {output_file}")
            except Exception as e:
                print(f"保存文件失败: {e}")

        return result

    def _preprocess(self, code: str) -> str:
        """
        预处理代码：基本清理和准备
        """
        print("预处理代码...")

        # 移除注释（保留原有注释）
        code = re.sub(r'//.*$', '', code, flags=re.MULTILINE)

        # 在分号后添加换行，初步格式化
        code = re.sub(r';', ';\n', code)

        # 在大括号后添加换行
        code = re.sub(r'\{', '{\n', code)
        code = re.sub(r'\}', '\n}\n', code)

        # 在逗号后添加空格（对象和数组中的）
        code = re.sub(r',', ', ', code)

        return code

    def _extract_strings(self, code: str) -> str:
        """
        提取和还原字符串
        """
        print("提取和还原字符串...")

        # 查找简单的字符串赋值模式
        string_patterns = [
            r'([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*["\']([^"\']*)["\']',
            r'var\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*["\']([^"\']*)["\']'
        ]

        for pattern in string_patterns:
            matches = re.finditer(pattern, code)
            for match in matches:
                var_name = match.group(1)
                string_value = match.group(2)
                if len(var_name) > 1 and len(string_value) > 0:  # 避免单字符变量
                    self.string_map[var_name] = string_value

        # 替换代码中的字符串变量
        for var_name, string_value in self.string_map.items():
            # 转义字符串中的特殊字符
            escaped_value = string_value.replace('\\', '\\\\').replace('"', '\\"')
            code = re.sub(r'\b' + re.escape(var_name) + r'\b', f'"{escaped_value}"', code)

        print(f"提取了 {len(self.string_map)} 个字符串")
        return code

    def _rename_variables(self, code: str) -> str:
        """
        重命名变量，使其更有意义
        """
        print("重命名变量...")

        # 分析变量使用模式
        variables = self._analyze_variables(code)

        # 为变量生成更有意义的名称
        for var in variables:
            if var in self.variable_map:
                continue

            # 基于使用模式生成名称
            new_name = self._generate_variable_name(var, code)
            self.variable_map[var] = new_name

        # 替换变量名
        for old_name, new_name in self.variable_map.items():
            # 使用词边界确保精确匹配
            pattern = r'\b' + re.escape(old_name) + r'\b'
            code = re.sub(pattern, new_name, code)

        print(f"重命名了 {len(self.variable_map)} 个变量")
        return code

    def _analyze_variables(self, code: str) -> List[str]:
        """
        分析代码中的变量
        """
        variables = set()

        # 查找变量声明
        patterns = [
            r'var\s+([a-zA-Z_$][a-zA-Z0-9_$]*)',
            r'let\s+([a-zA-Z_$][a-zA-Z0-9_$]*)',
            r'const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)',
            r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)',
            r'([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=',  # 赋值语句
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, code)
            for match in matches:
                var_name = match.group(1)
                if len(var_name) > 1:  # 忽略单字符变量
                    variables.add(var_name)

        return list(variables)

    def _generate_variable_name(self, var_name: str, code: str) -> str:
        """
        基于变量使用模式生成有意义的名称
        """
        # 查找变量的使用上下文
        usage_pattern = r'\b' + re.escape(var_name) + r'\b[^;]*'
        usages = re.findall(usage_pattern, code)

        # 基于使用模式推断变量类型
        for usage in usages:
            if 'function' in usage:
                return f'function_{var_name}'
            elif '=' in usage and '"' in usage:
                return f'string_{var_name}'
            elif '=' in usage and re.search(r'\d', usage):
                return f'number_{var_name}'
            elif 'require' in usage or 'import' in usage:
                return f'module_{var_name}'
            elif 'console.' in usage:
                return f'log_{var_name}'
            elif 'return' in usage:
                return f'result_{var_name}'

        # 基于变量名长度和字符模式
        if len(var_name) <= 3:
            return f'var_{var_name}'
        elif re.match(r'^[a-z]+[0-9]+$', var_name):
            return f'counter_{var_name}'
        elif re.match(r'^[A-Z][a-zA-Z]*$', var_name):
            return f'Class_{var_name}'
        else:
            return f'variable_{var_name}'

    def _format_code(self, code: str) -> str:
        """
        格式化代码，添加适当的缩进和换行
        """
        print("格式化代码...")

        lines = code.split('\n')
        formatted_lines = []
        indent_level = 0

        for line in lines:
            stripped = line.strip()
            if not stripped:
                continue

            # 减少缩进的情况
            if stripped.startswith('}') or stripped.startswith(']') or stripped.startswith(')'):
                indent_level = max(0, indent_level - 1)

            # 添加当前行
            if stripped:
                formatted_lines.append('    ' * indent_level + stripped)

            # 增加缩进的情况
            if stripped.endswith('{') or stripped.endswith('[') or stripped.endswith('('):
                indent_level += 1

        return '\n'.join(formatted_lines)

    def _add_comments(self, code: str) -> str:
        """
        添加有用的注释
        """
        print("添加注释...")

        lines = code.split('\n')
        commented_lines = []

        # 在文件开头添加头部注释
        commented_lines.append('// 逆向解析后的代码')
        commented_lines.append('// 由JavaScript逆向解析工具生成')
        commented_lines.append('// 原始代码已进行去混淆和格式化处理')
        commented_lines.append('')

        for line in lines:
            commented_lines.append(line)

            # 为特定模式添加注释
            if 'function' in line and '{' in line:
                commented_lines.append('    // 函数定义')
            elif 'require' in line:
                commented_lines.append('    // 模块导入')
            elif 'module.exports' in line:
                commented_lines.append('    // 模块导出')
            elif 'console.' in line:
                commented_lines.append('    // 调试输出')

        return '\n'.join(commented_lines)

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print("使用方法: python js_deobfuscator.py <input_file> [output_file]")
        print("示例: python js_deobfuscator.py /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude deobfuscated.js")
        return

    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None

    if not os.path.exists(input_file):
        print(f"错误: 输入文件 '{input_file}' 不存在")
        return

    # 创建逆向解析器
    deobfuscator = JSDeobfuscator()

    # 执行逆向解析
    try:
        result = deobfuscator.deobfuscate(input_file, output_file)
        if result and not output_file:
            print("\n=== 逆向解析结果 ===")
            print(result[:2000] + "..." if len(result) > 2000 else result)
    except Exception as e:
        print(f"逆向解析过程中发生错误: {e}")

if __name__ == "__main__":
    main()