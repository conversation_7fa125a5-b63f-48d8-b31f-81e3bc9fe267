#!/usr/bin/env python3
"""
Claude NodeJS逆向解析主程序
整合所有逆向解析功能，专门用于解析混淆的Claude CLI代码
"""

import os
import sys
import re
from pathlib import Path

# 导入我们的模块
from js_deobfuscator import JSDeobfuscator
from variable_restorer import VariableRestorer
from code_formatter import JavaScriptFormatter, FormatConfig

class ClaudeDeobfuscator:
    def __init__(self):
        self.deobfuscator = JSDeobfuscator()
        self.variable_restorer = VariableRestorer()
        self.formatter = JavaScriptFormatter(FormatConfig(
            indent_size=4,
            brace_style="allman",
            quote_style="single",
            max_line_length=100
        ))

    def deobfuscate_claude(self, input_path: str, output_dir: str = None) -> str:
        """
        逆向解析Claude CLI代码
        """
        print("=" * 60)
        print("Claude NodeJS逆向解析工具")
        print("=" * 60)

        if not os.path.exists(input_path):
            print(f"错误: 输入文件 '{input_path}' 不存在")
            return ""

        # 设置输出目录
        if output_dir is None:
            output_dir = os.path.join(os.path.dirname(input_path), "deobfuscated")

        os.makedirs(output_dir, exist_ok=True)

        # 生成输出文件路径
        base_name = os.path.basename(input_path)
        name_without_ext = os.path.splitext(base_name)[0]
        output_file = os.path.join(output_dir, f"{name_without_ext}_deobfuscated.js")

        print(f"输入文件: {input_path}")
        print(f"输出目录: {output_dir}")
        print(f"输出文件: {output_file}")
        print("-" * 60)

        try:
            # 读取输入文件
            print("1. 读取原始代码...")
            with open(input_path, 'r', encoding='utf-8') as f:
                original_code = f.read()

            file_size = len(original_code)
            print(f"   文件大小: {file_size:,} 字节")

            # 如果文件太大，只处理前部分用于测试
            if file_size > 1000000:  # 1MB
                print("   文件较大，将处理前1MB内容进行测试")
                original_code = original_code[:1000000]
                output_file = os.path.join(output_dir, f"{name_without_ext}_sample_deobfuscated.js")

            # 步骤1: 基础逆向解析
            print("2. 执行基础逆向解析...")
            step1_result = self.deobfuscator.deobfuscate_content(original_code)

            # 保存中间结果
            step1_file = os.path.join(output_dir, f"{name_without_ext}_step1.js")
            with open(step1_file, 'w', encoding='utf-8') as f:
                f.write(step1_result)
            print(f"   步骤1结果已保存到: {step1_file}")

            # 步骤2: 变量名还原
            print("3. 执行变量名还原...")
            step2_result = self.variable_restorer.analyze_and_restore(step1_result)

            # 保存中间结果
            step2_file = os.path.join(output_dir, f"{name_without_ext}_step2.js")
            with open(step2_file, 'w', encoding='utf-8') as f:
                f.write(step2_result)
            print(f"   步骤2结果已保存到: {step2_file}")

            # 步骤3: 代码格式化
            print("4. 执行代码格式化...")
            final_result = self.formatter.format(step2_result)

            # 保存最终结果
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(final_result)

            print("-" * 60)
            print("5. 逆向解析完成!")
            print(f"最终输出文件: {output_file}")

            # 生成分析报告
            self._generate_analysis_report(original_code, final_result, output_dir, name_without_ext)

            # 显示预览
            self._show_preview(final_result)

            return final_result

        except Exception as e:
            print(f"逆向解析过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return ""

    def _generate_analysis_report(self, original: str, deobfuscated: str, output_dir: str, base_name: str):
        """
        生成分析报告
        """
        report_file = os.path.join(output_dir, f"{base_name}_analysis_report.txt")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Claude NodeJS逆向解析分析报告\n")
            f.write("=" * 50 + "\n\n")

            # 文件信息
            f.write("## 文件信息\n")
            f.write(f"原始文件大小: {len(original):,} 字节\n")
            f.write(f"解析后文件大小: {len(deobfuscated):,} 字节\n")
            f.write(f"大小变化: {((len(deobfuscated) - len(original)) / len(original) * 100):+.1f}%\n\n")

            # 代码统计
            f.write("## 代码统计\n")
            original_lines = len(original.split('\n'))
            deobfuscated_lines = len(deobfuscated.split('\n'))
            f.write(f"原始代码行数: {original_lines:,}\n")
            f.write(f"解析后行数: {deobfuscated_lines:,}\n")
            f.write(f"行数变化: {deobfuscated_lines - original_lines:+,}\n\n")

            # 关键字统计
            f.write("## 关键字统计\n")
            keywords = ['function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'return', 'class']
            for keyword in keywords:
                original_count = len(re.findall(r'\b' + keyword + r'\b', original))
                deobfuscated_count = len(re.findall(r'\b' + keyword + r'\b', deobfuscated))
                f.write(f"{keyword}: {original_count} -> {deobfuscated_count}\n")

            f.write("\n## 解析步骤\n")
            f.write("1. 基础逆向解析 - 移除基本混淆，提取字符串\n")
            f.write("2. 变量名还原 - 分析变量使用模式，还原有意义的变量名\n")
            f.write("3. 代码格式化 - 美化代码格式，添加适当的缩进和换行\n\n")

            f.write("## 注意事项\n")
            f.write("- 本工具仅用于学习和研究目的\n")
            f.write("- 解析后的代码可能不完全等同于原始源代码\n")
            f.write("- 某些高级混淆技术可能需要手动处理\n")
            f.write("- 请遵守相关法律法规和软件许可协议\n")

        print(f"分析报告已保存到: {report_file}")

    def _show_preview(self, code: str, lines: int = 50):
        """
        显示解析结果预览
        """
        print("\n" + "=" * 60)
        print("解析结果预览:")
        print("=" * 60)

        code_lines = code.split('\n')
        preview_lines = min(lines, len(code_lines))

        for i, line in enumerate(code_lines[:preview_lines]):
            print(f"{i+1:3d}: {line}")

        if len(code_lines) > preview_lines:
            print(f"... (还有 {len(code_lines) - preview_lines} 行)")

        print("=" * 60)

def main():
    """
    主函数
    """
    if len(sys.argv) < 2:
        print("Claude NodeJS逆向解析工具")
        print("使用方法: python deobfuscate_claude.py <input_file> [output_dir]")
        print("\n示例:")
        print("  python deobfuscate_claude.py /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude")
        print("  python deobfuscate_claude.py /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude ./output")
        print("\n注意:")
        print("- 此工具仅用于学习和研究目的")
        print("- 请遵守相关法律法规和软件许可协议")
        print("- 不要将此工具用于非法用途")
        return

    input_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None

    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 '{input_file}' 不存在")
        return

    # 创建逆向解析器并执行
    deobfuscator = ClaudeDeobfuscator()
    result = deobfuscator.deobfuscate_claude(input_file, output_dir)

    if result:
        print("\n逆向解析成功完成!")
        print("请查看输出目录中的文件:")
        print("- *_deobfuscated.js: 最终解析结果")
        print("- *_step1.js: 基础逆向解析结果")
        print("- *_step2.js: 变量名还原结果")
        print("- *_analysis_report.txt: 详细分析报告")
    else:
        print("\n逆向解析失败，请检查错误信息")

if __name__ == "__main__":
    main()