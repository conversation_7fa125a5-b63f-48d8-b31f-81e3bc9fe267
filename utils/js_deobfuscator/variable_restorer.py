#!/usr/bin/env python3
"""
高级变量名还原工具
专门用于分析和还原JavaScript混淆代码中的变量名
"""

import re
import json
from typing import Dict, List, Tuple, Set
from collections import defaultdict

class VariableRestorer:
    def __init__(self):
        self.variable_usage = defaultdict(list)
        self.variable_types = {}
        self.function_signatures = {}
        self.import_map = {}
        self.export_map = {}

    def analyze_and_restore(self, code: str) -> str:
        """
        分析代码并还原变量名
        """
        print("开始高级变量名还原...")

        # 分析代码结构
        self._analyze_imports(code)
        self._analyze_exports(code)
        self._analyze_functions(code)
        self._analyze_variable_usage(code)

        # 生成新的变量名映射
        name_mapping = self._generate_name_mapping()

        # 应用变量名替换
        restored_code = self._apply_name_mapping(code, name_mapping)

        print(f"还原了 {len(name_mapping)} 个变量名")
        return restored_code

    def _analyze_imports(self, code: str):
        """
        分析import/require语句
        """
        print("分析模块导入...")

        # 查找require语句
        require_pattern = r'(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*require\s*\(\s*["\']([^"\']*)["\']\s*\)'
        matches = re.finditer(require_pattern, code)

        for match in matches:
            var_name = match.group(1)
            module_path = match.group(2)
            module_name = self._extract_module_name(module_path)
            self.import_map[var_name] = {
                'path': module_path,
                'name': module_name,
                'type': 'require'
            }

        # 查找import语句
        import_patterns = [
            r'import\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s+from\s+["\']([^"\']*)["\']',
            r'import\s+\{([^}]+)\}\s+from\s+["\']([^"\']*)["\']',
            r'import\s+\*\s+as\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s+from\s+["\']([^"\']*)["\']'
        ]

        for pattern in import_patterns:
            matches = re.finditer(pattern, code)
            for match in matches:
                if pattern == import_patterns[0]:  # default import
                    var_name = match.group(1)
                    module_path = match.group(2)
                    module_name = self._extract_module_name(module_path)
                    self.import_map[var_name] = {
                        'path': module_path,
                        'name': module_name,
                        'type': 'default_import'
                    }
                elif pattern == import_patterns[1]:  # named import
                    imports = match.group(1)
                    module_path = match.group(2)
                    module_name = self._extract_module_name(module_path)
                    for imp in imports.split(','):
                        imp = imp.strip().split(' as ')
                        original_name = imp[0].strip()
                        alias = imp[1].strip() if len(imp) > 1 else original_name
                        self.import_map[alias] = {
                            'path': module_path,
                            'name': module_name,
                            'original': original_name,
                            'type': 'named_import'
                        }
                elif pattern == import_patterns[2]:  # namespace import
                    var_name = match.group(1)
                    module_path = match.group(2)
                    module_name = self._extract_module_name(module_path)
                    self.import_map[var_name] = {
                        'path': module_path,
                        'name': module_name,
                        'type': 'namespace_import'
                    }

        print(f"发现 {len(self.import_map)} 个模块导入")

    def _analyze_exports(self, code: str):
        """
        分析export语句
        """
        print("分析模块导出...")

        # 查找module.exports
        exports_pattern = r'module\.exports\s*=\s*([a-zA-Z_$][a-zA-Z0-9_$]*)'
        matches = re.finditer(exports_pattern, code)

        for match in matches:
            var_name = match.group(1)
            self.export_map[var_name] = {
                'type': 'main_export'
            }

        # 查找exports.xxx
        named_exports_pattern = r'exports\.([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*([a-zA-Z_$][a-zA-Z0-9_$]*)'
        matches = re.finditer(named_exports_pattern, code)

        for match in matches:
            export_name = match.group(1)
            var_name = match.group(2)
            self.export_map[var_name] = {
                'name': export_name,
                'type': 'named_export'
            }

        print(f"发现 {len(self.export_map)} 个模块导出")

    def _analyze_functions(self, code: str):
        """
        分析函数定义
        """
        print("分析函数定义...")

        # 查找函数声明
        func_decl_pattern = r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(([^)]*)\)'
        matches = re.finditer(func_decl_pattern, code)

        for match in matches:
            func_name = match.group(1)
            params = [p.strip() for p in match.group(2).split(',') if p.strip()]
            self.function_signatures[func_name] = {
                'type': 'declaration',
                'params': params,
                'param_count': len(params)
            }

        # 查找函数表达式
        func_expr_pattern = r'(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*(?:async\s+)?function\s*\(([^)]*)\)'
        matches = re.finditer(func_expr_pattern, code)

        for match in matches:
            func_name = match.group(1)
            params = [p.strip() for p in match.group(2).split(',') if p.strip()]
            self.function_signatures[func_name] = {
                'type': 'expression',
                'params': params,
                'param_count': len(params)
            }

        # 查找箭头函数
        arrow_func_pattern = r'(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\(([^)]*)\)\s*=>'
        matches = re.finditer(arrow_func_pattern, code)

        for match in matches:
            func_name = match.group(1)
            params = [p.strip() for p in match.group(2).split(',') if p.strip()]
            self.function_signatures[func_name] = {
                'type': 'arrow',
                'params': params,
                'param_count': len(params)
            }

        print(f"发现 {len(self.function_signatures)} 个函数定义")

    def _analyze_variable_usage(self, code: str):
        """
        分析变量使用模式
        """
        print("分析变量使用模式...")

        # 查找所有变量名（2-4个字符的，可能是混淆的）
        var_pattern = r'\b([a-zA-Z_$][a-zA-Z0-9_$]{1,3})\b'
        matches = re.finditer(var_pattern, code)

        for match in matches:
            var_name = match.group(1)
            # 获取变量使用的上下文
            start = max(0, match.start() - 100)
            end = min(len(code), match.end() + 100)
            context = code[start:end]
            self.variable_usage[var_name].append(context)

        # 推断变量类型
        for var_name, contexts in self.variable_usage.items():
            self.variable_types[var_name] = self._infer_variable_type(var_name, contexts)

        print(f"分析了 {len(self.variable_usage)} 个变量的使用模式")

    def _infer_variable_type(self, var_name: str, contexts: List[str]) -> str:
        """
        推断变量类型
        """
        type_indicators = {
            'function': ['function', '=>', 'return', '(', ')'],
            'string': ['"', "'", '`', 'toString', 'charAt', 'substring'],
            'number': ['Number', 'parseInt', 'parseFloat', '+', '-', '*', '/'],
            'boolean': ['true', 'false', 'Boolean', '&&', '||', '!'],
            'array': ['[', ']', 'push', 'pop', 'forEach', 'map', 'filter'],
            'object': ['{', '}', '.', 'Object', 'keys', 'values'],
            'promise': ['Promise', 'then', 'catch', 'async', 'await'],
            'class': ['class', 'new', 'extends', 'this'],
            'module': ['require', 'import', 'export', 'module']
        }

        type_scores = defaultdict(int)

        for context in contexts:
            for type_name, indicators in type_indicators.items():
                for indicator in indicators:
                    if indicator in context:
                        type_scores[type_name] += 1

        # 返回得分最高的类型
        if type_scores:
            return max(type_scores.items(), key=lambda x: x[1])[0]
        return 'unknown'

    def _generate_name_mapping(self) -> Dict[str, str]:
        """
        生成变量名映射
        """
        print("生成变量名映射...")

        name_mapping = {}

        # 处理导入的模块
        for var_name, info in self.import_map.items():
            if info['type'] == 'require':
                name_mapping[var_name] = f"{info['name']}_module"
            elif info['type'] == 'default_import':
                name_mapping[var_name] = f"{info['name']}"
            elif info['type'] == 'named_import':
                name_mapping[var_name] = f"{info['original']}"
            elif info['type'] == 'namespace_import':
                name_mapping[var_name] = f"{info['name']}_namespace"

        # 处理导出的变量
        for var_name, info in self.export_map.items():
            if var_name not in name_mapping:
                if info['type'] == 'main_export':
                    name_mapping[var_name] = "main_export"
                else:
                    name_mapping[var_name] = f"export_{info['name']}"

        # 处理函数
        for func_name, info in self.function_signatures.items():
            if func_name not in name_mapping:
                if info['type'] == 'declaration':
                    name_mapping[func_name] = f"function_{func_name}"
                elif info['type'] == 'expression':
                    name_mapping[func_name] = f"func_{func_name}"
                elif info['type'] == 'arrow':
                    name_mapping[func_name] = f"arrow_{func_name}"

        # 处理其他变量
        for var_name, var_type in self.variable_types.items():
            if var_name not in name_mapping and len(var_name) <= 4:
                if var_type == 'function':
                    name_mapping[var_name] = f"func_{var_name}"
                elif var_type == 'string':
                    name_mapping[var_name] = f"str_{var_name}"
                elif var_type == 'number':
                    name_mapping[var_name] = f"num_{var_name}"
                elif var_type == 'boolean':
                    name_mapping[var_name] = f"bool_{var_name}"
                elif var_type == 'array':
                    name_mapping[var_name] = f"arr_{var_name}"
                elif var_type == 'object':
                    name_mapping[var_name] = f"obj_{var_name}"
                elif var_type == 'promise':
                    name_mapping[var_name] = f"promise_{var_name}"
                elif var_type == 'class':
                    name_mapping[var_name] = f"Class{var_name.upper()}"
                elif var_type == 'module':
                    name_mapping[var_name] = f"module_{var_name}"
                else:
                    name_mapping[var_name] = f"var_{var_name}"

        return name_mapping

    def _apply_name_mapping(self, code: str, name_mapping: Dict[str, str]) -> str:
        """
        应用变量名映射
        """
        print("应用变量名映射...")

        # 按变量名长度排序，优先替换长变量名避免冲突
        sorted_vars = sorted(name_mapping.keys(), key=len, reverse=True)

        for old_name in sorted_vars:
            new_name = name_mapping[old_name]
            # 使用词边界确保精确匹配
            pattern = r'\b' + re.escape(old_name) + r'\b'
            code = re.sub(pattern, new_name, code)

        return code

    def _extract_module_name(self, path: str) -> str:
        """
        从模块路径提取模块名
        """
        # 移除路径扩展名
        name = path.split('/')[-1].split('.')[0]

        # 处理特殊路径
        if path.startswith('node:'):
            return f"node_{name}"
        elif path.startswith('./'):
            return f"local_{name}"
        elif path.startswith('../'):
            return f"parent_{name}"
        elif '/' in path:
            return f"pkg_{name}"
        else:
            return name

def main():
    """
    测试函数
    """
    # 这里可以添加测试代码
    pass

if __name__ == "__main__":
    main()