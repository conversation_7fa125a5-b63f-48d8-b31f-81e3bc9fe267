# JavaScript逆向解析工具集

本工具集专门用于逆向解析混淆的JavaScript代码，特别是Node.js应用程序。

## 工具概览

### 1. 主程序

- **`deobfuscate_claude.py`** - 完整的逆向解析主程序
- **`simple_deobfuscator.py`** - 简化版逆向解析工具

### 2. 核心模块

- **`js_deobfuscator.py`** - 基础逆向解析模块
- **`variable_restorer.py`** - 变量名还原模块
- **`code_formatter.py`** - 代码格式化模块

## 使用方法

### 简单使用（推荐）

```bash
python debug/simple_deobfuscator.py <input_file>
```

示例：
```bash
python debug/simple_deobfuscator.py /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude
```

### 完整使用

```bash
python debug/deobfuscate_claude.py <input_file> [output_dir]
```

示例：
```bash
python debug/deobfuscate_claude.py /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude ./output
```

## 功能特性

### 1. 基础逆向解析
- 移除压缩格式，添加基本换行和空格
- 提取和还原字符串
- 基本的变量重命名

### 2. 变量名还原
- 分析变量使用模式
- 基于上下文推断变量类型
- 生成有意义的变量名

### 3. 代码格式化
- 智能缩进调整
- 美化代码结构
- 添加注释和说明

## 解析结果

### 输出文件

- `*_deobfuscated.js` - 最终解析结果
- `*_step1.js` - 基础逆向解析结果
- `*_step2.js` - 变量名还原结果
- `*_analysis_report.txt` - 详细分析报告

### 代码分析

工具会自动分析并报告：
- 关键字统计
- 模块导入信息
- 字符串内容
- 代码结构信息

## 示例输出

```
============================================================
Claude NodeJS逆向解析工具
============================================================
输入文件: /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude
文件大小: 9,783,876 字节
============================================================

1. 读取原始代码...
2. 执行基础逆向解析...
3. 执行变量名还原...
4. 执行代码格式化...
5. 逆向解析完成!

分析代码结构...
关键字统计:
  function: 1423
  var: 1541
  let: 871
  if: 1980
  else: 321
  for: 202
  while: 109
  return: 2071
  class: 52
  import: 39

导入的模块 (1个):
  - util

发现的字符串 (7834个):
  - "re hiring! https://job-boards.greenhouse.io/ant..."
  ... 还有 7829 个字符串
```

## 注意事项

### 法律声明

⚠️ **重要提醒**:
- 本工具仅用于学习和研究目的
- 请遵守相关法律法规和软件许可协议
- 不要将此工具用于非法用途
- 尊重软件的知识产权

### 技术限制

- 高度混淆的代码可能需要手动调整
- 某些高级混淆技术可能无法完全解析
- 解析结果可能与原始源代码存在差异
- 大文件处理时可能只处理部分内容

### 使用建议

1. **先使用简化版本**: 使用`simple_deobfuscator.py`进行初步解析
2. **查看分析报告**: 了解代码的整体结构
3. **逐步分析**: 从关键函数和变量开始分析
4. **结合其他工具**: 使用专业的JavaScript分析工具
5. **动态分析**: 结合运行时分析理解代码逻辑

---

**免责声明**: 本工具仅用于教育和研究目的。使用者应当遵守相关法律法规，不得将本工具用于任何非法用途。开发者不对工具的误用或滥用承担责任。
