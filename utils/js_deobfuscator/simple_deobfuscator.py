#!/usr/bin/env python3
"""
简化的JavaScript逆向解析工具
专门处理高度压缩和混淆的JavaScript代码
"""

import re
import os

def simple_deobfuscate(input_file: str, output_file: str = None) -> str:
    """
    简化的逆向解析函数
    """
    print(f"开始解析文件: {input_file}")

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return ""

    print(f"文件大小: {len(content):,} 字节")

    # 如果文件太大，只处理部分内容
    if len(content) > 500000:  # 500KB
        print("文件较大，处理前500KB内容进行演示")
        content = content[:500000]
        if output_file:
            output_file = output_file.replace('.js', '_sample.js')

    print("开始基础格式化...")

    # 步骤1: 基础格式化
    formatted = basic_format(content)

    # 步骤2: 美化
    beautified = beautify_code(formatted)

    # 保存结果
    if output_file:
        try:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(beautified)
            print(f"解析结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存文件失败: {e}")

    # 显示预览
    show_preview(beautified)

    return beautified

def basic_format(code: str) -> str:
    """
    基础格式化
    """
    print("执行基础格式化...")

    # 在分号后添加换行
    code = re.sub(r';', ';\n', code)

    # 在大括号周围添加换行
    code = re.sub(r'\{', '{\n', code)
    code = re.sub(r'\}', '\n}\n', code)

    # 在逗号后添加空格（如果不是函数调用）
    code = re.sub(r',(?![^\)]*\))', ',\n', code)

    # 移除连续空行
    code = re.sub(r'\n\s*\n\s*\n', '\n\n', code)

    return code

def beautify_code(code: str) -> str:
    """
    美化代码
    """
    print("美化代码...")

    lines = code.split('\n')
    beautified_lines = []
    indent_level = 0

    for line in lines:
        stripped = line.strip()
        if not stripped:
            continue

        # 计算缩进变化
        indent_change = calculate_indent_change(stripped)

        # 减少缩进
        if indent_change < 0:
            indent_level = max(0, indent_level + indent_change)

        # 添加当前行
        beautified_lines.append('    ' * indent_level + stripped)

        # 增加缩进
        if indent_change > 0:
            indent_level += indent_change

    result = '\n'.join(beautified_lines)

    # 添加注释头部
    header = """/*
 * 逆向解析后的代码
 * 由JavaScript逆向解析工具生成
 *
 * 原始代码分析:
 * - 高度压缩的JavaScript代码
 * - 变量名被混淆为短标识符
 * - 包含复杂的模块导入逻辑
 *
 * 解析步骤:
 * 1. 基础格式化 - 添加换行和空格
 * 2. 缩进调整 - 恢复代码结构
 * 3. 注释添加 - 标记重要代码段
 */

"""

    return header + result

def calculate_indent_change(line: str) -> int:
    """
    计算行的缩进变化
    """
    # 以 } 开头或结尾的行减少缩进
    if line.startswith('}'):
        return -1
    elif line.endswith('}'):
        return -1
    # 以 { 结尾的行增加缩进
    elif line.endswith('{'):
        return 1
    # 以 [ 或 ( 结尾的行可能需要后续增加缩进
    elif line.endswith('[') or line.endswith('('):
        return 1
    else:
        return 0

def show_preview(code: str, lines: int = 30):
    """
    显示代码预览
    """
    print("\n" + "="*60)
    print("解析结果预览:")
    print("="*60)

    code_lines = code.split('\n')
    preview_lines = min(lines, len(code_lines))

    for i, line in enumerate(code_lines[:preview_lines]):
        print(f"{i+1:3d}: {line}")

    if len(code_lines) > preview_lines:
        print(f"... (还有 {len(code_lines) - preview_lines} 行)")

    print("="*60)

def analyze_code_structure(code: str):
    """
    分析代码结构
    """
    print("\n分析代码结构...")

    # 统计关键字
    keywords = ['function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'return', 'class', 'import', 'export']
    print("关键字统计:")
    for keyword in keywords:
        count = len(re.findall(r'\b' + keyword + r'\b', code))
        if count > 0:
            print(f"  {keyword}: {count}")

    # 查找导入的模块
    imports = re.findall(r'require\s*\(\s*["\']([^"\']+)["\']\s*\)', code)
    if imports:
        print(f"\n导入的模块 ({len(imports)}个):")
        for imp in imports[:10]:  # 只显示前10个
            print(f"  - {imp}")
        if len(imports) > 10:
            print(f"  ... 还有 {len(imports) - 10} 个模块")

    # 查找字符串
    strings = re.findall(r'["\']([^"\']{10,})["\']', code)  # 长度大于10的字符串
    if strings:
        print(f"\n发现的字符串 ({len(strings)}个):")
        for s in strings[:5]:  # 只显示前5个
            print(f"  - \"{s[:50]}{'...' if len(s) > 50 else ''}\"")
        if len(strings) > 5:
            print(f"  ... 还有 {len(strings) - 5} 个字符串")

def main():
    """
    主函数
    """
    import sys

    if len(sys.argv) < 2:
        print("简化JavaScript逆向解析工具")
        print("使用方法: python simple_deobfuscator.py <input_file> [output_file]")
        print("\n示例:")
        print("  python simple_deobfuscator.py /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude")
        print("  python simple_deobfuscator.js input.js output.js")
        print("\n注意:")
        print("- 此工具仅用于学习和研究目的")
        print("- 请遵守相关法律法规和软件许可协议")
        return

    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None

    if not os.path.exists(input_file):
        print(f"错误: 输入文件 '{input_file}' 不存在")
        return

    # 如果没有指定输出文件，自动生成
    if not output_file:
        output_dir = os.path.join(os.path.dirname(input_file), "deobfuscated_simple")
        os.makedirs(output_dir, exist_ok=True)
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = os.path.join(output_dir, f"{base_name}_formatted.js")

    # 执行逆向解析
    result = simple_deobfuscate(input_file, output_file)

    if result:
        # 分析代码结构
        analyze_code_structure(result)

        print("\n逆向解析完成!")
        print(f"输出文件: {output_file}")
        print("\n建议后续步骤:")
        print("1. 手动分析关键函数和变量")
        print("2. 使用更专业的工具进行深度逆向解析")
        print("3. 结合动态分析理解代码逻辑")
    else:
        print("逆向解析失败")

if __name__ == "__main__":
    main()