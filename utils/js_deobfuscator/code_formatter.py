#!/usr/bin/env python3
"""
JavaScript代码格式化和美化工具
专门用于处理混淆的JavaScript代码，使其具有良好的可读性
"""

import re
import json
from typing import List, Tuple
from dataclasses import dataclass

@dataclass
class FormatConfig:
    """格式化配置"""
    indent_size: int = 4
    max_line_length: int = 120
    brace_style: str = "allman"  # allman, k&r
    quote_style: str = "single"  # single, double
    keep_comments: bool = True
    add_semicolons: bool = True
    space_before_paren: bool = True
    space_in_paren: bool = False
    object_indent: bool = True

class JavaScriptFormatter:
    def __init__(self, config: FormatConfig = None):
        self.config = config or FormatConfig()
        self.indent_char = ' '
        self.current_indent = 0
        self.bracket_stack = []
        self.paren_stack = []

    def format(self, code: str) -> str:
        """
        格式化JavaScript代码
        """
        print("开始格式化代码...")

        # 预处理
        code = self._preprocess(code)

        # 分词
        tokens = self._tokenize(code)

        # 格式化
        formatted_code = self._format_tokens(tokens)

        # 后处理
        formatted_code = self._postprocess(formatted_code)

        print("代码格式化完成")
        return formatted_code

    def _preprocess(self, code: str) -> str:
        """
        预处理代码
        """
        # 标准化换行符
        code = code.replace('\r\n', '\n').replace('\r', '\n')

        # 移除多余空行但保留一些结构
        code = re.sub(r'\n\s*\n\s*\n', '\n\n', code)

        return code

    def _tokenize(self, code: str) -> List[Tuple[str, str]]:
        """
        将代码分词
        """
        tokens = []
        i = 0
        n = len(code)

        while i < n:
            char = code[i]

            # 跳过空白字符
            if char.isspace():
                if char == '\n':
                    tokens.append(('newline', char))
                i += 1
                continue

            # 字符串
            if char in ['"', "'", '`']:
                token, i = self._read_string(code, i)
                tokens.append(token)
                continue

            # 注释
            if char == '/' and i + 1 < n:
                next_char = code[i + 1]
                if next_char == '/':
                    token, i = self._read_line_comment(code, i)
                    tokens.append(token)
                    continue
                elif next_char == '*':
                    token, i = self._read_block_comment(code, i)
                    tokens.append(token)
                    continue

            # 数字
            if char.isdigit():
                token, i = self._read_number(code, i)
                tokens.append(token)
                continue

            # 标识符和关键字
            if char.isalpha() or char == '_' or char == '$':
                token, i = self._read_identifier(code, i)
                tokens.append(token)
                continue

            # 运算符和标点符号
            token, i = self._read_operator(code, i)
            tokens.append(token)

        return tokens

    def _read_string(self, code: str, start: int) -> Tuple[Tuple[str, str], int]:
        """
        读取字符串
        """
        quote = code[start]
        i = start + 1
        n = len(code)
        escape = False

        while i < n:
            char = code[i]
            if escape:
                escape = False
            elif char == '\\':
                escape = True
            elif char == quote:
                i += 1
                break
            i += 1

        return (('string', code[start:i]), i)

    def _read_line_comment(self, code: str, start: int) -> Tuple[Tuple[str, str], int]:
        """
        读取单行注释
        """
        i = start + 2
        n = len(code)

        while i < n and code[i] != '\n':
            i += 1

        return (('comment', code[start:i]), i)

    def _read_block_comment(self, code: str, start: int) -> Tuple[Tuple[str, str], int]:
        """
        读取块注释
        """
        i = start + 2
        n = len(code)

        while i + 1 < n and not (code[i] == '*' and code[i + 1] == '/'):
            i += 1

        if i + 1 < n:
            i += 2

        return (('comment', code[start:i]), i)

    def _read_number(self, code: str, start: int) -> Tuple[Tuple[str, str], int]:
        """
        读取数字
        """
        i = start
        n = len(code)

        # 整数部分
        while i < n and code[i].isdigit():
            i += 1

        # 小数部分
        if i < n and code[i] == '.':
            i += 1
            while i < n and code[i].isdigit():
                i += 1

        # 科学计数法
        if i < n and code[i] in ['e', 'E']:
            i += 1
            if i < n and code[i] in ['+', '-']:
                i += 1
            while i < n and code[i].isdigit():
                i += 1

        return (('number', code[start:i]), i)

    def _read_identifier(self, code: str, start: int) -> Tuple[Tuple[str, str], int]:
        """
        读取标识符
        """
        i = start
        n = len(code)

        while i < n and (code[i].isalnum() or code[i] == '_' or code[i] == '$'):
            i += 1

        identifier = code[start:i]

        # 检查是否是关键字
        keywords = {
            'break', 'case', 'catch', 'class', 'const', 'continue', 'debugger',
            'default', 'delete', 'do', 'else', 'export', 'extends', 'false',
            'finally', 'for', 'function', 'if', 'import', 'in', 'instanceof',
            'let', 'new', 'null', 'return', 'super', 'switch', 'this', 'throw',
            'true', 'try', 'typeof', 'var', 'void', 'while', 'with', 'yield'
        }

        token_type = 'keyword' if identifier in keywords else 'identifier'
        return ((token_type, identifier), i)

    def _read_operator(self, code: str, start: int) -> Tuple[Tuple[str, str], int]:
        """
        读取运算符
        """
        # 多字符运算符
        operators = [
            '===', '!==', '==>', '=>', '>>>', '>>', '<<', '++', '--', '&&', '||',
            '+=', '-=', '*=', '/=', '%=', '&=', '|=', '^=', '>=', '<=', '!=', '=='
        ]

        i = start + 1
        n = len(code)

        # 检查多字符运算符
        while i <= n:
            op = code[start:i]
            if op in operators:
                i += 1
            else:
                i -= 1
                break

        if i > start + 1:
            op = code[start:i]
        else:
            op = code[start]
            i = start + 1

        # 确定运算符类型
        if op in '{[()]}':
            return (('bracket', op), i)
        elif op in ',.;:':
            return (('punctuation', op), i)
        else:
            return (('operator', op), i)

    def _format_tokens(self, tokens: List[Tuple[str, str]]) -> str:
        """
        格式化token列表
        """
        output = []
        i = 0
        n = len(tokens)

        while i < n:
            token_type, token_value = tokens[i]

            if token_type == 'newline':
                output.append('\n')
            elif token_type == 'comment':
                output.append(self._format_comment(token_value))
            elif token_type == 'keyword':
                output.append(self._format_keyword(token_value, tokens, i))
            elif token_type == 'identifier':
                output.append(self._format_identifier(token_value, tokens, i))
            elif token_type == 'string':
                output.append(self._format_string(token_value))
            elif token_type == 'number':
                output.append(token_value)
            elif token_type == 'bracket':
                formatted, i = self._format_bracket(token_value, tokens, i)
                output.append(formatted)
            elif token_type == 'punctuation':
                output.append(self._format_punctuation(token_value, tokens, i))
            elif token_type == 'operator':
                output.append(self._format_operator(token_value, tokens, i))

            i += 1

        return ''.join(output)

    def _format_comment(self, comment: str) -> str:
        """
        格式化注释
        """
        if comment.startswith('//'):
            return f"{'    ' * self.current_indent}{comment}"
        elif comment.startswith('/*'):
            lines = comment.split('\n')
            formatted_lines = []
            for line in lines:
                if line.strip():
                    formatted_lines.append(f"{'    ' * self.current_indent}{line}")
                else:
                    formatted_lines.append('')
            return '\n'.join(formatted_lines)
        return comment

    def _format_keyword(self, keyword: str, tokens: List[Tuple[str, str]], index: int) -> str:
        """
        格式化关键字
        """
        # 在某些关键字前添加换行
        if keyword in ['function', 'class', 'if', 'else', 'for', 'while', 'try', 'catch', 'finally']:
            if index > 0 and tokens[index - 1][0] != 'newline':
                return f"\n{'    ' * self.current_indent}{keyword}"

        # 在function关键字后控制空格
        if keyword == 'function':
            next_token = tokens[index + 1] if index + 1 < len(tokens) else None
            if next_token and next_token[0] == 'identifier':
                return f"{keyword} "

        return keyword

    def _format_identifier(self, identifier: str, tokens: List[Tuple[str, str]], index: int) -> str:
        """
        格式化标识符
        """
        return identifier

    def _format_string(self, string: str) -> str:
        """
        格式化字符串
        """
        if self.config.quote_style == 'single' and string.startswith('"'):
            string = string.replace('"', "'")
        elif self.config.quote_style == 'double' and string.startswith("'"):
            string = string.replace("'", '"')
        return string

    def _format_bracket(self, bracket: str, tokens: List[Tuple[str, str]], index: int) -> Tuple[str, int]:
        """
        格式化括号
        """
        if bracket in ['{', '[', '(']:
            self.bracket_stack.append(bracket)

            if bracket == '{':
                if self.config.brace_style == "allman":
                    result = f"\n{'    ' * self.current_indent}{bracket}\n"
                    self.current_indent += 1
                    result += f"{'    ' * self.current_indent}"
                else:  # K&R style
                    result = f" {bracket}\n"
                    self.current_indent += 1
                    result += f"{'    ' * self.current_indent}"
            else:
                result = bracket

            return result, index

        elif bracket in ['}', ']', ')']:
            if self.bracket_stack:
                self.bracket_stack.pop()

            if bracket == '}':
                self.current_indent = max(0, self.current_indent - 1)
                result = f"\n{'    ' * self.current_indent}{bracket}"
            else:
                result = bracket

            return result, index

        return bracket, index

    def _format_punctuation(self, punct: str, tokens: List[Tuple[str, str]], index: str) -> str:
        """
        格式化标点符号
        """
        if punct == ';':
            return ';'
        elif punct == ',':
            return ', '
        elif punct == ':':
            # 检查是否是三元运算符或对象属性
            next_token = tokens[index + 1] if index + 1 < len(tokens) else None
            if next_token and next_token[0] in ['string', 'number', 'identifier']:
                return ': '
            return ':'
        return punct

    def _format_operator(self, op: str, tokens: List[Tuple[str, str]], index: int) -> str:
        """
        格式化运算符
        """
        # 在运算符周围添加空格
        if op in ['=', '+', '-', '*', '/', '%', '==', '===', '!=', '!==', '<', '>', '<=', '>=']:
            return f" {op} "
        elif op in ['&&', '||', '+=', '-=', '*=', '/=', '%=']:
            return f" {op} "
        elif op in ['++', '--']:
            return op
        else:
            return f" {op} "

    def _postprocess(self, code: str) -> str:
        """
        后处理代码
        """
        # 移除多余的空行
        code = re.sub(r'\n\s*\n\s*\n', '\n\n', code)

        # 确保文件末尾有换行符
        if not code.endswith('\n'):
            code += '\n'

        return code

def main():
    """
    测试函数
    """
    config = FormatConfig(
        indent_size=4,
        brace_style="allman",
        quote_style="single"
    )

    formatter = JavaScriptFormatter(config)

    # 测试代码
    test_code = """
    function test(a,b){if(a>b){return a;}else{return b;}}
    """

    formatted = formatter.format(test_code)
    print("格式化结果:")
    print(formatted)

if __name__ == "__main__":
    main()