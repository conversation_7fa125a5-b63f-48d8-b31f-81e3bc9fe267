# JavaScript逆向解析工具使用指南

本指南详细介绍如何使用utils目录下的JavaScript逆向解析工具。

## 📁 目录结构

```
utils/
├── README.md                    # Utils工具集总览
├── run_deobfuscator.py          # 统一启动脚本 ⭐推荐使用
├── USAGE_GUIDE.md               # 本使用指南
└── js_deobfuscator/             # JavaScript逆向解析工具包
    ├── __init__.py
    ├── deobfuscate_claude.py    # 完整版逆向解析工具
    ├── simple_deobfuscator.py   # 简化版逆向解析工具
    ├── js_deobfuscator.py       # 基础解析模块
    ├── variable_restorer.py     # 变量名还原模块
    ├── code_formatter.py        # 代码格式化模块
    └── README_js_deobfuscator.md # 详细技术文档
```

## 🚀 快速开始

### 方法一：使用统一启动脚本（推荐）

```bash
# 简单解析模式
python utils/run_deobfuscator.py simple <input_file>

# 完整解析模式
python utils/run_deobfuscator.py full <input_file> --output <output_dir>
```

**示例：**
```bash
# 解析Claude CLI（简单模式）
python utils/run_deobfuscator.py simple /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude

# 解析任意JavaScript文件（完整模式）
python utils/run_deobfuscator.py full /path/to/script.js --output ./deobfuscated
```

### 方法二：直接使用工具

```bash
cd utils/js_deobfuscator

# 使用简化版
python simple_deobfuscator.py <input_file>

# 使用完整版
python deobfuscate_claude.py <input_file> [output_dir]
```

## 📋 使用场景

### 1. 分析混淆的Node.js应用

```bash
# 分析Claude CLI
python utils/run_deobfuscator.py simple /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude

# 分析其他Node.js应用
python utils/run_deobfuscator.py simple /usr/local/bin/some-node-app
```

### 2. 学习JavaScript代码结构

```bash
# 解析并学习某个JavaScript库
python utils/run_deobfuscator.py full ./lib/minified-library.js --output ./analysis
```

### 3. 代码审计

```bash
# 详细分析可疑代码
python utils/run_deobfuscator.py full ./suspicious.js --output ./audit
```

## 🎯 功能对比

| 功能 | 简单版 | 完整版 |
|------|--------|--------|
| 基础格式化 | ✅ | ✅ |
| 变量名还原 | ❌ | ✅ |
| 代码美化 | ✅ | ✅ |
| 分析报告 | ✅ | ✅ |
| 中间文件 | ❌ | ✅ |
| 处理速度 | 快 | 慢 |

**建议：**
- 初次分析使用**简单版**
- 深度研究使用**完整版**

## 📊 输出文件说明

### 简单版输出
- `*_formatted.js` - 格式化后的代码

### 完整版输出
- `*_deobfuscated.js` - 最终解析结果
- `*_step1.js` - 基础逆向解析结果
- `*_step2.js` - 变量名还原结果
- `*_analysis_report.txt` - 详细分析报告

## ⚙️ 高级用法

### 1. 自定义输出目录

```bash
python utils/run_deobfuscator.py full input.js --output /path/to/custom/output
```

### 2. 批量处理

创建批量处理脚本：

```bash
#!/bin/bash
# batch_deobfuscate.sh

for file in ./input/*.js; do
    echo "处理文件: $file"
    python utils/run_deobfuscator.py simple "$file" --output "./output"
done
```

### 3. 集成到工作流

在CI/CD中集成：

```yaml
# .github/workflows/deobfuscate.yml
- name: Deobfuscate JavaScript
  run: |
    python utils/run_deobfuscator.py full ./dist/app.js --output ./analysis
```

## 🔧 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod +x utils/run_deobfuscator.py
   ```

2. **Python模块找不到**
   ```bash
   # 确保在项目根目录
   python utils/run_deobfuscator.py simple input.js
   ```

3. **文件不存在**
   ```bash
   # 检查文件路径
   ls -la /path/to/input.js
   ```

4. **输出目录权限**
   ```bash
   # 确保输出目录可写
   mkdir -p ./output
   chmod 755 ./output
   ```

### 调试模式

```bash
# 查看详细错误信息
python utils/run_deobfuscator.py simple input.js 2>&1 | tee debug.log
```

## 📈 性能优化

### 大文件处理

对于大文件（>10MB），建议：

1. **使用简单版模式**
2. **分块处理**
3. **增加内存限制**

```bash
# 处理大文件
python utils/run_deobfuscator.py simple large-file.js --output ./temp
```

### 内存优化

```bash
# 限制Python内存使用
export PYTHONMALLOC=malloc
python utils/run_deobfuscator.py simple input.js
```

## 📚 进阶学习

### 1. 理解输出结果

解析后的代码包含：
- 格式化的代码结构
- 识别的模块导入
- 函数和变量关系
- 字符串内容

### 2. 手动分析技巧

1. **查看导入的模块** - 了解依赖关系
2. **分析函数结构** - 理解代码逻辑
3. **追踪变量使用** - 推断功能
4. **查找关键字符串** - 识别功能点

### 3. 结合其他工具

- **浏览器开发者工具** - 动态分析
- **Node.js调试器** - 运行时分析
- **静态分析工具** - 深度检查

## 🛡️ 安全提醒

### 合法使用

- ✅ 学习JavaScript代码结构
- ✅ 分析开源项目
- ✅ 安全研究
- ✅ 恶意软件分析（授权）

### 禁止用途

- ❌ 侵犯知识产权
- ❌ 绕过版权保护
- ❌ 未授权的逆向工程
- ❌ 恶意用途

## 📞 获取帮助

1. **查看帮助信息**
   ```bash
   python utils/run_deobfuscator.py --help
   ```

2. **查看详细文档**
   ```bash
   cat utils/js_deobfuscator/README_js_deobfuscator.md
   ```

3. **查看技术实现**
   - 阅读 `js_deobfuscator.py` - 基础解析逻辑
   - 阅读 `variable_restorer.py` - 变量还原算法
   - 阅读 `code_formatter.py` - 格式化实现

---

## 🎉 开始使用

现在你已经了解了如何使用JavaScript逆向解析工具，选择适合的使用方法开始分析吧！

**推荐开始命令：**
```bash
python utils/run_deobfuscator.py simple /path/to/your/javascript/file.js
```

记住：仅用于学习和研究目的！