#!/usr/bin/env python3
"""
JavaScript逆向解析工具启动脚本
提供便捷的命令行接口来运行逆向解析工具
"""

import os
import sys
import argparse

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(
        description='JavaScript逆向解析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 简单解析（推荐）
  python run_deobfuscator.py simple /path/to/input.js

  # 完整解析
  python run_deobfuscator.py full /path/to/input.js --output ./output

  # 解析Claude CLI
  python run_deobfuscator.py simple /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude

注意:
- 本工具仅用于学习和研究目的
- 请遵守相关法律法规和软件许可协议
- 不要将此工具用于非法用途
        """
    )

    parser.add_argument(
        'mode',
        choices=['simple', 'full'],
        help='解析模式: simple(简化版) 或 full(完整版)'
    )

    parser.add_argument(
        'input_file',
        help='要解析的JavaScript文件路径'
    )

    parser.add_argument(
        '--output', '-o',
        help='输出目录路径（可选）'
    )

    parser.add_argument(
        '--version', '-v',
        action='version',
        version='JavaScript逆向解析工具 v1.0.0'
    )

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        return 1

    # 获取工具目录
    tool_dir = os.path.join(os.path.dirname(__file__), 'js_deobfuscator')

    # 添加到Python路径
    sys.path.insert(0, tool_dir)

    try:
        if args.mode == 'simple':
            # 使用简化版工具
            from simple_deobfuscator import simple_deobfuscate

            print("启动简化版JavaScript逆向解析工具...")
            result = simple_deobfuscate(args.input_file, args.output)

        elif args.mode == 'full':
            # 使用完整版工具
            from deobfuscate_claude import ClaudeDeobfuscator

            print("启动完整版JavaScript逆向解析工具...")
            deobfuscator = ClaudeDeobfuscator()
            result = deobfuscator.deobfuscate_claude(args.input_file, args.output)

        if result:
            print("\n✅ 逆向解析完成!")
            return 0
        else:
            print("\n❌ 逆向解析失败!")
            return 1

    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保所有工具文件都在正确的位置")
        return 1
    except Exception as e:
        print(f"❌ 解析过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())