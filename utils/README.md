# Utils 工具集

本项目包含各种实用工具和脚本，用于开发和维护。

## 目录结构

```
utils/
├── README.md                           # 本文档
├── js_deobfuscator/                    # JavaScript逆向解析工具包
│   ├── __init__.py                     # 包初始化文件
│   ├── deobfuscate_claude.py           # 完整的逆向解析主程序
│   ├── simple_deobfuscator.py          # 简化版逆向解析工具
│   ├── js_deobfuscator.py              # 基础逆向解析模块
│   ├── variable_restorer.py            # 变量名还原模块
│   ├── code_formatter.py               # 代码格式化模块
│   └── README_js_deobfuscator.md       # JavaScript逆向解析工具详细说明
└── [更多工具目录...]                    # 未来可添加更多工具
```

## JavaScript逆向解析工具集

专门用于逆向解析混淆的JavaScript代码，特别是Node.js应用程序。

### 快速使用

#### 简单使用（推荐）

```bash
cd utils/js_deobfuscator
python simple_deobfuscator.py <input_file>
```

示例：
```bash
cd utils/js_deobfuscator
python simple_deobfuscator.py /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude
```

#### 完整使用

```bash
cd utils/js_deobfuscator
python deobfuscate_claude.py <input_file> [output_dir]
```

示例：
```bash
cd utils/js_deobfuscator
python deobfuscate_claude.py /home/<USER>/.nvm/versions/node/v20.19.4/bin/claude ./output
```

### 功能特性

1. **基础逆向解析**
   - 移除压缩格式，添加基本换行和空格
   - 提取和还原字符串
   - 基本的变量重命名

2. **变量名还原**
   - 分析变量使用模式
   - 基于上下文推断变量类型
   - 生成有意义的变量名

3. **代码格式化**
   - 智能缩进调整
   - 美化代码结构
   - 添加注释和说明

### 输出文件

- `*_deobfuscated.js` - 最终解析结果
- `*_step1.js` - 基础逆向解析结果
- `*_step2.js` - 变量名还原结果
- `*_analysis_report.txt` - 详细分析报告

## 使用须知

### 法律声明

⚠️ **重要提醒**:
- 本工具仅用于学习和研究目的
- 请遵守相关法律法规和软件许可协议
- 不要将此工具用于非法用途
- 尊重软件的知识产权

### 技术限制

- 高度混淆的代码可能需要手动调整
- 某些高级混淆技术可能无法完全解析
- 解析结果可能与原始源代码存在差异
- 大文件处理时可能只处理部分内容

## 添加新工具

要向utils目录添加新工具，请：

1. 在utils下创建新的工具目录
2. 添加工具文件和说明文档
3. 更新本README.md文件
4. 确保工具具有良好的文档和使用说明

## 贡献指南

欢迎贡献新的工具和改进：

1. 确保代码质量和文档完整性
2. 添加适当的错误处理
3. 包含详细的使用说明
4. 遵循项目的代码规范

## 免责声明

本工具集中的所有工具仅用于教育和研究目的。使用者应当遵守相关法律法规，不得将工具用于任何非法用途。开发者不对工具的误用或滥用承担责任。

---

如有问题或建议，请查看具体工具的文档或创建Issue进行反馈。