<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- CodeMirror CSS -->
    <link href="/aicode/static/external/codemirror.css" rel="stylesheet">
    <link href="/aicode/static/external/codemirror/theme/monokai.css" rel="stylesheet">
    <link href="/aicode/static/external/codemirror/addon/scroll/simplescrollbars.min.css" rel="stylesheet">
    <!-- Cherry Markdown CSS -->
    <link href="/aicode/static/external/cherry-markdown.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>
    <!-- CodeMirror -->
    <script src="/aicode/static/external/codemirror.js"></script>
    <script src="/aicode/static/external/codemirror/mode/javascript.js"></script>
    <script src="/aicode/static/external/codemirror/mode/python.js"></script>
    <script src="/aicode/static/external/codemirror/mode/htmlmixed.js"></script>
    <script src="/aicode/static/external/codemirror/mode/css.js"></script>
    <script src="/aicode/static/external/codemirror/mode/xml.js"></script>
    <script src="/aicode/static/external/codemirror/mode/markdown.js"></script>
    <script src="/aicode/static/external/codemirror/mode/clike.min.js"></script>
    <script src="/aicode/static/external/codemirror/mode/go.min.js"></script>
    <script src="/aicode/static/external/codemirror/mode/shell.min.js"></script>
    <script src="/aicode/static/external/codemirror/addon/scroll/simplescrollbars.min.js"></script>
    <!-- Cherry Markdown and dependencies -->
    <link href="/aicode/static/external/cherry-markdown.css" rel="stylesheet">
    <script src="/aicode/static/external/cherry-markdown.js"></script>
    <script src="/aicode/static/external/echarts.min.js"></script>
    <script src="/aicode/static/external/cherry-table-echarts-plugin.js"></script>
    <!-- 自定义JS -->
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/file-manager.js"></script>
    <script src="/aicode/static/js/markdown-ai.js"></script>

    <style>      

        iframe.cherry-dialog-iframe {
            width: 100%;
            height: 100%;
        }
        .file-manager-container {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        .file-tree-panel {
            width: 300px;
            min-width: 200px;
            max-width: 600px;
            background-color: #252526;
            color: #cccccc;
            border-right: 1px solid #3e3e42;
            display: flex;
            flex-direction: column;
            resize: horizontal;
            overflow: hidden;
            position: relative;
        }

        .resize-handle {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 4px;
            background-color: transparent;
            cursor: col-resize;
            z-index: 1000;
        }

        .resize-handle:hover {
            background-color: #0078d4;
        }

        .file-tree-header {
            padding: 10px 15px;
            background-color: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            font-weight: bold;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .toggle-file-tree {
            background: none;
            border: none;
            color: #cccccc;
            cursor: pointer;
            font-size: 16px;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .toggle-file-tree:hover {
            background-color: #3e3e42;
            border-radius: 3px;
        }
        
        .show-file-tree-btn {
            position: absolute;
            top: 10px;
            left: 0;
            z-index: 1000;
            background-color: #2d2d30;
            color: #cccccc;
            border: 1px solid #3e3e42;
            border-left: none;
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
            padding: 5px 8px;
            cursor: pointer;
            display: none;
        }
        
        .show-file-tree-btn:hover {
            background-color: #3e3e42;
        }
        
        .file-tree-hidden .file-tree-panel {
            width: 0 !important;
            min-width: 0;
            overflow: hidden;
        }
        
        .file-tree-hidden .show-file-tree-btn {
            display: block;
        }
        
        .file-tree-content {
            flex: 1;
            overflow-x: auto; /* 添加横向滚动条 */
            overflow-y: auto;
            padding: 5px 0;
        }

        .file-tree-item {
            display: flex;
            align-items: center;
            padding: 4px 15px;
            cursor: pointer;
            font-size: 13px;
            line-height: 22px;
            white-space: nowrap;
            user-select: none;
            position: relative;
            min-width: max-content; /* 确保内容不会被压缩 */
        }

        .file-tree-item:hover {
            background-color: #2a2d2e;
        }

        .file-tree-item.selected {
            background-color: #094771;
        }

        .file-tree-item.directory {
            font-weight: 500;
        }

        .file-tree-item .icon {
            margin-right: 6px;
            width: 16px;
            text-align: center;
            font-size: 12px;
            flex-shrink: 0; /* 防止图标被压缩 */
        }

        .file-tree-item .name {
            flex-shrink: 0; /* 防止文件名被压缩 */
        }

        .file-tree-item .expand-icon {
            margin-right: 4px;
            width: 12px;
            text-align: center;
            font-size: 10px;
            cursor: pointer;
            flex-shrink: 0; /* 防止展开图标被压缩 */
        }

        .file-tree-item .expand-icon.expanded {
            transform: rotate(90deg);
        }

        /* 移除原来的嵌套类，使用新的缩进方法 */
        .file-tree-indent {
            display: inline-block;
            width: 20px;
            min-width: 20px;
            height: 1px;
            flex-shrink: 0; /* 防止缩进元素被压缩 */
        }

        .file-tree-hidden .file-tree-panel {
            width: 0 !important;
            min-width: 0;
            overflow: hidden;
        }
        
        .file-tree-hidden .show-file-tree-btn {
            display: block;
        }
        
        /* 删除原有的 nested-* 类定义 */
        
        .file-tree-item.cut-item {
            opacity: 0.5;
            background-color: #3e3e42;
        }

        .editor-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }

        .editor-tabs {
            display: flex;
            background-color: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            min-height: 35px;
            overflow-x: auto;
        }

        .editor-tab {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background-color: #2d2d30;
            border-right: 1px solid #3e3e42;
            cursor: pointer;
            font-size: 13px;
            white-space: nowrap;
            min-width: 120px;
            max-width: 200px;
        }

        .editor-tab.active {
            background-color: #1e1e1e;
            border-bottom: 1px solid #1e1e1e;
        }

        .editor-tab:hover {
            background-color: #383838;
        }

        .editor-tab.active:hover {
            background-color: #1e1e1e;
        }

        .editor-tab .tab-icon {
            margin-right: 6px;
            font-size: 12px;
        }

        .editor-tab .tab-close {
            margin-left: auto;
            margin-right: -4px;
            padding: 2px;
            border-radius: 2px;
            font-size: 10px;
            opacity: 0.6;
        }

        .editor-tab .tab-close:hover {
            background-color: #e81123;
            opacity: 1;
        }

        .editor-content {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .editor-instance {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: none;
        }

        .editor-instance.active {
            display: block;
        }

        .CodeMirror {
            height: 100% !important;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #8c8c8c;
            font-size: 16px;
        }

        .welcome-screen .icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .toolbar {
            display: flex;
            align-items: center;
            padding: 5px 10px;
            background-color: #2d2d30;
            border-bottom: 1px solid #3e3e42;
            gap: 10px;
        }

        .toolbar .btn {
            padding: 4px 8px;
            font-size: 12px;
            border: none;
            background-color: #0e639c;
            color: white;
            border-radius: 2px;
            cursor: pointer;
        }

        .toolbar .btn:hover {
            background-color: #1177bb;
        }

        .toolbar .btn:disabled {
            background-color: #3e3e42;
            color: #8c8c8c;
            cursor: not-allowed;
        }

        .context-menu {
            position: fixed;
            background-color: #383838;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 4px 0;
            z-index: 1000;
            min-width: 150px;
            max-width: 250px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            display: none;
            /* 防止菜单内容溢出 */
            overflow: hidden;
            /* 确保菜单不会被其他元素遮挡 */
            transform: translateZ(0);
        }

        .context-menu-item {
            padding: 6px 12px;
            cursor: pointer;
            font-size: 13px;
            color: #cccccc;
            /* 确保长文本不会换行 */
            white-space: nowrap;
            /* 防止文本溢出 */
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .context-menu-item:hover {
            background-color: #094771;
        }

        .context-menu-item.disabled {
            color: #8c8c8c;
            cursor: not-allowed;
        }

        .context-menu-item.disabled:hover {
            background-color: transparent;
        }

        .context-menu-separator {
            height: 1px;
            background-color: #5a5a5a;
            margin: 4px 0;
        }

        /* 滚动条样式 */
        .file-tree-content::-webkit-scrollbar,
        .editor-tabs::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .file-tree-content::-webkit-scrollbar-track,
        .editor-tabs::-webkit-scrollbar-track {
            background: #2d2d30;
        }

        .file-tree-content::-webkit-scrollbar-thumb,
        .editor-tabs::-webkit-scrollbar-thumb {
            background: #424242;
            border-radius: 4px;
        }

        .file-tree-content::-webkit-scrollbar-thumb:hover,
        .editor-tabs::-webkit-scrollbar-thumb:hover {
            background: #4f4f4f;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .file-tree-panel {
                width: 250px;
                min-width: 200px;
            }
        }
    </style>
</head>

<body>
    <div id="context-menu" class="context-menu">
        <div class="context-menu-item" onclick="FileManager.openFile()"><i class="fas fa-file-import"></i> 打开</div>
        <div class="context-menu-item" onclick="FileManager.createNewFile()"><i class="fas fa-file"></i> 新建文件</div>
        <div class="context-menu-item" onclick="FileManager.createNewFolder()"><i class="fas fa-folder-plus"></i> 新建文件夹</div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" onclick="FileManager.cutFile()"><i class="fas fa-cut"></i> 剪切</div>
        <div class="context-menu-item" onclick="FileManager.copyFile()"><i class="fas fa-copy"></i> 复制</div>
        <div class="context-menu-item" onclick="FileManager.pasteFile()"><i class="fas fa-paste"></i> 粘贴</div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" onclick="FileManager.renameFile()"><i class="fas fa-edit"></i> 重命名</div>
        <div class="context-menu-item" onclick="FileManager.deleteFile()"><i class="fas fa-trash-alt"></i> 删除</div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" onclick="FileManager.downloadFile()"><i class="fas fa-download"></i> 下载</div>
        <div class="context-menu-item" onclick="FileManager.showFileInfo()"><i class="fas fa-info-circle"></i> 属性</div>
    </div>

    <div class="file-manager-container" id="file-manager-container">
        <button class="show-file-tree-btn" id="show-file-tree" title="显示文件树">
            <i class="fas fa-chevron-right"></i>
        </button>
        <div class="file-tree-panel">
            <div class="file-tree-header">
                <span>文件资源管理器</span>
                <button class="toggle-file-tree" id="toggle-file-tree" title="隐藏文件树">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            <div class="toolbar">
                <button class="btn" onclick="FileManager.createNewFile()" title="新建文件">
                    <i class="fas fa-file"></i>
                </button>
                <button class="btn" onclick="FileManager.createNewFolder()" title="新建文件夹">
                    <i class="fas fa-folder-plus"></i>
                </button>
                <button class="btn" onclick="FileManager.refreshFileTree()" title="刷新">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button class="btn" onclick="FileManager.triggerFileUpload()" title="上传文件">
                    <i class="fas fa-upload"></i>
                </button>
                <input type="file" id="file-upload-input" style="display: none;" multiple onchange="FileManager.handleFileUpload(event)">
            </div>
            <div class="file-tree-content">
                <div id="file-tree"></div>
            </div>
            <div class="resize-handle" id="resize-handle"></div>
        </div>

        <!-- 编辑器面板 -->
        <div class="editor-panel">
            <div class="editor-tabs" id="editor-tabs">
                <!-- 标签页将在这里动态生成 -->
            </div>
            <div class="editor-content" id="editor-content">
                <div class="welcome-screen">
                    <div class="icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div>选择一个文件开始编辑</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        $(document).ready(function () {
            const urlParams = new URLSearchParams(window.location.search);
            const projectId = urlParams.get('project_id');
            if (projectId) {
                FileManager.initFilesPage(projectId);
            } else {
                Utils.showAlert('缺少项目ID参数', 'danger');
            }
        });
    </script>
</body>

</html>
