<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话与预览 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">

    <style>
        html,
        body {
            overflow: hidden;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .main-container {
            display: flex;
            height: 100vh;
        }

        .left-panel {
            flex: 1;
            width: 100%;
            border-right: none;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .left-panel.with-preview {
            flex: 0 0 30%;
            min-width: 280px;
            border-right: 2px solid var(--border-color);
        }

        .right-panel {
            flex: 1;
            display: none;
            flex-direction: column;
            height: 100vh;
            min-width: 450px;
        }

        /* 响应式设计：小屏幕时调整布局 */
        @media (max-width: 1200px) {
            .left-panel.with-preview {
                flex: 0 0 35%;
                min-width: 300px;
            }
        }

        @media (max-width: 1024px) {
            .left-panel.with-preview {
                flex: 0 0 40%;
                min-width: 320px;
            }
        }

        @media (max-width: 768px) {
            .main-container.with-preview {
                flex-direction: column;
            }

            .left-panel.with-preview {
                flex: 1;
                height: 30vh;
                min-width: unset;
                border-right: none;
                border-bottom: 2px solid var(--border-color);
            }

            .right-panel.active {
                height: 70vh;
                min-width: unset;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .left-panel.with-preview {
                height: 25vh;
            }

            .right-panel.active {
                height: 75vh;
            }

            .chat-container {
                max-height: calc(100vh - 130px);
                padding: 0.75rem;
            }

            .left-panel.with-preview .chat-container {
                max-height: calc(25vh - 130px);
            }

            .input-area {
                padding: 0.5rem;
                max-height: 80px;
            }

            #messageInput {
                max-height: 40px;
                min-height: 30px;
            }

            .input-area .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.8rem;
            }

            .input-area .form-check {
                margin-bottom: 0.1rem;
            }

            .input-area .small {
                font-size: 0.7rem;
            }

            /* 当预览开启时，进一步压缩输入区域 */
            .left-panel.with-preview .input-area {
                max-height: 70px;
                padding: 0.25rem;
            }

            .left-panel.with-preview #messageInput {
                min-height: 25px;
                font-size: 0.8rem;
            }

            .left-panel.with-preview .input-area .btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.75rem;
            }

            .left-panel.with-preview .input-area .form-check-label {
                font-size: 0.65rem;
            }
        }

        .right-panel.active {
            display: flex;
            animation: slideInRight 0.3s ease-out;
        }

        /* 预览面板过渡动画 */
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }

            to {
                opacity: 0;
                transform: translateX(20px);
            }
        }

        /* 左侧面板过渡效果 */
        .left-panel {
            transition: all 0.3s ease;
        }

        .main-container {
            transition: all 0.3s ease;
        }
        
        .preview-iframe {
            flex: 1;
            border: none;
            width: 100%;
            height: calc(100vh - 30px);
        }

        /* 确保主要内容区域使用自定义滚动条而不是浏览器默认滚动条 */
        .main-content {
            overflow-y: auto;
            overflow-x: hidden;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            border: 1.5px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-secondary);
            padding: 1rem;
            min-height: 0;
            /* 确保flex子元素能正确收缩 */
        }

        /* 移动设备上的调整 */
        @media (max-width: 768px) {
            .chat-container {
                max-height: calc(100vh - 140px);
                /* 全屏时减去输入区域高度 */
            }

            .left-panel.with-preview .chat-container {
                max-height: calc(30vh - 140px);
                /* 有预览时减去输入区域和标题栏的高度 */
            }

            .input-area {
                padding: 0.75rem;
                max-height: 120px;
            }

            #messageInput {
                max-height: 60px;
                min-height: 50px;
            }

            /* 移动设备上调整按钮和开关布局 */
            .input-area .btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }

            .input-area .form-check-label {
                font-size: 0.75rem;
            }
        }

        .message-entry {
            border-radius: var(--border-radius-sm);
            margin-bottom: 0.75rem;
            box-shadow: var(--shadow);
            transition: var(--transition);
            animation: fadeIn 0.3s ease-in;
        }

        /* 当预览开启时，消息更紧凑 */
        .left-panel.with-preview .message-entry {
            margin-bottom: 0.5rem;
            padding: 0.5rem;
        }

        .left-panel.with-preview .message-content {
            font-size: 0.8rem;
            line-height: 1.4;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-entry:hover {
            box-shadow: var(--shadow-md);
        }

        .message-content {
            word-wrap: break-word;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .message-user {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-left: 4px solid #2196f3;
            width: 90%;
            /* 减少10%宽度 */
            margin-left: auto;
            /* 靠右显示 */
        }

        .message-user .d-flex {
            flex-direction: row-reverse;
            /* 用户消息标题靠右显示 */
        }

        .message-user .d-flex .me-2:first-child {
            margin-right: 0 !important;
            margin-left: 0.5rem !important;
        }

        .message-user .message-time {
            margin-left: 0;
            margin-right: 0.5rem;
        }

        .message-assistant {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%);
            border-left: 4px solid #4caf50;
        }

        .message-system {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-left: 4px solid #ff9800;
        }

        .message-streaming {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border-left: 4px solid #9c27b0;
            position: relative;
        }

        .typing-indicator {
            display: inline-block;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 0.3;
            }

            50% {
                opacity: 1;
            }
        }

        .input-area {
            border-top: 2px solid var(--border-color);
            background-color: var(--bg-primary);
            padding: 1rem;
            flex-shrink: 0;
            /* 防止输入区域被压缩 */
            max-height: 150px;
            /* 限制最大高度 */
        }

        /* 输入框样式优化 */
        #messageInput {
            resize: none;
            max-height: 60px;
            min-height: 40px;
        }

        /* 当预览开启时，进一步优化输入区域 */
        .left-panel.with-preview .input-area {
            padding: 0.75rem;
            max-height: 100px;
        }

        .left-panel.with-preview #messageInput {
            max-height: 50px;
            min-height: 35px;
            font-size: 0.875rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
            box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
        }

        .status-connected {
            background-color: var(--success-color);
        }

        .status-disconnected {
            background-color: #dc3545;
        }

        .status-typing {
            background-color: #9c27b0;
            animation: pulse 1.5s infinite;
        }

        .load-more-btn {
            background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            border: 1px solid #ccc;
            color: #666;
        }

        .load-more-btn:hover {
            background: linear-gradient(135deg, #eeeeee 0%, #d0d0d0 100%);
            color: #333;
        }

        /* 自定义滚动条 */
        .chat-container::-webkit-scrollbar {
            width: 8px;
        }

        .chat-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .chat-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .chat-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .message-time {
            font-size: 0.75rem;
            color: #666;
            margin-left: 0.5rem;
        }

        /* 内容折叠样式 */
        .message-content.collapsed {
            max-height: 5.6em;
            /* 5行高度 */
            overflow: hidden;
            position: relative;
        }

        .message-content.collapsed::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2em;
            background: linear-gradient(transparent, var(--bg-secondary));
        }

        .toggle-content-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #666;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            border-radius: 0.25rem;
            margin-top: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .toggle-content-btn:hover {
            background: rgba(255, 255, 255, 1);
            border-color: rgba(0, 0, 0, 0.2);
            color: #333;
        }

        .message-entry.expanded .message-content {
            max-height: none;
        }

        .message-entry.expanded .message-content::after {
            display: none;
        }

        /* 加载动画优化 */
        .loading-dots::after {
            content: '...';
            animation: dots 1.5s steps(3, end) infinite;
        }

        @keyframes dots {

            0%,
            20% {
                content: '.';
            }

            40% {
                content: '..';
            }

            60%,
            100% {
                content: '...';
            }
        }

        /* 消息进入动画 */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-new {
            animation: slideInUp 0.3s ease-out;
        }

        /* 新消息提示 */
        .new-message-indicator {
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
            animation: pulse 2s infinite;
        }

        /* 优化滚动条 */
        .chat-container::-webkit-scrollbar {
            width: 6px;
        }

        .chat-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }

        .chat-container::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        .chat-container::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        /* 输入框聚焦效果 */
        .form-control:focus {
            border-color: #4CAF50;
            box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
        }

        /* 按钮悬停效果优化 */
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        /* 消息内容代码块样式 */
        .message-content pre {
            background: rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            padding: 8px 12px;
            margin: 8px 0;
            overflow-x: auto;
            font-size: 0.8rem;
        }

        .message-content code {
            background: rgba(0, 0, 0, 0.05);
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
    </style>
</head>

<body>
    <!-- 主容器 -->
    <div class="main-container">
        <!-- 左侧面板：对话容器 -->
        <div class="left-panel">
            <div class="card-body p-0 h-100 d-flex flex-column">
                <!-- 对话消息容器 -->
                <div class="chat-container flex-grow-1" id="chatContainer">
                    <!-- 加载更多按钮（在对话容器内部顶部） -->
                    <div class="text-center p-2 border-bottom" id="loadMoreSection" style="display: none;">
                        <button type="button" class="btn btn-sm load-more-btn" id="loadMoreBtn">
                            <i class="fas fa-history"></i> 显示更多历史记录
                        </button>
                    </div>

                    <!-- 消息列表区域 -->
                    <div id="messagesList">
                        <div class="text-center py-5">
                            <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
                            <div>加载对话历史中...</div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-area">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="input-group">
                                <textarea class="form-control" id="messageInput" placeholder="输入您的问题..." rows="2"
                                    maxlength="500" style="resize: none;"></textarea>
                                <button class="btn btn-primary btn-sm" type="button" id="sendBtn" disabled style="padding: 0.25rem 0.5rem; font-size: 0.875rem;">
                                    <i class="fas fa-paper-plane"></i> 发送
                                </button>
                                <button class="btn btn-primary btn-sm" type="button" id="refreshBtn" style="padding: 0.25rem 0.5rem; font-size: 0.875rem;">
                                    <i class="fas fa-sync"></i> 刷新
                                </button>
                            </div>
                            <div class="d-flex justify-content-between mt-2">
                                <div>
                                    <div class="form-check form-switch d-inline-block me-3">
                                        <input class="form-check-input" type="checkbox" id="autoRefreshCheckbox">
                                        <label class="form-check-label" for="autoRefreshCheckbox">
                                            <small class="text-muted">自动刷新</small>
                                        </label>
                                    </div>
                                    <div class="form-check form-switch d-inline-block">
                                        <input class="form-check-input" type="checkbox" id="previewToggleCheckbox">
                                        <label class="form-check-label" for="previewToggleCheckbox">
                                            <small class="text-muted" id="previewToggleLabel">
                                                开启预览
                                            </small>
                                        </label>
                                    </div>
                                    <button class="btn btn-outline-danger btn-sm ms-2" type="button" id="clearSessionBtn" style="padding: 0.25rem 0.5rem; font-size: 0.875rem;" title="清理会话">
                                        <i class="fas fa-trash-alt"></i> 清理会话
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧面板：预览容器 -->
        <div class="right-panel" id="rightPanel">
            <div class="preview-header" style="min-height: 20px; padding: 0.25rem 0.5rem;">
                <div class="d-flex justify-content-between align-items-center">
                    <h8 class="mb-0" style="font-size: 0.8rem;">
                        <i class="fas fa-globe me-1" style="font-size: 0.7rem;"></i>
                        应用预览
                        <span class="badge bg-secondary ms-1" id="previewStatus" style="display: none; font-size: 0.6rem; padding: 0.2em 0.4em;">
                            <i class="fas fa-eye"></i> 静态预览
                        </span>
                    </h8>
                    <div>
                        <button type="button" class="btn btn-xs btn-outline-primary py-0 me-1" id="autoRefreshBtn" title="开启自动刷新" style="font-size: 0.6rem; padding: 0.1rem 0.2rem; display: none;">
                            <i class="fas fa-sync" style="font-size: 0.6rem;"></i>
                        </button>
                        <button type="button" class="btn btn-xs btn-outline-primary py-0" id="manualRefreshBtn" title="手动刷新预览" style="font-size: 0.6rem; padding: 0.1rem 0.2rem;">
                            <i class="fas fa-sync-alt" style="font-size: 0.6rem;"></i>
                        </button>
                    </div>
                </div>
            </div>
            <iframe class="preview-iframe" id="previewIframe"
                sandbox="allow-scripts allow-same-origin allow-forms"></iframe>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>

    <!-- 自定义JS -->
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/task_chat.js"></script>

    <script>
        // 预览管理器
        const PreviewManager = {
            isPreviewActive: false,
            refreshInterval: null,
            projectId: null,
            isSessionActive: true, // 会话是否活跃
            hasAIInteraction: false, // 是否有AI交互
            lastAIInteractionTime: null, // 最后一次AI交互时间
            previewLoadedOnce: false, // 预览是否已经加载过一次

            init(projectId) {
                this.projectId = projectId;
                // 完全重置所有状态
                this.isPreviewActive = false;
                this.hasAIInteraction = false;
                this.lastAIInteractionTime = null;
                this.previewLoadedOnce = false;
                this.isSessionActive = false;

                // 清理可能存在的定时器
                this.stopAutoRefresh();

                // 确保UI状态重置
                $('.left-panel').removeClass('with-preview');
                $('.main-container').removeClass('with-preview');
                $('#rightPanel').removeClass('active');
                $('#previewStatus').hide();
            },

            toggle() {
                if (this.isPreviewActive) {
                    this.stopPreview();
                } else {
                    this.startPreview();
                }
            },

            startPreview() {
                this.isPreviewActive = true;

                // 不保存用户偏好设置，让每次都默认关闭

                // 添加预览样式类
                $('.left-panel').addClass('with-preview');
                $('.main-container').addClass('with-preview');
                $('#rightPanel').addClass('active');
                $('#previewToggleCheckbox').prop('checked', true);
                $('#previewStatus').show();
                
                // 显示自动刷新按钮
                $('#autoRefreshBtn').show();

                // 更新预览标签
                $('#previewToggleLabel').html('关闭预览');

                // 设置iframe的src
                const iframe = $('#previewIframe');

                if (!this.previewLoadedOnce) {
                    // 第一次加载预览
                    iframe.attr('src', `/aicode/project_app/${this.projectId}/`);
                    this.previewLoadedOnce = true;
                    this.updatePreviewStatus('首次加载', 'info');
                } else {
                    // 已经加载过，直接显示当前内容
                    iframe.attr('src', `/aicode/project_app/${this.projectId}/`);
                }

                // 根据AI交互状态决定是否自动刷新
                this.decideAutoRefresh();
            },

            stopPreview() {
                this.isPreviewActive = false;
                this.isSessionActive = false;

                // 不保存用户偏好设置，让每次都默认关闭

                // 移除预览样式类
                $('.left-panel').removeClass('with-preview');
                $('.main-container').removeClass('with-preview');
                $('#rightPanel').removeClass('active');
                $('#previewToggleCheckbox').prop('checked', false);
                $('#previewStatus').hide();
                
                // 隐藏自动刷新按钮
                $('#autoRefreshBtn').hide();
                
                // 确保停止自动刷新
                this.stopAutoRefresh();
                
                // 重置自动刷新按钮状态为默认的非刷新状态
                $('#autoRefreshBtn').html('<i class="fas fa-sync" style="font-size: 0.6rem;"></i>');
                $('#autoRefreshBtn').removeClass('btn-primary').addClass('btn-outline-primary');
                $('#autoRefreshBtn').attr('title', '开启自动刷新');

                // 更新预览标签
                $('#previewToggleLabel').html('开启预览');

                // 清空iframe
                $('#previewIframe').attr('src', 'about:blank');
            },

            // 智能决定是否自动刷新
            decideAutoRefresh() {
                if (this.hasAIInteraction && this.isSessionActive) {
                    // 有AI交互且会话活跃，启动自动刷新，显示"实时预览"
                    this.startAutoRefresh();
                    this.updatePreviewStatus('实时预览中', 'success');
                    // 确保按钮状态正确
                    $('#autoRefreshBtn').html('<i class="fas fa-sync fa-spin" style="font-size: 0.6rem;"></i>');
                    $('#autoRefreshBtn').removeClass('btn-outline-primary').addClass('btn-primary');
                    $('#autoRefreshBtn').attr('title', '停止刷新');
                } else {
                    // 没有AI交互或会话不活跃，停止自动刷新，显示"静态预览"
                    this.stopAutoRefresh();
                    this.updatePreviewStatus('静态预览', 'secondary');
                    // 重置按钮状态为非刷新状态
                    $('#autoRefreshBtn').html('<i class="fas fa-sync" style="font-size: 0.6rem;"></i>');
                    $('#autoRefreshBtn').removeClass('btn-primary').addClass('btn-outline-primary');
                    $('#autoRefreshBtn').attr('title', '开启自动刷新');
                }
            },

            startAutoRefresh() {
                this.stopAutoRefresh(); // 确保没有重复的定时器
                this.refreshInterval = setInterval(() => {
                    // 只有在预览活跃且会话活跃时才刷新
                    if (this.isPreviewActive && this.isSessionActive && this.hasAIInteraction) {
                        this.refreshIframe();
                    } else {
                        // 如果条件不满足，停止自动刷新
                        this.decideAutoRefresh();
                    }
                }, 30000); // 每30秒刷新一次
            },

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            },

            // 标记AI交互开始
            markAIInteractionStart() {
                this.hasAIInteraction = true;
                this.lastAIInteractionTime = Date.now();
                this.isSessionActive = true;

                // 如果预览已开启，重新决定刷新策略
                if (this.isPreviewActive) {
                    this.decideAutoRefresh();
                }
            },

            // 标记AI交互结束
            markAIInteractionEnd() {
                this.isSessionActive = false;

                // AI会话结束后，立即刷新一次应用预览（确保显示最新的结果）
                if (this.isPreviewActive) {
                    this.refreshIframe();
                }

                // 5秒后重新评估刷新策略（给用户一些时间继续交互）
                setTimeout(() => {
                    if (!this.isSessionActive && this.isPreviewActive) {
                        this.decideAutoRefresh();
                    }
                }, 5000);
            },

            // 更新预览状态显示
            updatePreviewStatus(text, type = 'success') {
                const statusElement = $('#previewStatus');
                const statusClasses = {
                    'success': 'bg-success',
                    'secondary': 'bg-secondary',
                    'info': 'bg-info',
                    'warning': 'bg-warning'
                };

                // 移除所有状态类
                Object.values(statusClasses).forEach(cls => statusElement.removeClass(cls));

                // 添加新状态类
                statusElement.addClass(statusClasses[type] || 'bg-secondary');

                // 更新状态文本
                const statusTexts = {
                    'success': '<i class="fas fa-sync fa-spin"></i> 实时预览中',
                    'secondary': '<i class="fas fa-eye"></i> 静态预览',
                    'info': '<i class="fas fa-sync fa-spin"></i> 刷新中...',
                    'warning': '<i class="fas fa-pause"></i> 预览已暂停'
                };

                statusElement.html(statusTexts[type] || statusTexts['secondary']);
            },

            // 标记会话结束
            endSession() {
                this.markAIInteractionEnd();
            },

            refreshIframe() {
                if (this.isPreviewActive) {
                    const iframe = $('#previewIframe');
                    const currentSrc = iframe.attr('src');

                    // 显示刷新状态
                    if (!this.hasAIInteraction || !this.isSessionActive) {
                        this.updatePreviewStatus('刷新中...', 'info');
                    }

                    iframe.attr('src', 'about:blank');

                    // 短暂延迟后重新加载
                    setTimeout(() => {
                        if (this.isPreviewActive) {
                            iframe.attr('src', currentSrc);

                            // 刷新完成后恢复状态显示
                            if (!this.hasAIInteraction || !this.isSessionActive) {
                                setTimeout(() => {
                                    this.updatePreviewStatus('静态预览', 'secondary');
                                }, 1000);
                            }
                        }
                    }, 100);
                }
            }
        };

        // 页面初始化
        $(document).ready(function () {
            const projectId = Router.getParam('project_id');
            const taskId = Router.getParam('task_id');

            if (!projectId || !taskId) {
                Utils.showAlert('缺少必要参数', 'danger');
                Router.navigate('projects.html');
                return;
            }

            // 初始化预览管理器
            PreviewManager.init(projectId);

            // 默认关闭预览，忽略之前保存的偏好设置
            // 每次打开页面都从关闭状态开始

            // 清理当前项目的预览偏好设置
            localStorage.setItem(`preview_preference_${projectId}`, 'false');

            // 可选：清理所有项目的预览偏好设置（确保全局默认关闭）
            // 这个操作比较激进，可以根据需要启用
            /*
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('preview_preference_')) {
                    localStorage.setItem(key, 'false');
                }
            }
            */

            // 设置UI初始状态
            $('#previewToggleCheckbox').prop('checked', false);
            $('#previewToggleLabel').html('开启预览');
            $('#previewStatus').hide();
            // 隐藏自动刷新按钮
            $('#autoRefreshBtn').hide();

            // 扩展TaskChatManager以支持AI交互检测
            const originalSetWaitingState = TaskChatManager.setWaitingState;
            TaskChatManager.setWaitingState = function (isWaiting) {
                // 调用原始方法
                originalSetWaitingState.call(this, isWaiting);

                // 恢复AI交互状态控制预览刷新的逻辑
                if (isWaiting) {
                    // AI开始响应，标记AI交互开始
                    PreviewManager.markAIInteractionStart();
                } else {
                    // AI响应完成，延迟标记AI交互结束
                    setTimeout(() => {
                        PreviewManager.markAIInteractionEnd();
                    }, 3000); // 3秒延迟，给用户时间继续交互
                }
            };

            // 重写sendMessage方法以跟踪用户交互和AI交互
            const originalSendMessage = TaskChatManager.sendMessage;
            TaskChatManager.sendMessage = function () {
                this.hasUserInteraction = true;
                this.lastUserMessageTime = Date.now();

                // 用户发送消息时，标记AI交互即将开始
                PreviewManager.markAIInteractionStart();

                return originalSendMessage.call(this);
            };

            // 初始化聊天页面
            TaskChatManager.initChatPage(projectId, taskId);

            // 绑定预览切换复选框
            $('#previewToggleCheckbox').on('change', function () {
                PreviewManager.toggle();
            });

            // 绑定手动刷新按钮
            $('#manualRefreshBtn').on('click', function () {
                if (PreviewManager.isPreviewActive) {
                    PreviewManager.refreshIframe();
                    // 短暂显示刷新反馈
                    const btn = $(this);
                    const originalIcon = btn.html();
                    btn.html('<i class="fas fa-sync fa-spin"></i>');
                    btn.removeClass('btn-outline-primary').addClass('btn-primary');

                    setTimeout(() => {
                        btn.html(originalIcon);
                        btn.removeClass('btn-primary').addClass('btn-outline-primary');
                    }, 1000);
                }
            });

            // 绑定自动刷新按钮
            $('#autoRefreshBtn').on('click', function () {
                if (PreviewManager.isPreviewActive) {
                    // 切换自动刷新状态
                    if (PreviewManager.refreshInterval) {
                        // 当前正在自动刷新，停止自动刷新
                        PreviewManager.stopAutoRefresh();
                        $('#autoRefreshBtn').html('<i class="fas fa-sync" style="font-size: 0.6rem;"></i>');
                        $('#autoRefreshBtn').removeClass('btn-primary').addClass('btn-outline-primary');
                        $('#autoRefreshBtn').attr('title', '开启自动刷新');
                        PreviewManager.updatePreviewStatus('自动刷新已停止', 'warning');
                    } else {
                        // 当前未自动刷新，启动自动刷新
                        PreviewManager.startAutoRefresh();
                        $('#autoRefreshBtn').html('<i class="fas fa-sync fa-spin" style="font-size: 0.6rem;"></i>');
                        $('#autoRefreshBtn').removeClass('btn-outline-primary').addClass('btn-primary');
                        $('#autoRefreshBtn').attr('title', '停止刷新');

                        // 根据AI交互状态更新预览状态
                        if (PreviewManager.hasAIInteraction && PreviewManager.isSessionActive) {
                            PreviewManager.updatePreviewStatus('实时预览中', 'success');
                        } else {
                            PreviewManager.updatePreviewStatus('自动刷新中', 'info');
                        }
                    }
                }
            });

            // 绑定清理会话按钮
            $('#clearSessionBtn').on('click', function() {
                if (confirm('确定要清理当前会话吗？这将删除所有对话历史。')) {
                    const projectId = Router.getParam('project_id');
                    const taskId = Router.getParam('task_id');
                    
                    if (projectId && taskId) {
                        $.ajax({
                            url: `/aicode/api/projects/${projectId}/tasks/${taskId}/chat`,
                            method: 'DELETE',
                            success: function(response) {
                                if (response.success) {
                                    // 清空聊天记录显示
                                    $('#messagesList').empty();
                                    Utils.showAlert('会话已清理', 'success');
                                } else {
                                    Utils.showAlert('清理会话失败: ' + (response.error || response.message || '未知错误'), 'danger');
                                }
                            },
                            error: function(xhr) {
                                let errorMessage = '清理会话失败';
                                if (xhr.status === 405) {
                                    errorMessage = '请求方法不被允许，请检查API接口';
                                } else if (xhr.responseJSON) {
                                    errorMessage = '清理会话失败: ' + (xhr.responseJSON.error || xhr.responseJSON.message || '未知错误');
                                }
                                Utils.showAlert(errorMessage, 'danger');
                            }
                        });
                    } else {
                        Utils.showAlert('缺少项目或任务参数', 'danger');
                    }
                }
            });

            // 绑定返回任务按钮
            $('#back-to-tasks-btn').on('click', function () {
                // 停止预览
                if (PreviewManager.isPreviewActive) {
                    PreviewManager.stopPreview();
                }
                Router.navigate(`project_tasks.html?project_id=${projectId}`);
            });

            // 绑定清空对话按钮
            $('#clear-chat-btn').on('click', function () {
                TaskChatManager.clearChat();
            });

            // 绑定发送按钮
            $('#sendBtn').on('click', function () {
                TaskChatManager.sendMessage();
            });

            // 绑定刷新按钮
            $('#refreshBtn').on('click', function () {
                TaskChatManager.refreshMessage();
            });

            // 绑定输入框事件
            $('#messageInput').on('input', function () {
                const text = $(this).val();
                const length = text.length;
                $('#charCount').text(`${length}/2000`);

                // 启用/禁用发送按钮
                $('#sendBtn').prop('disabled', text.trim() === 0);
            });

            // 绑定键盘事件
            $('#messageInput').on('keydown', function (e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if ($('#sendBtn').prop('disabled') === false) {
                        TaskChatManager.sendMessage();
                    }
                }
            });

            // 绑定加载更多按钮
            $('#loadMoreBtn').on('click', function () {
                TaskChatManager.loadMoreMessages();
            });

            // 页面卸载时清理定时器和样式
            $(window).on('beforeunload', function () {
                if (PreviewManager.isPreviewActive) {
                    PreviewManager.stopPreview();
                }
                // 确保清理所有预览相关的样式类
                $('.left-panel').removeClass('with-preview');
                $('.main-container').removeClass('with-preview');

                // 清理本地存储中的预览偏好设置，确保下次打开时默认关闭
                const projectId = Router.getParam('project_id');
                if (projectId) {
                    localStorage.setItem(`preview_preference_${projectId}`, 'false');
                }
            });

            // 监听页面可见性变化
            $(document).on('visibilitychange', function () {
                if (document.hidden && PreviewManager.isPreviewActive) {
                    // 页面被隐藏时，停止预览（会话可能已结束）
                    PreviewManager.endSession();
                }
            });

            // 设置会话超时检测
            let sessionTimeoutTimer;
            const resetSessionTimeout = function () {
                clearTimeout(sessionTimeoutTimer);
                sessionTimeoutTimer = setTimeout(() => {
                    // 如果5分钟内没有新的用户交互，认为会话结束
                    if (PreviewManager.isPreviewActive && TaskChatManager.hasUserInteraction) {
                        PreviewManager.endSession();
                    }
                }, 5 * 60 * 1000); // 5分钟
            };

            // 监听用户交互
            $(document).on('mousemove keydown click scroll', function () {
                if (PreviewManager.isPreviewActive) {
                    resetSessionTimeout();
                }
            });

            // 初始化会话超时检测
            resetSessionTimeout();
        });
    </script>
</body>

</html>