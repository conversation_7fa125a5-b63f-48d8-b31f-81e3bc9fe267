/**
 * 文件管理器JavaScript模块
 */
// 文件管理器主对象
window.FileManager = {
    currentProjectId: null,
    currentProject: null,
    selectedFile: null,
    selectedFiles: new Set(), // 存储多选的文件
    contextMenuTarget: null,
    openTabs: new Map(), // 存储打开的标签页
    activeTabId: null,
    editors: new Map(), // 存储CodeMirror编辑器实例
    expandedFolders: new Set(), // 存储展开的文件夹
    clipboard: null, // 剪贴板
    clipboardOperation: null, // 剪贴板操作类型：'copy' 或 'cut'
    lastSelectedFile: null, // 最后选择的文件，用于范围选择
    isRangeSelecting: false, // 是否正在进行范围选择

    /**
     * 初始化文件管理页面
     */
    initFilesPage: function(projectId) {
        this.currentProjectId = projectId;
        this.loadProject();
        this.loadFileTree();
        this.bindEvents();
    },

    /**
     * 加载项目信息
     */
    loadProject: function() {
        API.projects.get(this.currentProjectId)
            .done((project) => {
                this.currentProject = project;
                this.renderProjectInfo(project);
            })
            .fail((xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载项目信息失败: ' + (response.message || '未知错误'), 'danger');
            });
    },

    /**
     * 渲染项目信息
     */
    renderProjectInfo: function(project) {
        $('#project-name').text(project.name);
    },

    /**
     * 加载文件树
     */
    loadFileTree: function(path = '') {
        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files`,
            method: 'GET',
            data: { path: path },
            success: (response) => {
                if (response.success) {
                    this.renderFileTree(response.files, path);
                    // 文件树渲染完成后重新绑定事件
                    this.bindFileTreeEvents();
                } else {
                    Utils.showAlert('加载文件树失败', 'danger');
                }
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载文件树失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });
    },

    /**
     * 渲染文件树
     */
    renderFileTree: function(files, currentPath = '') {
        const container = $('#file-tree');

        if (currentPath === '') {
            // 根目录，清空容器
            container.empty();
        }

        files.forEach(file => {
            const item = this.createFileTreeItem(file, currentPath);
            if (currentPath === '') {
                container.append(item);
            } else {
                // 找到父文件夹并添加子项
                const parentElement = container.find(`[data-path="${currentPath}"]`);
                if (parentElement.length > 0) {
                    // 找到父文件夹后面的最后一个子项位置
                    let insertAfter = parentElement;
                    const parentLevel = (currentPath.match(/\//g) || []).length;

                    // 查找同级或更深层级的最后一个元素
                    let nextSibling = parentElement.next();
                    while (nextSibling.length > 0) {
                        const siblingPath = nextSibling.attr('data-path');
                        const siblingLevel = (siblingPath.match(/\//g) || []).length;

                        // 如果是更深层级的子项，继续查找
                        if (siblingLevel > parentLevel) {
                            insertAfter = nextSibling;
                            nextSibling = nextSibling.next();
                        } else {
                            // 遇到同级或更浅层级，停止查找
                            break;
                        }
                    }

                    insertAfter.after(item);
                }
            }
        });
    },

    /**
     * 创建文件树项目
     */
    createFileTreeItem: function(file, parentPath) {
        const isDirectory = file.is_directory;
        const icon = this.getFileIcon(file);

        // 计算缩进级别 - 基于文件路径的深度
        let nestLevel = 0;
        if (file.path) {
            // 计算路径中 / 的数量来确定层级
            const pathParts = file.path.split('/');
            nestLevel = pathParts.length - 1;
        }

        const expandIcon = isDirectory ? 
            `<span class="expand-icon" onclick="FileManager.toggleFolder('${file.path}')">
                <i class="fas fa-chevron-right"></i>
            </span>` : 
            '<span class="expand-icon"></span>';

        // 创建缩进元素
        let indent = '';
        for (let i = 0; i < nestLevel; i++) {
            indent += '<span class="file-tree-indent"></span>';
        }

        const item = $(`
            <div class="file-tree-item ${isDirectory ? 'directory' : 'file'}"
                 data-path="${file.path}"
                 data-is-directory="${isDirectory}"
                 data-nest-level="${nestLevel}"
                 oncontextmenu="FileManager.showContextMenu(event, '${file.path}', ${isDirectory})">
                ${indent}
                ${expandIcon}
                <span class="icon">${icon}</span>
                <span class="name file-name">${file.name}</span>
            </div>
        `);

        return item;
    },

    /**
     * 获取文件图标
     */
    getFileIcon: function(file) {
        if (file.is_directory) {
            return '<i class="fas fa-folder"></i>';
        }

        const ext = file.extension.toLowerCase();
        const iconMap = {
            '.js': '<i class="fab fa-js-square" style="color: #f7df1e;"></i>',
            '.py': '<i class="fab fa-python" style="color: #3776ab;"></i>',
            '.html': '<i class="fab fa-html5" style="color: #e34f26;"></i>',
            '.css': '<i class="fab fa-css3-alt" style="color: #1572b6;"></i>',
            '.json': '<i class="fas fa-code" style="color: #ffd700;"></i>',
            '.md': '<i class="fab fa-markdown" style="color: #083fa1;"></i>',
            '.txt': '<i class="fas fa-file-alt"></i>',
            '.xml': '<i class="fas fa-code" style="color: #ff6600;"></i>',
            '.yml': '<i class="fas fa-cog" style="color: #cb171e;"></i>',
            '.yaml': '<i class="fas fa-cog" style="color: #cb171e;"></i>',
            '.jpg': '<i class="fas fa-image" style="color: #4caf50;"></i>',
            '.jpeg': '<i class="fas fa-image" style="color: #4caf50;"></i>',
            '.png': '<i class="fas fa-image" style="color: #4caf50;"></i>',
            '.gif': '<i class="fas fa-image" style="color: #4caf50;"></i>',
            '.pdf': '<i class="fas fa-file-pdf" style="color: #d32f2f;"></i>',
            '.xlsx': '<i class="fas fa-file-excel" style="color: #217346;"></i>',
            '.xls': '<i class="fas fa-file-excel" style="color: #217346;"></i>',
            '.docx': '<i class="fas fa-file-word" style="color: #2b579a;"></i>',
            '.doc': '<i class="fas fa-file-word" style="color: #2b579a;"></i>',
            '.pptx': '<i class="fas fa-file-powerpoint" style="color: #d24726;"></i>',
            '.ppt': '<i class="fas fa-file-powerpoint" style="color: #d24726;"></i>',
            '.zip': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>',
            '.rar': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>',
            '.7z': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>',
            '.tar': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>',
            '.gz': '<i class="fas fa-file-archive" style="color: #ffc107;"></i>'
        };

        return iconMap[ext] || '<i class="fas fa-file"></i>';
    },

    /**
     * 切换文件夹展开/收起
     */
    toggleFolder: function(path) {
        const item = $(`.file-tree-item[data-path="${path}"]`);
        const expandIcon = item.find('.expand-icon i');
        
        if (this.expandedFolders.has(path)) {
            // 收起文件夹
            this.expandedFolders.delete(path);
            expandIcon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
            this.hideSubItems(path);
        } else {
            // 展开文件夹
            this.expandedFolders.add(path);
            expandIcon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
            this.loadSubItems(path);
        }
    },

    /**
     * 加载子项目
     */
    loadSubItems: function(path) {
        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files`,
            method: 'GET',
            data: { path: path },
            success: (response) => {
                if (response.success) {
                    this.insertSubItems(path, response.files);
                }
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载文件夹内容失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });
    },

    /**
     * 插入子项目
     */
    insertSubItems: function(parentPath, files) {
        const parentItem = $(`.file-tree-item[data-path="${parentPath}"]`);
        let insertAfter = parentItem;

        files.forEach(file => {
            const item = this.createFileTreeItem(file, parentPath);
            insertAfter.after(item);
            insertAfter = item;
        });
    },

    /**
     * 隐藏子项目
     */
    hideSubItems: function(parentPath) {
        const allItems = $('.file-tree-item');
        allItems.each(function() {
            const itemPath = $(this).data('path');
            if (itemPath && itemPath.startsWith(parentPath + '/')) {
                $(this).remove();
            }
        });
    },

    /**
     * 选择文件
     */
    selectFile: function(path, isDirectory, event) {
        // 阻止事件冒泡
        if (event) {
            event.stopPropagation();
        }

        const item = $(`.file-tree-item[data-path="${path}"]`);
        const isCtrlKey = event && (event.ctrlKey || event.metaKey);
        const isShiftKey = event && event.shiftKey;

        // 处理Shift+点击的范围选择
        if (isShiftKey && this.lastSelectedFile && this.lastSelectedFile !== path) {
            // 范围选择模式
            lastSelectedFile = this.lastSelectedFile;
            //console.log('执行范围选择:', lastSelectedFile, '->', path);
            event.preventDefault();

            // 清除之前的选择
            this.clearSelection();

            // 选择范围
            if (this.selectRangeBetween(lastSelectedFile, path)) {
                // 范围选择成功，不打开文件
                //console.log('范围选择成功，选中的文件:', Array.from(this.selectedFiles));
                this.isRangeSelecting = true;
                return;
            } else {
                // console.log(`范围选择失败: [${this.lastSelectedFile} -> ${path}]`);
            }
        }

        // 处理Ctrl+点击的多选
        if (isCtrlKey) {
            //console.log('执行Ctrl+多选，当前选中数量:', this.selectedFiles.size);
            if (item.hasClass('selected')) {
                // 取消选中
                item.removeClass('selected');
                this.selectedFiles.delete(path);
                //console.log('取消选择:', path);

                // 如果是当前选中的主文件，更新为集合中的最后一个
                if (this.selectedFile === path) {
                    const filesArray = Array.from(this.selectedFiles);
                    this.selectedFile = filesArray.length > 0 ? filesArray[filesArray.length - 1] : null;
                    this.lastSelectedFile = this.selectedFile;
                    //console.log('更新最后选择:', this.lastSelectedFile);
                }
            } else {
                // 添加到选中集合
                item.addClass('selected');
                this.selectedFiles.add(path);
                this.selectedFile = path;
                this.lastSelectedFile = path;
                //console.log('添加选择:', path);
            }
            this.isRangeSelecting = false;
        } else {
            if (this.lastSelectedFile){
                this.clearSelection();
            }
            // 单选模式
            // 如果不是范围选择，则清除之前的选择
            if (!this.isRangeSelecting) {
                $('.file-tree-item').removeClass('selected');
                this.selectedFiles.clear();
            }

            // 添加选中状态
            if (!item.hasClass('selected')) {
                item.addClass('selected');
                this.selectedFiles.add(path);
            }
            this.selectedFile = path;
            this.lastSelectedFile = path;
            this.isRangeSelecting = false;

            // 如果是文件且不是多选，则打开文件
            if (!isDirectory && !isCtrlKey && !isShiftKey) {
                this.openFile(path);
            }
        }
    },

    /**
     * 打开文件
     */
    openFile: function(path) {
        if (!path) {
            // 优先使用右键菜单选中的文件
            if (this.contextMenuTarget && !this.contextMenuTarget.isDirectory) {
                path = this.contextMenuTarget.path;
            } else {
                path = this.selectedFile;
            }
        }

        if (!path) {
            Utils.showAlert('请先选择一个文件', 'warning');
            return;
        }

        // 检查是否为文件（如果是文件夹则不打开）
        if (this.contextMenuTarget && this.contextMenuTarget.isDirectory) {
            Utils.showAlert('不能打开文件夹', 'warning');
            this.hideContextMenu();
            return;
        }

        // 检查文件是否已经打开
        if (this.openTabs.has(path)) {
            this.switchToTab(path);
            this.hideContextMenu();
            return;
        }

        // 加载文件内容
        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/content`,
            method: 'GET',
            data: { path: path },
            success: (response) => {
                if (response.success) {
                    this.createTab(path, response);
                } else {
                    Utils.showAlert('打开文件失败: ' + response.message, 'danger');
                }
                this.hideContextMenu();
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('打开文件失败: ' + (response.error || response.message || '未知错误'), 'danger');
                this.hideContextMenu();
            }
        });
    },

    /**
     * 创建标签页
     */
    createTab: function(path, fileData) {
        const fileName = path.split('/').pop();
        const tabId = 'tab-' + Date.now();
        const isMarkdown = fileData.file_info.extension.toLowerCase() === '.md';

        // 创建标签页
        const tab = $(`
            <div class="editor-tab" data-tab-id="${tabId}" data-file-path="${path}">
                <span class="tab-icon">${this.getFileIcon(fileData.file_info)}</span>
                <span class="tab-name">${fileName}</span>
                <span class="tab-close" onclick="FileManager.closeTab('${tabId}', event)">
                    <i class="fas fa-times"></i>
                </span>
            </div>
        `);

        tab.on('click', () => this.switchToTab(path));
        // 添加双击事件处理
        tab.on('dblclick', () => {
            // 如果是Markdown文件，切换到编辑模式
            if (isMarkdown) {
                const tabInfo = this.openTabs.get(path);
                if (tabInfo && tabInfo.editor) {
                    tabInfo.editor.switchModel("editOnly");
                }
            }
        });
        $('#editor-tabs').append(tab);

        let editor;

        if (isMarkdown) {
            // 创建Markdown编辑器容器
            const editorContainer = $(`
                <div class="editor-instance markdown-editor-instance" data-tab-id="${tabId}">
                    <div id="markdown-editor-${tabId}" style="height: 100%; width: 100%;"></div>
                </div>
            `);
            $('#editor-content').append(editorContainer);

            // 初始化Cherry Markdown编辑器
            editor = this.createMarkdownEditor(tabId, fileData.content || '', path);
        } else {
            // 创建CodeMirror编辑器容器
            const editorContainer = $(`
                <div class="editor-instance code-editor-instance" data-tab-id="${tabId}">
                    <textarea id="editor-${tabId}"></textarea>
                </div>
            `);
            $('#editor-content').append(editorContainer);

            // 初始化CodeMirror
            const mode = this.getEditorMode(fileData.file_info.extension);
            editor = CodeMirror.fromTextArea(document.getElementById(`editor-${tabId}`), {
                mode: mode,
                theme: 'monokai',
                lineNumbers: true,
                lineWrapping: true,
                indentUnit: 4,
                tabSize: 4,
                autoCloseBrackets: true,
                matchBrackets: true,
                foldGutter: true,
                gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
                extraKeys: {
                    "Ctrl-Space": "autocomplete",
                    "Ctrl-/": "toggleComment",
                    "Ctrl-D": "deleteLine",
                    "Ctrl-Shift-K": "deleteLine",
                    "Alt-Up": "swapLineUp",
                    "Alt-Down": "swapLineDown",
                    "Ctrl-]": "indentMore",
                    "Ctrl-[": "indentLess",
                    "Ctrl-F": "findPersistent",
                    "Ctrl-H": "replace",
                    "F11": function(cm) {
                        cm.setOption("fullScreen", !cm.getOption("fullScreen"));
                    },
                    "Esc": function(cm) {
                        if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
                    }
                },
                highlightSelectionMatches: {showToken: /\w/, annotateScrollbar: true},
                styleActiveLine: true,
                selectionPointer: true,
                cursorBlinkRate: 530,
                dragDrop: true,
                allowDropFileTypes: ["text/plain"],
                scrollbarStyle: "overlay"
            });

            editor.setValue(fileData.content || '');
        }

        // 保存标签页信息
        this.openTabs.set(path, {
            tabId: tabId,
            fileName: fileName,
            filePath: path,
            fileData: fileData,
            editor: editor,
            modified: false,
            isMarkdown: isMarkdown
        });

        this.editors.set(tabId, editor);

        // 切换到新标签页
        this.switchToTab(path);

        if (isMarkdown) {
            // Markdown编辑器的监听器在createMarkdownEditor中设置
        } else {
            // 监听编辑器变化
            editor.on('change', () => {
                this.markTabAsModified(path);
            });

            // 监听光标位置变化，显示行列信息
            editor.on('cursorActivity', () => {
                this.updateCursorInfo(editor, tabId);
            });

            // 自动保存功能（可选）
            let autoSaveTimer;
            editor.on('change', () => {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(() => {
                    const tabInfo = this.openTabs.get(path);
                    if (tabInfo && tabInfo.modified) {
                        this.autoSaveFile(path);
                    }
                }, 5000); // 5秒后自动保存
            });
        }
    },

    /**
     * 创建Markdown编辑器
     */
    createMarkdownEditor: function(tabId, content, filePath) {
        // 设置全局变量供markdown-editor.js使用
        currentProjectId = this.currentProjectId;
        apiEndpoint = `files/content?path=${encodeURIComponent(filePath)}`;
        contentField = 'content';
        docType = 'other';

        // 创建Cherry Markdown实例
        //const cherry = new Cherry(config);
        const cherry = createCherry(`markdown-editor-${tabId}`, content)
        
        // 自动保存功能
        let autoSaveTimer;
        cherry.editor.editor.on('change', () => {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(() => {
                const tabInfo = FileManager.openTabs.get(filePath);
                if (tabInfo && tabInfo.modified) {
                    FileManager.autoSaveMarkdownFile(filePath, cherry);
                }
            }, 5000);
        });
        
        const previewers = document.querySelectorAll('.cherry-previewer');
        if (previewers) {
            // 修改所有预览器的宽度（已经打开的也会被修改）
            previewers.forEach(previewer => {
                previewer.style.width = '60%';
            });
        }
        const editors = document.querySelectorAll('.cherry-editor');
        if (editors) {
             editors.forEach(editor => {
                editor.style.width = '40%';
            });
        }
        cherry.switchModel("previewOnly")

        return cherry;
    },

    /**
     * 保存Markdown文件
     */
    saveMarkdownFile: function(path, cherryEditor) {
        const content = cherryEditor.getValue();

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/save`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                path: path,
                content: content
            }),
            success: (response) => {
                if (response.success) {
                    Utils.showAlert('文件保存成功', 'success');
                    this.markTabAsSaved(path);
                } else {
                    Utils.showAlert('保存文件失败: ' + response.message, 'danger');
                }
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('保存文件失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });
    },

    /**
     * 自动保存Markdown文件
     */
    autoSaveMarkdownFile: function(path, cherryEditor) {
        const content = cherryEditor.getValue();

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/save`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                path: path,
                content: content
            }),
            success: (response) => {
                if (response.success) {
                    this.markTabAsSaved(path);
                    console.log('Markdown文件自动保存成功:', path);
                }
            },
            error: (xhr) => {
                console.error('Markdown文件自动保存失败:', xhr);
            }
        });
    },

    /**
     * 获取编辑器模式
     */
    getEditorMode: function(extension) {
        const modeMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'javascript',
            '.tsx': 'javascript',
            '.py': 'python',
            '.html': 'htmlmixed',
            '.htm': 'htmlmixed',
            '.css': 'css',
            '.scss': 'css',
            '.sass': 'css',
            '.less': 'css',
            '.xml': 'xml',
            '.svg': 'xml',
            '.md': 'markdown',
            '.markdown': 'markdown',
            '.json': 'javascript',
            '.txt': 'text/plain',
            '.log': 'text/plain',
            '.yml': 'yaml',
            '.yaml': 'yaml',
            '.sh': 'text/x-sh',
            '.bash': 'text/x-sh',
            '.zsh': 'text/x-sh',
            '.fish': 'text/x-sh',
            '.sql': 'sql',
            '.php': 'php',
            '.java': 'text/x-java',
            '.c': 'text/x-csrc',
            '.cpp': 'text/x-c++src',
            '.cc': 'text/x-c++src',
            '.cxx': 'text/x-c++src',
            '.h': 'text/x-csrc',
            '.hpp': 'text/x-c++src',
            '.hxx': 'text/x-c++src',
            '.go': 'text/x-go',
            '.rs': 'text/x-rustsrc',
            '.rb': 'text/x-ruby',
            '.vue': 'text/x-vue',
            '.dockerfile': 'dockerfile',
            '.gitignore': 'text/plain',
            '.env': 'text/plain'
        };

        return modeMap[extension.toLowerCase()] || 'text/plain';
    },

    /**
     * 切换到指定标签页
     */
    switchToTab: function(path) {
        const tabInfo = this.openTabs.get(path);
        if (!tabInfo) return;

        // 移除所有活动状态
        $('.editor-tab').removeClass('active');
        $('.editor-instance').removeClass('active');

        // 激活当前标签页
        $(`.editor-tab[data-tab-id="${tabInfo.tabId}"]`).addClass('active');
        $(`.editor-instance[data-tab-id="${tabInfo.tabId}"]`).addClass('active');

        this.activeTabId = tabInfo.tabId;

        // 刷新编辑器
        setTimeout(() => {
            if (tabInfo.isMarkdown) {
                // Cherry Markdown编辑器通常不需要refresh，但可以确保焦点正确
                if (tabInfo.editor && tabInfo.editor.editor) {
                    //tabInfo.editor.editor.focus();
                }
            } else {
                // CodeMirror编辑器需要refresh
                tabInfo.editor.refresh();
            }
        }, 100);
    },

    /**
     * 关闭标签页
     */
    closeTab: function(tabId, event) {
        if (event) {
            event.stopPropagation();
        }

        // 找到对应的文件路径
        let pathToClose = null;
        for (let [path, tabInfo] of this.openTabs) {
            if (tabInfo.tabId === tabId) {
                pathToClose = path;
                break;
            }
        }

        if (!pathToClose) return;

        const tabInfo = this.openTabs.get(pathToClose);
        
        // 检查是否有未保存的更改
        if (tabInfo.modified) {
            if (!confirm('文件有未保存的更改，确定要关闭吗？')) {
                return;
            }
        }

        // 移除DOM元素
        $(`.editor-tab[data-tab-id="${tabId}"]`).remove();
        $(`.editor-instance[data-tab-id="${tabId}"]`).remove();

        // 清理数据
        this.openTabs.delete(pathToClose);
        this.editors.delete(tabId);

        // 如果关闭的是当前活动标签页，切换到其他标签页
        if (this.activeTabId === tabId) {
            const remainingTabs = Array.from(this.openTabs.values());
            if (remainingTabs.length > 0) {
                this.switchToTab(remainingTabs[0].filePath);
            } else {
                this.activeTabId = null;
                this.showWelcomeScreen();
            }
        }
    },

    /**
     * 显示欢迎屏幕
     */
    showWelcomeScreen: function() {
        $('#editor-content').html(`
            <div class="welcome-screen">
                <div class="icon">
                    <i class="fas fa-code"></i>
                </div>
                <div>选择一个文件开始编辑</div>
            </div>
        `);
    },

    /**
     * 标记标签页为已修改
     */
    markTabAsModified: function(path) {
        const tabInfo = this.openTabs.get(path);
        if (!tabInfo || tabInfo.modified) return;

        tabInfo.modified = true;
        const tab = $(`.editor-tab[data-tab-id="${tabInfo.tabId}"]`);
        const tabName = tab.find('.tab-name');
        if (!tabName.text().endsWith(' •')) {
            tabName.text(tabName.text() + ' •');
        }
    },

    /**
     * 保存当前文件
     */
    saveCurrentFile: function() {
        if (!this.activeTabId) {
            Utils.showAlert('没有打开的文件', 'warning');
            return;
        }

        const editor = this.editors.get(this.activeTabId);
        if (!editor) return;

        // 找到对应的文件路径
        let pathToSave = null;
        let tabInfo = null;
        for (let [path, info] of this.openTabs) {
            if (info.tabId === this.activeTabId) {
                pathToSave = path;
                tabInfo = info;
                break;
            }
        }

        if (!pathToSave || !tabInfo) return;

        // 根据文件类型调用不同的保存方法
        if (tabInfo.isMarkdown) {
            this.saveMarkdownFile(pathToSave, editor);
        } else {
            const content = editor.getValue();

            $.ajax({
                url: `/aicode/api/projects/${this.currentProjectId}/files/save`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    path: pathToSave,
                    content: content
                }),
                success: (response) => {
                    if (response.success) {
                        Utils.showAlert('文件保存成功', 'success');
                        this.markTabAsSaved(pathToSave);
                    } else {
                        Utils.showAlert('保存文件失败: ' + response.message, 'danger');
                    }
                },
                error: (xhr) => {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('保存文件失败: ' + (response.error || response.message || '未知错误'), 'danger');
                }
            });
        }
    },

    /**
     * 标记标签页为已保存
     */
    markTabAsSaved: function(path) {
        const tabInfo = this.openTabs.get(path);
        if (!tabInfo) return;

        tabInfo.modified = false;
        const tab = $(`.editor-tab[data-tab-id="${tabInfo.tabId}"]`);
        const tabName = tab.find('.tab-name');
        const text = tabName.text();
        if (text.endsWith(' •')) {
            tabName.text(text.slice(0, -2));
        }
    },

    /**
     * 显示右键菜单
     */
    showContextMenu: function(event, path, isDirectory) {
        event.preventDefault();
        event.stopPropagation();

        this.contextMenuTarget = { path: path, isDirectory: isDirectory };

        const menu = $('#context-menu');

        // 先显示菜单以获取其尺寸
        menu.css({
            left: '-9999px',
            top: '-9999px',
            display: 'block'
        });

        // 获取菜单和窗口的尺寸信息
        const menuWidth = menu.outerWidth();
        const menuHeight = menu.outerHeight();
        const windowWidth = $(window).width();
        const windowHeight = $(window).height();
        const scrollTop = $(window).scrollTop();
        const scrollLeft = $(window).scrollLeft();

        // 计算菜单位置
        let left = event.pageX;
        let top = event.pageY;

        // 检查右边界，如果超出则向左调整
        if (left + menuWidth > windowWidth + scrollLeft) {
            left = windowWidth + scrollLeft - menuWidth - 5; // 留5px边距
        }

        // 检查底部边界，如果超出则向上调整
        if (top + menuHeight > windowHeight + scrollTop) {
            top = windowHeight + scrollTop - menuHeight - 5; // 留5px边距
        }

        // 确保菜单不会超出左边界和上边界
        left = Math.max(left, scrollLeft + 5);
        top = Math.max(top, scrollTop + 5);

        // 设置最终位置
        menu.css({
            left: left + 'px',
            top: top + 'px',
            display: 'block'
        });

        // 根据文件类型启用/禁用菜单项
        const openItem = menu.find('.context-menu-item').eq(0);
        if (isDirectory) {
            openItem.addClass('disabled');
        } else {
            openItem.removeClass('disabled');
        }
    },

    /**
     * 隐藏右键菜单
     */
    hideContextMenu: function() {
        $('#context-menu').hide();
        this.contextMenuTarget = null;
    },

    /**
     * 创建新文件
     */
    createNewFile: function() {
        const fileName = prompt('请输入文件名:');
        if (!fileName) return;

        let parentPath = '';
        if (this.selectedFile) {
            const selectedItem = $(`.file-tree-item[data-path="${this.selectedFile}"]`);
            if (selectedItem.data('is-directory')) {
                parentPath = this.selectedFile;
            } else {
                // 获取父目录
                const pathParts = this.selectedFile.split('/');
                pathParts.pop();
                parentPath = pathParts.join('/');
            }
        }

        const fullPath = parentPath ? `${parentPath}/${fileName}` : fileName;

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/create`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                path: fullPath,
                content: '',
                is_directory: false
            }),
            success: (response) => {
                if (response.success) {
                    Utils.showAlert('文件创建成功', 'success');
                    this.refreshFileTree();
                } else {
                    Utils.showAlert('创建文件失败: ' + response.message, 'danger');
                }
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('创建文件失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });

        this.hideContextMenu();
    },

    /**
     * 创建新文件夹
     */
    createNewFolder: function() {
        const folderName = prompt('请输入文件夹名:');
        if (!folderName) return;

        let parentPath = '';
        if (this.selectedFile) {
            const selectedItem = $(`.file-tree-item[data-path="${this.selectedFile}"]`);
            if (selectedItem.data('is-directory')) {
                parentPath = this.selectedFile;
            } else {
                // 获取父目录
                const pathParts = this.selectedFile.split('/');
                pathParts.pop();
                parentPath = pathParts.join('/');
            }
        }

        const fullPath = parentPath ? `${parentPath}/${folderName}` : folderName;

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/create`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                path: fullPath,
                is_directory: true
            }),
            success: (response) => {
                if (response.success) {
                    Utils.showAlert('文件夹创建成功', 'success');
                    this.refreshFileTree();
                } else {
                    Utils.showAlert('创建文件夹失败: ' + response.message, 'danger');
                }
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('创建文件夹失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });

        this.hideContextMenu();
    },

    /**
     * 重命名文件
     */
    renameFile: function() {
        if (!this.contextMenuTarget && !this.selectedFile) {
            Utils.showAlert('请先选择一个文件或文件夹', 'warning');
            return;
        }

        const targetPath = this.contextMenuTarget ? this.contextMenuTarget.path : this.selectedFile;
        const oldName = targetPath.split('/').pop();
        const newName = prompt('请输入新名称:', oldName);

        if (!newName || newName === oldName) {
            this.hideContextMenu();
            return;
        }

        const pathParts = targetPath.split('/');
        pathParts.pop();
        const newPath = pathParts.length > 0 ? `${pathParts.join('/')}/${newName}` : newName;

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/rename`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                old_path: targetPath,
                new_path: newPath
            }),
            success: (response) => {
                if (response.success) {
                    Utils.showAlert('重命名成功', 'success');
                    this.refreshFileTree();

                    // 如果重命名的文件当前已打开，需要更新标签页
                    if (this.openTabs.has(targetPath)) {
                        const tabInfo = this.openTabs.get(targetPath);
                        this.openTabs.delete(targetPath);
                        tabInfo.filePath = newPath;
                        tabInfo.fileName = newName;
                        this.openTabs.set(newPath, tabInfo);

                        // 更新标签页显示
                        const tab = $(`.editor-tab[data-tab-id="${tabInfo.tabId}"]`);
                        tab.attr('data-file-path', newPath);
                        tab.find('.tab-name').text(newName);
                    }
                } else {
                    Utils.showAlert('重命名失败: ' + response.message, 'danger');
                }
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('重命名失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });

        this.hideContextMenu();
    },

    /**
     * 删除文件
     */
    deleteFile: function() {
        // 检查是否有选中的文件
        let filesToDelete = [];
        
        if (this.selectedFiles.size > 1) {
            // 优先多选模式 - 添加所有选中的文件
            this.selectedFiles.forEach(path => {
                filesToDelete.push({
                    path: path,
                    name: path.split('/').pop()
                });
            });
        } else if (this.contextMenuTarget) {
            // 其次 - 当前右键点击的文件
            filesToDelete.push({
                path: this.contextMenuTarget.path,
                name: this.contextMenuTarget.path.split('/').pop()
            });
        } else if (this.selectedFile) {
            // 当前选中的文件(opened)
            filesToDelete.push({
                path: this.selectedFile,
                name: this.selectedFile.split('/').pop()
            });
        }

        // 检查是否有要删除的文件
        if (filesToDelete.length === 0) {
            Utils.showAlert('请先选择一个或多个文件或文件夹', 'warning');
            this.hideContextMenu();
            return;
        }

        // 构建确认消息
        let confirmMessage;
        if (filesToDelete.length === 1) {
            confirmMessage = `确定要删除 "${filesToDelete[0].name}" 吗？此操作不可撤销。`;
        } else {
            const fileNames = filesToDelete.slice(0, 5).map(f => f.name).join('", "');
            const moreFiles = filesToDelete.length > 5 ? ` 等${filesToDelete.length}个文件` : '';
            confirmMessage = `确定要删除 "${fileNames}"${moreFiles} 吗？此操作不可撤销。`;
        }

        if (!confirm(confirmMessage)) {
            this.hideContextMenu();
            return;
        }

        // 如果只有一个文件，使用原有的删除API
        if (filesToDelete.length === 1) {
            const targetPath = filesToDelete[0].path;
            
            $.ajax({
                url: `/aicode/api/projects/${this.currentProjectId}/files/delete`,
                method: 'DELETE',
                contentType: 'application/json',
                data: JSON.stringify({
                    path: targetPath
                }),
                success: (response) => {
                    if (response.success) {
                        Utils.showAlert('删除成功', 'success');
                        this.refreshFileTree();

                        // 如果删除的文件当前已打开，关闭对应的标签页
                        if (this.openTabs.has(targetPath)) {
                            const tabInfo = this.openTabs.get(targetPath);
                            this.closeTab(tabInfo.tabId);
                        }
                    } else {
                        Utils.showAlert('删除失败: ' + response.message, 'danger');
                    }
                },
                error: (xhr) => {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('删除失败: ' + (response.error || response.message || '未知错误'), 'danger');
                }
            });
        } else {
            // 使用批量删除API
            const paths = filesToDelete.map(f => f.path);
            
            $.ajax({
                url: `/aicode/api/projects/${this.currentProjectId}/files/batch_delete`,
                method: 'DELETE',
                contentType: 'application/json',
                data: JSON.stringify({
                    paths: paths
                }),
                success: (response) => {
                    if (response.success) {
                        Utils.showAlert(`批量删除成功: ${response.success_count} 个文件`, 'success');
                        this.refreshFileTree();

                        // 关闭已删除文件的标签页
                        paths.forEach(path => {
                            if (this.openTabs.has(path)) {
                                const tabInfo = this.openTabs.get(path);
                                this.closeTab(tabInfo.tabId);
                            }
                        });
                    } else {
                        // 部分删除成功
                        if (response.success_count > 0) {
                            Utils.showAlert(`部分删除成功: ${response.success_count} 个文件成功，${response.failed_count} 个文件失败`, 'warning');
                            this.refreshFileTree();
                            
                            // 关闭已删除文件的标签页
                            paths.forEach(path => {
                                if (this.openTabs.has(path)) {
                                    const tabInfo = this.openTabs.get(path);
                                    this.closeTab(tabInfo.tabId);
                                }
                            });
                        } else {
                            Utils.showAlert('批量删除失败: ' + response.message, 'danger');
                        }
                        
                        // 显示失败的文件详情
                        if (response.failed_files && response.failed_files.length > 0) {
                            console.log('删除失败的文件:', response.failed_files);
                        }
                    }
                },
                error: (xhr) => {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('批量删除失败: ' + (response.error || response.message || '未知错误'), 'danger');
                }
            });
        }

        this.hideContextMenu();
    },

    /**
     * 下载文件
     */
    downloadFile: function() {
        let filesToDownload = [];

        // 优先使用右键菜单选中的文件
        if (this.contextMenuTarget) {
            if (!this.contextMenuTarget.isDirectory) {
                filesToDownload.push(this.contextMenuTarget.path);
            }
        } else if (this.selectedFiles.size > 0) {
            // 多选模式 - 过滤掉文件夹，只保留文件
            this.selectedFiles.forEach(path => {
                const item = $(`.file-tree-item[data-path="${path}"]`);
                const isDirectory = item.data('is-directory');
                if (!isDirectory) {
                    filesToDownload.push(path);
                }
            });
        } else if (this.selectedFile) {
            // 单选模式
            const isDirectory = $(`.file-tree-item[data-path="${this.selectedFile}"]`).data('is-directory');
            if (!isDirectory) {
                filesToDownload.push(this.selectedFile);
            }
        }

        // 检查是否有可下载的文件
        if (filesToDownload.length === 0) {
            Utils.showAlert('没有可下载的文件（文件夹不能下载）', 'warning');
            this.hideContextMenu();
            return;
        }

        // 如果只有一个文件，直接下载
        if (filesToDownload.length === 1) {
            const downloadUrl = `/aicode/api/projects/${this.currentProjectId}/files/download?path=${encodeURIComponent(filesToDownload[0])}`;
            window.open(downloadUrl, '_blank');
        } else {
            // 多个文件，使用批量下载API（下载为ZIP）
            this.downloadMultipleFiles(filesToDownload);
        }

        this.hideContextMenu();
    },

    /**
     * 下载多个文件（打包为ZIP）
     */
    downloadMultipleFiles: function(paths) {
        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/download_multiple`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ paths: paths }),
            xhrFields: {
                responseType: 'blob'
            },
            success: (blob) => {
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `files_${this.currentProjectId}.zip`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                Utils.showAlert(`成功下载 ${paths.length} 个文件`, 'success');
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('批量下载失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });
    },

    /**
     * 刷新文件树
     */
    refreshFileTree: function() {
        this.expandedFolders.clear();
        this.loadFileTree();
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 点击空白处隐藏右键菜单
        $(document).on('click', () => {
            this.hideContextMenu();
        });

        // 阻止右键菜单的默认行为
        $('#context-menu').on('contextmenu', (e) => {
            e.preventDefault();
        });

        // 键盘快捷键
        $(document).on('keydown', (e) => {
            // Ctrl+S 保存文件
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveCurrentFile();
            }

            // Ctrl+W 关闭当前标签页
            if (e.ctrlKey && e.key === 'w') {
                e.preventDefault();
                if (this.activeTabId) {
                    this.closeTab(this.activeTabId);
                }
            }
        });

        // 阻止文件树项目的默认右键菜单
        $('.file-tree-content').on('contextmenu', '.file-tree-item', (e) => {
            e.preventDefault();
        });

        // 文件树点击事件委托
        $('.file-tree-content').on('click', '.file-tree-item', (e) => {
            const $item = $(e.currentTarget);
            const path = $item.data('path');
            const isDirectory = $item.data('is-directory');
            this.selectFile(path, isDirectory, e);
        });

        // 文件树面板宽度调整功能
        this.initResizeHandle();
        
        // 绑定文件树显示/隐藏功能
        $('#toggle-file-tree').on('click', function() {
            const container = $('#file-manager-container');
            container.toggleClass('file-tree-hidden');
        });
        
        $('#show-file-tree').on('click', function() {
            const container = $('#file-manager-container');
            container.removeClass('file-tree-hidden');
        });
    },

    /**
     * 初始化调整手柄
     */
    initResizeHandle: function() {
        const resizeHandle = document.getElementById('resize-handle');
        const fileTreePanel = document.querySelector('.file-tree-panel');
        const container = document.querySelector('.file-manager-container');

        if (!resizeHandle || !fileTreePanel || !container) return;

        let isResizing = false;
        let startX = 0;
        let startWidth = 0;

        // 鼠标按下事件
        resizeHandle.addEventListener('mousedown', (e) => {
            isResizing = true;
            startX = e.clientX;
            startWidth = fileTreePanel.offsetWidth;

            // 添加临时样式
            document.body.style.cursor = 'col-resize';
            document.body.style.userSelect = 'none';

            // 添加全局事件监听器
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            // 防止选中文本
            e.preventDefault();
        });

        // 鼠标移动事件
        function handleMouseMove(e) {
            if (!isResizing) return;

            const deltaX = e.clientX - startX;
            let newWidth = startWidth + deltaX;

            // 限制最小和最大宽度
            const minWidth = 200;
            const maxWidth = 600;

            if (newWidth < minWidth) {
                newWidth = minWidth;
            } else if (newWidth > maxWidth) {
                newWidth = maxWidth;
            }

            // 设置新宽度
            fileTreePanel.style.width = newWidth + 'px';

            // 保存宽度到localStorage
            localStorage.setItem('file-tree-width', newWidth);
        }

        // 鼠标释放事件
        function handleMouseUp() {
            isResizing = false;

            // 恢复样式
            document.body.style.cursor = '';
            document.body.style.userSelect = '';

            // 移除全局事件监听器
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        }

        // 恢复上次的宽度设置
        const savedWidth = localStorage.getItem('file-tree-width');
        if (savedWidth) {
            const width = parseInt(savedWidth);
            if (width >= 200 && width <= 600) {
                fileTreePanel.style.width = width + 'px';
            }
        }
    },

    /**
     * 更新光标信息
     */
    updateCursorInfo: function(editor, tabId) {
        const cursor = editor.getCursor();
        const line = cursor.line + 1;
        const col = cursor.ch + 1;
        const selection = editor.getSelection();
    },

    /**
     * 自动保存文件
     */
    autoSaveFile: function(path) {
        const tabInfo = this.openTabs.get(path);
        if (!tabInfo || !tabInfo.modified) return;

        const content = tabInfo.editor.getValue();

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/save`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                path: path,
                content: content
            }),
            success: (response) => {
                if (response.success) {
                    this.markTabAsSaved(path);
                    console.log('文件自动保存成功:', path);
                }
            },
            error: (xhr) => {
                console.error('自动保存失败:', xhr);
            }
        });
    },

    /**
     * 格式化代码
     */
    formatCode: function() {
        if (!this.activeTabId) return;

        const editor = this.editors.get(this.activeTabId);
        if (!editor) return;

        // 简单的代码格式化（主要是缩进）
        const content = editor.getValue();
        const lines = content.split('\n');
        let indentLevel = 0;
        const indentSize = 4;

        const formattedLines = lines.map(line => {
            const trimmed = line.trim();
            if (!trimmed) return '';

            // 简单的缩进逻辑
            if (trimmed.endsWith('{') || trimmed.endsWith(':')) {
                const formatted = ' '.repeat(indentLevel * indentSize) + trimmed;
                indentLevel++;
                return formatted;
            } else if (trimmed.startsWith('}')) {
                indentLevel = Math.max(0, indentLevel - 1);
                return ' '.repeat(indentLevel * indentSize) + trimmed;
            } else {
                return ' '.repeat(indentLevel * indentSize) + trimmed;
            }
        });

        editor.setValue(formattedLines.join('\n'));
    },

    /**
     * 切换主题
     */
    toggleTheme: function() {
        const currentTheme = this.currentTheme || 'monokai';
        const newTheme = currentTheme === 'monokai' ? 'default' : 'monokai';

        this.editors.forEach(editor => {
            editor.setOption('theme', newTheme);
        });

        this.currentTheme = newTheme;
    },

    /**
     * 查找和替换
     */
    showFindReplace: function() {
        if (!this.activeTabId) return;

        const editor = this.editors.get(this.activeTabId);
        if (!editor) return;

        // 触发CodeMirror的查找功能
        editor.execCommand('findPersistent');
    },

    /**
     * 跳转到指定行
     */
    goToLine: function() {
        if (!this.activeTabId) return;

        const editor = this.editors.get(this.activeTabId);
        if (!editor) return;

        const lineNumber = prompt('跳转到行号:');
        if (lineNumber && !isNaN(lineNumber)) {
            const line = parseInt(lineNumber) - 1;
            editor.setCursor(line, 0);
            editor.focus();
        }
    },

    /**
     * 获取文件统计信息
     */
    getFileStats: function() {
        if (!this.activeTabId) return null;

        const editor = this.editors.get(this.activeTabId);
        if (!editor) return null;

        const content = editor.getValue();
        const lines = content.split('\n').length;
        const chars = content.length;
        const words = content.split(/\s+/).filter(word => word.length > 0).length;

        return { lines, chars, words };
    },

    /**
     * 复制文件
     */
    copyFile: function() {
        if (!this.contextMenuTarget && !this.selectedFile) {
            Utils.showAlert('请先选择一个文件或文件夹', 'warning');
            return;
        }

        // 优先使用右键菜单选中的文件
        const targetPath = this.contextMenuTarget ? this.contextMenuTarget.path : this.selectedFile;
        this.clipboard = targetPath;
        this.clipboardOperation = 'copy';

        Utils.showAlert('已复制到剪贴板', 'info');
        this.hideContextMenu();
    },

    /**
     * 剪切文件
     */
    cutFile: function() {
        if (!this.contextMenuTarget && !this.selectedFile) {
            Utils.showAlert('请先选择一个文件或文件夹', 'warning');
            return;
        }

        // 优先使用右键菜单选中的文件
        const targetPath = this.contextMenuTarget ? this.contextMenuTarget.path : this.selectedFile;
        this.clipboard = targetPath;
        this.clipboardOperation = 'cut';

        // 添加视觉反馈
        $(`.file-tree-item[data-path="${targetPath}"]`).addClass('cut-item');

        Utils.showAlert('已剪切到剪贴板', 'info');
        this.hideContextMenu();
    },

    /**
     * 粘贴文件
     */
    pasteFile: function() {
        if (!this.clipboard) {
            Utils.showAlert('剪贴板为空', 'warning');
            this.hideContextMenu();
            return;
        }

        let targetDir = '';
        if (this.selectedFile) {
            const selectedItem = $(`.file-tree-item[data-path="${this.selectedFile}"]`);
            if (selectedItem.data('is-directory')) {
                targetDir = this.selectedFile;
            } else {
                // 获取父目录
                const pathParts = this.selectedFile.split('/');
                pathParts.pop();
                targetDir = pathParts.join('/');
            }
        }

        const sourcePath = this.clipboard;
        const fileName = sourcePath.split('/').pop();
        const targetPath = targetDir ? `${targetDir}/${fileName}` : fileName;

        if (this.clipboardOperation === 'copy') {
            this.copyFileToTarget(sourcePath, targetPath);
        } else if (this.clipboardOperation === 'cut') {
            this.moveFileToTarget(sourcePath, targetPath);
        }

        this.hideContextMenu();
    },

    /**
     * 复制文件到目标位置
     */
    copyFileToTarget: function(sourcePath, targetPath) {
        // 先读取源文件内容
        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/content`,
            method: 'GET',
            data: { path: sourcePath },
            success: (response) => {
                if (response.success) {
                    // 创建新文件
                    $.ajax({
                        url: `/aicode/api/projects/${this.currentProjectId}/files/create`,
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            path: targetPath,
                            content: response.content || '',
                            is_directory: response.file_info.is_directory
                        }),
                        success: (createResponse) => {
                            if (createResponse.success) {
                                Utils.showAlert('文件复制成功', 'success');
                                this.refreshFileTree();
                            } else {
                                Utils.showAlert('复制失败: ' + createResponse.message, 'danger');
                            }
                        },
                        error: (xhr) => {
                            const response = xhr.responseJSON || {};
                            Utils.showAlert('复制失败: ' + (response.error || response.message || '未知错误'), 'danger');
                        }
                    });
                } else {
                    Utils.showAlert('读取源文件失败: ' + response.message, 'danger');
                }
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('读取源文件失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });
    },

    /**
     * 移动文件到目标位置
     */
    moveFileToTarget: function(sourcePath, targetPath) {
        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/rename`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                old_path: sourcePath,
                new_path: targetPath
            }),
            success: (response) => {
                if (response.success) {
                    Utils.showAlert('文件移动成功', 'success');
                    this.refreshFileTree();

                    // 清空剪贴板
                    this.clipboard = null;
                    this.clipboardOperation = null;

                    // 移除视觉反馈
                    $('.file-tree-item').removeClass('cut-item');

                    // 如果移动的文件当前已打开，需要更新标签页
                    if (this.openTabs.has(sourcePath)) {
                        const tabInfo = this.openTabs.get(sourcePath);
                        this.openTabs.delete(sourcePath);
                        tabInfo.filePath = targetPath;
                        tabInfo.fileName = targetPath.split('/').pop();
                        this.openTabs.set(targetPath, tabInfo);

                        // 更新标签页显示
                        const tab = $(`.editor-tab[data-tab-id="${tabInfo.tabId}"]`);
                        tab.attr('data-file-path', targetPath);
                        tab.find('.tab-name').text(tabInfo.fileName);
                    }
                } else {
                    Utils.showAlert('移动失败: ' + response.message, 'danger');
                }
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('移动失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });
    },

    /**
     * 显示文件信息
     */
    showFileInfo: function() {
        if (!this.contextMenuTarget && !this.selectedFile) {
            Utils.showAlert('请先选择一个文件或文件夹', 'warning');
            return;
        }

        // 优先使用右键菜单选中的文件
        const targetPath = this.contextMenuTarget ? this.contextMenuTarget.path : this.selectedFile;

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/files/content`,
            method: 'GET',
            data: { path: targetPath },
            success: (response) => {
                if (response.success) {
                    const fileInfo = response.file_info;
                    const sizeStr = this.formatFileSize(fileInfo.size);
                    const typeStr = fileInfo.is_directory ? '文件夹' : '文件';

                    const info = `
                        文件名: ${fileInfo.name}
                        类型: ${typeStr}
                        大小: ${sizeStr}
                        修改时间: ${fileInfo.modified_time}
                        扩展名: ${fileInfo.extension || '无'}
                        MIME类型: ${fileInfo.mime_type || '无'}
                        AI生成: ${fileInfo.is_ai_generated ? '是' : '否'}
                    `;

                    alert(info);
                } else {
                    Utils.showAlert('获取文件信息失败: ' + response.message, 'danger');
                }
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('获取文件信息失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });

        this.hideContextMenu();
    },

    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 保存所有打开的文件
     */
    saveAllFiles: function() {
        let savedCount = 0;
        let totalModified = 0;

        this.openTabs.forEach((tabInfo, path) => {
            if (tabInfo.modified) {
                totalModified++;
                const content = tabInfo.editor.getValue();

                $.ajax({
                    url: `/aicode/api/projects/${this.currentProjectId}/files/save`,
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        path: path,
                        content: content
                    }),
                    success: (response) => {
                        if (response.success) {
                            this.markTabAsSaved(path);
                            savedCount++;

                            if (savedCount === totalModified) {
                                Utils.showAlert(`已保存 ${savedCount} 个文件`, 'success');
                            }
                        }
                    },
                    error: (xhr) => {
                        console.error('保存文件失败:', path, xhr);
                    }
                });
            }
        });

        if (totalModified === 0) {
            Utils.showAlert('没有需要保存的文件', 'info');
        }
    },

    /**
     * 触发文件上传
     */
    triggerFileUpload: function() {
        $('#file-upload-input').click();
    },

    /**
     * 处理文件上传
     */
    handleFileUpload: function(event) {
        const files = event.target.files;
        if (!files || files.length === 0) {
            return;
        }

        // 获取目标路径
        let targetPath = '';
        if (this.selectedFile) {
            const selectedItem = $(`.file-tree-item[data-path="${this.selectedFile}"]`);
            if (selectedItem.data('is-directory')) {
                targetPath = this.selectedFile;
            } else {
                // 获取父目录
                const pathParts = this.selectedFile.split('/');
                pathParts.pop();
                targetPath = pathParts.join('/');
            }
        }

        // 上传每个文件
        let uploadCount = 0;
        const totalFiles = files.length;

        Array.from(files).forEach(file => {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('path', targetPath);

            $.ajax({
                url: `/aicode/api/projects/${this.currentProjectId}/files/upload`,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: (response) => {
                    uploadCount++;
                    if (response.success) {
                        //console.log(`文件上传成功: ${file.name}`);
                    } else {
                        Utils.showAlert(`上传 ${file.name} 失败: ${response.message}`, 'danger');
                    }

                    if (uploadCount === totalFiles) {
                        Utils.showAlert(`成功上传 ${uploadCount} 个文件`, 'success');
                        this.refreshFileTree();
                    }
                },
                error: (xhr) => {
                    uploadCount++;
                    const response = xhr.responseJSON || {};
                    Utils.showAlert(`上传 ${file.name} 失败: ${response.error || response.message || '未知错误'}`, 'danger');

                    if (uploadCount === totalFiles) {
                        this.refreshFileTree();
                    }
                }
            });
        });

        // 清空文件输入框，以便下次可以上传相同的文件
        event.target.value = '';
    },

    /**
     * 绑定文件树事件
     */
    bindFileTreeEvents: function() {
        // 移除之前的事件监听器，避免重复绑定
        $('.file-tree-content').off('click', '.file-tree-item');

        // 文件树点击事件委托
        $('.file-tree-content').on('click', '.file-tree-item', (e) => {
            const $item = $(e.currentTarget);
            const path = $item.data('path');
            const isDirectory = $item.data('is-directory');
            this.selectFile(path, isDirectory, e);
        });
    },

    /**
     * 获取所有可见的文件树项，按顺序排列
     */
    getAllVisibleFileItems: function() {
        return $('.file-tree-item').map(function() {
            return {
                path: $(this).data('path'),
                element: $(this)
            };
        }).get();
    },

    /**
     * 在两个文件路径之间选择所有文件
     */
    selectRangeBetween: function(startPath, endPath) {
        try {
            const allItems = this.getAllVisibleFileItems();
            let startIndex = -1;
            let endIndex = -1;

            //console.log('开始范围选择:', startPath, '->', endPath);

            // 找到开始和结束索引
            for (let i = 0; i < allItems.length; i++) {
                if (allItems[i].path === startPath) {
                    startIndex = i;
                }
                if (allItems[i].path === endPath) {
                    endIndex = i;
                }
                if (startIndex !== -1 && endIndex !== -1) {
                    break;
                }
            }

            // 检查索引是否有效
            if (startIndex < 0 || startIndex >= allItems.length ||
                endIndex < 0 || endIndex >= allItems.length) {
                console.er('❌ 范围选择索引无效:', 'startIndex=' + startIndex, 'endIndex=' + endIndex, '总数=' + allItems.length);
                return false;
            }

            // 如果找到了两个文件，选择它们之间的所有文件
            const minIndex = Math.min(startIndex, endIndex);
            const maxIndex = Math.max(startIndex, endIndex);

            for (let i = minIndex; i <= maxIndex; i++) {
                const item = allItems[i];
                if (item && item.element) {
                    item.element.addClass('selected');
                    this.selectedFiles.add(item.path);
                }
            }

            // 设置最后选择的文件为结束文件
            this.selectedFile = endPath;
            this.lastSelectedFile = endPath;

            // console.log('范围选择完成，共选中', this.selectedFiles.size, '个文件');
            return true;

        } catch (error) {
            console.error('范围选择出错:', error);
            return false;
        }
    },

    /**
     * 清除所有选择
     */
    clearSelection: function() {
        $('.file-tree-item').removeClass('selected');
        this.selectedFiles.clear();
        this.selectedFile = null;
        this.lastSelectedFile = null;
        this.isRangeSelecting = false;
    }
};
