function createDocCherry(id, content, aiMenu, mode = "defaultModel") {
    let cherry = null;
    var saveMenu = Cherry.createMenuHook('保存', {
        noIcon: true, name: '保存',
        onClick: function (selection) {
            saveContent();
            return selection
        }
    });

    // 气泡工具栏的通用助手菜单
    var askAiMenu = Cherry.createMenuHook('AI助手', {
        iconName: 'question',
        onClick: async function (selection) {
            const request = await showAskAI(`AI助手`, "请输入你的问题");
            if (request) {
                var content = await askAI(selection, request.text, enable_kb, request.deepThinking);
                const ask_button = await showCustomConfirm(`AI结果`, content, selection);
                return wrap_selection(selection, content, ask_button.action);
            }
            return selection;
        }
    });

    // 气泡工具栏的创建任务菜单
    var taskMenu = Cherry.createMenuHook('创建任务', {
        iconName: 'phone',
        onClick: async function (selection) {
            genTask(selection);
            return selection;
        }
    });

    // 首位置的浮动工具栏
    var floatAiMenu = Cherry.createMenuHook('AI', {
        noIcon: true, name: 'AI',
        onClick: async function () {
            //genTask();        
            const allLines = cherry.editor.editor.getValue().split('\n'); // 获取所有行
            if (allLines.length < 1) {
                return showAiChat();
            }
            const cursor = cherry.editor.editor.getCursor(); // 获取光标位置

            const currentLine = cursor.line;
            // 前10行文本
            const prevLinesStart = Math.max(0, currentLine - 10); // 前10行的起始行号，避免负数
            const prevLines = allLines.slice(prevLinesStart, currentLine).join('\n'); // 获取前10行的文本内容

            // 后10行文本
            const nextLinesEnd = Math.min(allLines.length - 1, currentLine + 10);
            const nextLines = allLines.slice(cursor.line + 1, nextLinesEnd).join('\n');

            return showAiChat(prevLines, nextLines);
        }
    });

    // 导入知识库菜单
    var kb_Menu = Cherry.createMenuHook('导入知识库', {
        noIcon: true, name: '导入知识库',
        onClick: async function (selection) {
            // 先保存当前内容
            saveContent();

            // 显示确认对话框
            const confirmed = confirm('确定要将当前文档导入到知识库吗？');
            if (!confirmed) {
                return selection;
            }

            try {
                // 调用后端API导入文档到知识库
                const response = await fetch(`/aicode/api/projects/${currentProjectId}/import_to_kb`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        doc_type: docType  // 'requirement' 或 'design'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showMessage('文档已成功导入到知识库', 'success');
                } else {
                    showMessage('导入失败: ' + result.message, 'danger');
                }
            } catch (error) {
                console.error('导入知识库失败:', error);
                showMessage('导入知识库失败', 'danger');
            }

            return selection;
        }
    });

    // 追加用于需求/设计的AI菜单
    const doc_menus = {
        aiMenuName: aiMenu,
        askAiMenuName: askAiMenu,
        taskMenuName: taskMenu,
        saveMenuName: saveMenu,
        kb_MenuName: kb_Menu,
        floatAiMenuName: floatAiMenu,
    };
    // cherry = createCherry(id, content , mode , doc_menus , ['saveMenuName', 'aiMenuName'])
    cherry = createCherry(id, content, mode, doc_menus, ['saveMenuName', 'aiMenuName'], ["askAiMenuName", "taskMenuName"], ['floatAiMenuName'], ['kb_MenuName'])

    loadContent(cherry);
    const previewer = document.querySelector('.cherry-previewer');
    if (previewer) {
        previewer.style.width = '40%';
    }
    const editor = document.querySelector('.cherry-editor');
    if (editor) {
        editor.style.width = '60%';
    }

    // 键盘快捷键
    $(document).on('keydown', (e) => {
        // Ctrl+S 保存文件
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveContent();
        }
    });

    return cherry;
}
function changeSaveBtn(title) {
    // 获取"保存"按钮的元素，并修改"保存"按钮的显示为：保存*
    const saveButton = document.querySelector('.cherry-toolbar-button.cherry-toolbar-保存');
    if (saveButton) {
        saveButton.textContent = title;
    }
}
// 加载内容
function loadContent(cherry) {
    let content = '';
    function afterChange(md, html) {
        //console.log('afterChange');
        if (content == md) {
            // 初始加载
            return false;
        }
        content = md
        changeSaveBtn('保存*');
        return false;
    }
    fetch(`/aicode/api/projects/${currentProjectId}/${apiEndpoint}`)
        .then(response => response.json())
        .then(data => {
            content = data[contentField] || '';
            cherry.setValue(content);
        })
        .catch(error => {
            showMessage(`需求加载失败:${error}`, 'danger');
        })
        .finally(() => {
            cherry.on('afterChange', afterChange);
        });
}

// 保存内容
function saveContent() {
    const content = cherry.getValue();
    const payload = {};
    payload[contentField] = content;

    fetch(`/aicode/api/projects/${currentProjectId}/${apiEndpoint}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
    })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                changeSaveBtn('保存');
                showMessage('保存成功', 'success');
            } else {
                showMessage('保存失败: ' + result.message, 'danger');
            }
        })
        .catch(error => {
            console.error('保存失败:', error);
            showMessage('保存失败', 'danger');
        });
}

function showAskAI(title, content) {
    return new Promise((resolve) => {
        // 生成唯一的模态框ID
        const modalId = 'askAiModal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="modal-header py-2 px-3">
                            <h6 class="modal-title" id="${modalId}Label">${title}</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body py-2 px-3">
                            <div class="mb-2">
                                <textarea class="form-control form-control-sm" id="askAiInput-${modalId}" rows="3" placeholder="请输入您的问题..."></textarea>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="deepThinking-${modalId}">
                                <label class="form-check-label small" for="deepThinking-${modalId}">深度思考</label>
                            </div>
                        </div>
                        <div class="modal-footer py-2 px-3 justify-content-center">
                            <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-sm btn-primary" id="confirmBtn-${modalId}">确认</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById(modalId);
        const confirmBtn = document.getElementById(`confirmBtn-${modalId}`);
        const inputElement = document.getElementById(`askAiInput-${modalId}`);
        const deepThinkingElement = document.getElementById(`deepThinking-${modalId}`);

        // 初始化并显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 处理确认按钮点击事件
        confirmBtn.addEventListener('click', () => {
            const inputValue = inputElement.value;
            const deepThinkingChecked = deepThinkingElement.checked;
            modal.hide();
            resolve({
                text: inputValue,
                deepThinking: deepThinkingChecked
            });
        });

        // 监听模态框隐藏事件，清理DOM
        modalElement.addEventListener('hidden.bs.modal', () => {
            // 延迟删除元素，确保动画完成
            setTimeout(() => {
                if (modalElement.parentNode) {
                    modalElement.parentNode.removeChild(modalElement);
                }
            }, 300);
        });
    });
}

// 优化选中内容
async function optimizeSelection(selection, ai_action, enable_kb) {
    const originalBtnHtml = $('#actionButtons').html();
    var timerId = showAIProgress(ai_action)

    try {
        // 调用后端API进行优化            
        const response = await fetch(`/aicode/api/projects/${currentProjectId}/documentai`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(
                { selection: selection, enable_kb: enable_kb, ai_action: ai_action, doc_type: docType }
            )
        });

        const result = await response.json();

        if (result.success) {
            return result.message;
        } else {
            Utils.showAlert('优化失败: ' + result.message, 'danger');
            return selection;
        }
    } catch (error) {
        Utils.showAlert('优化请求失败', 'danger');
        return selection;
    }
    finally {
        // 清除计时器并恢复按钮
        clearInterval(timerId);
        $('#actionButtons').html(originalBtnHtml);
    };
}

async function askAI(selection, user_input, enable_kb, deep_think) {
    const originalBtnHtml = $('#actionButtons').html();
    var timerId = showAIProgress('AI助手处理')

    try {
        // 调用后端API进行优化            
        const response = await fetch(`/aicode/api/projects/${currentProjectId}/askai`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(
                { selection: selection, enable_kb: enable_kb, user_input: user_input, doc_type: docType, deep_think: deep_think }
            )
        });

        const result = await response.json();

        if (result.success) {
            return result.message;
        } else {
            Utils.showAlert('AI处理失败: ' + result.message, 'danger');
            return selection;
        }
    } catch (error) {
        Utils.showAlert('AI处理失败', 'danger');
        return selection;
    }
    finally {
        // 清除计时器并恢复按钮
        clearInterval(timerId);
        $('#actionButtons').html(originalBtnHtml);
    };
}

// 任务创建对话框
function showGenTaskDialog(selection) {
    return new Promise((resolve) => {
        // 生成唯一的模态框ID
        const modalId = 'genTaskModal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header py-2 px-3">
                            <h6 class="modal-title" id="${modalId}Label">创建任务</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body py-3">
                            <div class="mb-3">
                                <div class="border rounded p-2 bg-light">
                                    <pre class="mb-0" style="white-space: pre-wrap; max-height: 150px; overflow-y: auto;">${selection}</pre>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer py-2 px-3 justify-content-center">
                            <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-sm btn-primary" id="createTaskBtn-${modalId}">创建任务</button>
                            <button type="button" class="btn btn-sm btn-success" id="createAndRunTaskBtn-${modalId}">创建并执行</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById(modalId);
        const createTaskBtn = document.getElementById(`createTaskBtn-${modalId}`);
        const createAndRunTaskBtn = document.getElementById(`createAndRunTaskBtn-${modalId}`);

        // 初始化并显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 处理创建任务按钮点击事件
        createTaskBtn.addEventListener('click', () => {
            modal.hide();
            resolve({ action: 'create', run: false });
        });

        // 处理创建并执行按钮点击事件
        createAndRunTaskBtn.addEventListener('click', () => {
            modal.hide();
            resolve({ action: 'create', run: true });
        });

        // 监听模态框隐藏事件，清理DOM
        modalElement.addEventListener('hidden.bs.modal', () => {
            // 延迟删除元素，确保动画完成
            setTimeout(() => {
                if (modalElement.parentNode) {
                    modalElement.parentNode.removeChild(modalElement);
                }
            }, 300);

            // 如果用户没有点击任何按钮，不执行任何操作
            resolve({ action: 'cancel' });
        });
    });
}

// 更新genTask函数以使用新的对话框
async function genTask(selection) {
    const result = await showGenTaskDialog(selection);

    // 如果用户取消操作，则不执行任何操作
    if (result.action === 'cancel') {
        return;
    }

    fetch(`/aicode/api/projects/${currentProjectId}/gen_task`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(
            { selection: selection, doc_type: docType, enable_kb: enable_kb, is_run: result.run }
        )
    })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                Utils.showAlert('任务创建成功', 'success');
            } else {
                Utils.showAlert('任务创建失败: ' + result.message, 'danger');
            }
        })
        .catch(error => {
            Utils.showAlert('任务创建失败', 'danger');
        })
}

// 绘图配置对话框
function showDrawConfig(title, description, selection) {
    return new Promise((resolve) => {
        // 生成唯一的模态框ID
        const modalId = 'drawConfigModal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="modal-header py-2 px-3">
                            <h6 class="modal-title" id="${modalId}Label">${title}</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body py-2 px-3">
                            <div class="mb-2">
                                <label class="form-label small mb-1">绘图类型</label>
                                <select class="form-select form-select-sm" id="drawType-${modalId}">
                                    <option value="流程图">流程图</option>
                                    <option value=">时序图">时序图</option>
                                    <option value="类图">类图</option>
                                    <option value="状态图">状态图</option>
                                    <option value="实体关系图">实体关系图</option>
                                    <option value="甘特图">甘特图</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label class="form-label small mb-1">补充说明</label>
                                <textarea class="form-control form-control-sm" id="drawPrompt-${modalId}" rows="2" placeholder="请详细描述你想要绘制的图表内容..." style="font-size: 0.875rem;"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer py-2 px-3 justify-content-center">
                            <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-sm btn-primary" id="confirmDrawBtn-${modalId}">确认生成</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById(modalId);
        const confirmBtn = document.getElementById(`confirmDrawBtn-${modalId}`);
        const drawTypeElement = document.getElementById(`drawType-${modalId}`);
        const drawPromptElement = document.getElementById(`drawPrompt-${modalId}`);

        // 初始化并显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 处理确认按钮点击事件
        confirmBtn.addEventListener('click', () => {
            const drawType = drawTypeElement.value;
            const drawPrompt = drawPromptElement.value;
            modal.hide();
            resolve({
                type: drawType,
                prompt: drawPrompt
            });
        });

        // 监听模态框隐藏事件，清理DOM
        modalElement.addEventListener('hidden.bs.modal', () => {
            // 延删除元素，确保动画完成
            setTimeout(() => {
                if (modalElement.parentNode) {
                    modalElement.parentNode.removeChild(modalElement);
                }
            }, 300);
        });
    });
}
async function drawAI(selection, draw_type, prompt, enable_kb, deep_think = false) {
    const originalBtnHtml = $('#actionButtons').html();
    var timerId = showAIProgress('AI绘图')

    try {
        // 调用后端API进行优化            
        const response = await fetch(`/aicode/api/projects/${currentProjectId}/drawai`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(
                { selection: selection, enable_kb: enable_kb, draw_type: draw_type, prompt: prompt, deep_think: deep_think }
            )
        });

        const result = await response.json();

        if (result.success) {
            return result.message;
        } else {
            Utils.showAlert('AI处理失败: ' + result.message, 'danger');
            return "";
        }
    } catch (error) {
        Utils.showAlert('AI处理失败', 'danger');
        return "";
    }
    finally {
        // 清除计时器并恢复按钮
        clearInterval(timerId);
        $('#actionButtons').html(originalBtnHtml);
    };
}
// 引导式需求生成
function showAiChat(prevLines = "", nextLines = "") {
    return new Promise((resolve) => {
        // 生成唯一的模态框ID
        const modalId = 'aiChatModal-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-xl modal-dialog-scrollable">
                    <div class="modal-content" style="height: 85vh;">
                        <div class="modal-header py-2 px-3">
                            <h6 class="modal-title" id="${modalId}Label">AI引导式需求生成</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body py-2 px-3 d-flex flex-column" style="overflow: hidden;">
                            <div id="chatMessages-${modalId}" class="flex-grow-1 overflow-auto mb-3 border rounded p-3 bg-light" style="min-height: 0;">
                                <div class="text-muted text-center py-5">
                                    <p>欢迎使用AI引导式需求生成功能</p>
                                    <p>请描述您的需求，AI将引导您完善需求规格</p>
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <textarea class="form-control form-control-sm" id="chatInput-${modalId}" rows="2" placeholder="请输入您的需求..." style="resize: none;"></textarea>
                                <div class="d-flex flex-column gap-1">
                                    <button type="button" class="btn btn-sm btn-primary" id="sendBtn-${modalId}">发送</button>
                                    <button type="button" class="btn btn-sm btn-success d-none" id="acceptBtn-${modalId}">采纳</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将模态框添加到页面中
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById(modalId);
        const chatMessages = document.getElementById(`chatMessages-${modalId}`);
        const chatInput = document.getElementById(`chatInput-${modalId}`);
        const sendBtn = document.getElementById(`sendBtn-${modalId}`);
        const acceptBtn = document.getElementById(`acceptBtn-${modalId}`);

        // 初始化并显示模态框
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // 会话ID
        let sessionId = 'req_' + Date.now();
        let finalRequirement = '';

        // 添加消息到聊天框
        function addMessage(role, content, isLoading = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `mb-3 ${role === 'user' ? 'text-end' : ''}`;

            const bubble = document.createElement('div');
            bubble.className = `d-inline-block p-2 rounded ${role === 'user' ? 'bg-primary text-white' : 'bg-white border'}`;
            bubble.style.maxWidth = '80%';
            bubble.style.textAlign = 'left';
            bubble.style.whiteSpace = 'pre-wrap';

            if (isLoading) {
                bubble.innerHTML = '<span class="text-muted"><i class="fas fa-spinner fa-spin"></i> AI思考中...</span>';
                messageDiv.setAttribute('data-loading', 'true');
            } else {
                bubble.textContent = content;
            }

            messageDiv.appendChild(bubble);
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return messageDiv;
        }

        // 发送消息
        async function sendMessage() {
            const userMessage = chatInput.value.trim();
            if (!userMessage) return;

            // 清空第一次的欢迎消息
            if (chatMessages.children.length === 1 && chatMessages.children[0].classList.contains('text-muted')) {
                chatMessages.innerHTML = '';
            }

            // 添加用户消息
            addMessage('user', userMessage);
            chatInput.value = '';
            sendBtn.disabled = true;
            chatInput.disabled = true;

            // 添加加载中的AI消息
            const loadingMsg = addMessage('assistant', '', true);

            try {
                const response = await fetch(`/aicode/api/projects/${currentProjectId}/chatai`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        session_id: sessionId,
                        message: userMessage,
                        enable_kb: enable_kb,
                        prev_lines: prevLines || '',
                        next_lines: nextLines || '',
                    })
                });

                const result = await response.json();

                // 删除加载消息
                loadingMsg.remove();

                if (result.success) {
                    // 添加AI响应
                    addMessage('assistant', result.message);

                    // 检查是否是最终需求规格
                    if (result.is_final) {
                        finalRequirement = result.message;
                        acceptBtn.classList.remove('d-none');
                    }
                } else {
                    addMessage('assistant', '抱歉，处理失败：' + result.message);
                }
            } catch (error) {
                loadingMsg.remove();
                addMessage('assistant', '抱歉，请求失败，请重试');
            } finally {
                sendBtn.disabled = false;
                chatInput.disabled = false;
                chatInput.focus();
            }
        }

        // 发送按钮点击事件
        sendBtn.addEventListener('click', sendMessage);

        // 回车发送（Ctrl+Enter换行）
        chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 采纳按钮点击事件
        acceptBtn.addEventListener('click', () => {
            modal.hide();
            resolve(finalRequirement);
        });

        // 监听模态框隐藏事件，清理DOM
        modalElement.addEventListener('hidden.bs.modal', () => {
            setTimeout(() => {
                if (modalElement.parentNode) {
                    modalElement.parentNode.removeChild(modalElement);
                }
            }, 300);

            // 如果用户没有采纳，返回空
            if (!finalRequirement) {
                resolve('');
            }
        });
    });
}