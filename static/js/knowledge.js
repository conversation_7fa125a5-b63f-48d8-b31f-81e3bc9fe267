let currentProjectId = null;
let currentKnowledgeBase = null;
let currentSearchType = 'documents'; // 默认搜索文档知识库

$(document).ready(function () {
    // 从URL参数或父页面获取项目ID
    const urlParams = new URLSearchParams(window.location.search);
    currentProjectId = urlParams.get('project_id');
    if (!currentProjectId) {
        alert('缺少项目ID参数');
        window.close();
        return;
    }

    $('#knowledgeSection').show();
    $('#uploadBtn').prop('disabled', false);
    $('#rebuildCodeBtn').prop('disabled', false);
    $('#clearBtn').prop('disabled', false);
    loadKnowledgeBase();

    // 文件选择事件
    $('#knowledgeFile').change(function () {
        const file = this.files[0];
        if (file && !$('#knowledgeName').val()) {
            const fileName = file.name.replace(/\.[^/.]+$/, "");
            $('#knowledgeName').val(fileName);
        }

        // 根据文件扩展名自动选择文件类型和显示JSON处理选项
        if (file) {
            const fileExtension = file.name.toLowerCase().split('.').pop();
            if (fileExtension === 'json') {
                $('#knowledgeType').val('json_data');
                $('#jsonProcessingOptions').show();
            } else {
                if ($('#knowledgeType').val() === 'json_data') {
                    $('#knowledgeType').val('knowledge');
                }
                $('#jsonProcessingOptions').hide();
            }
        }
    });

    // 文件类型选择事件
    $('#knowledgeType').change(function () {
        const selectedType = $(this).val();
        if (selectedType === 'json_data') {
            $('#jsonProcessingOptions').show();
        } else {
            $('#jsonProcessingOptions').hide();
        }
    });

    // 分块阈值选择事件
    $('#jsonChunkThreshold').change(function () {
        const selectedValue = $(this).val();
        if (selectedValue === 'custom') {
            $('#jsonCustomThresholdOptions').show();
        } else {
            $('#jsonCustomThresholdOptions').hide();
        }
    });

    // 搜索框回车事件
    $('#searchQuery').keypress(function (e) {
        if (e.which === 13) {
            searchKnowledge();
        }
    });
    
    // 设置下拉框初始值
    $('#searchType').val(currentSearchType);
    
    // 监听下拉框变化事件
    $('#searchType').change(function() {
        currentSearchType = $(this).val();
    });
});

// 设置搜索类型 (旧方法，保留以确保兼容性)
function setSearchType(type) {
    currentSearchType = type;
    
    // 更新UI
    if (type === 'documents') {
        $('#search-documents').addClass('active');
        $('#search-code').removeClass('active');
    } else {
        $('#search-documents').removeClass('active');
        $('#search-code').addClass('active');
    }
}

// 新增函数：处理下拉框搜索类型切换
function changeSearchType() {
    currentSearchType = document.getElementById('searchType').value;
}

// 切换搜索结果的显示/隐藏
function toggleSearchResults() {
    const resultsBody = $('#searchResultsBody');
    const toggleIcon = $('#toggleIcon');
    
    if (resultsBody.is(':visible')) {
        resultsBody.hide();
        toggleIcon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
    } else {
        resultsBody.show();
        toggleIcon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
    }
}

// 加载知识库
function loadKnowledgeBase() {
    $.get(`/aicode/api/projects/${currentProjectId}/knowledge_bases`, function (response) {
        if (response.success && response.knowledge_base) {
            currentKnowledgeBase = response.knowledge_base;
            updateKnowledgeStats();
            loadKnowledgeDocuments();
        } else {
            showNotification('加载知识库失败: ' + (response.message || '未知错误'), 'error');
        }
    }).fail(function () {
        showNotification('加载知识库失败', 'error');
    });
    
    // 加载代码知识库统计信息
    loadCodeKnowledgeStats();
}

// 加载代码知识库统计信息
function loadCodeKnowledgeStats() {
    $.get(`/aicode/api/projects/${currentProjectId}/code_knowledge_stats`, function (response) {
        if (response.success) {
            $('#totalCodeFiles').text(response.stats.file_count || 0);
            $('#totalCodeChunks').text(response.stats.chunk_count || 0);
        } else {
            $('#totalCodeFiles').text('0');
            $('#totalCodeChunks').text('0');
        }
    }).fail(function () {
        $('#totalCodeFiles').text('0');
        $('#totalCodeChunks').text('0');
    });
}

// 加载知识库文档列表
function loadKnowledgeDocuments() {
    $.get(`/aicode/api/knowledge_bases/${currentProjectId}/documents`, function (response) {
        if (response.success) {
            renderKnowledgeTable(response.documents || []);
        } else {
            showNotification('加载文档列表失败: ' + response.message, 'error');
        }
    }).fail(function () {
        showNotification('加载文档列表失败', 'error');
    });
}

// 更新知识库统计信息
function updateKnowledgeStats() {
    if (currentKnowledgeBase) {
        $('#totalDocuments').text(currentKnowledgeBase.document_count || 0);
        $('#totalChunks').text(currentKnowledgeBase.chunk_count || 0);
    }
}

// 渲染知识库表格
function renderKnowledgeTable(documents) {
    const tbody = $('#docs-table-body');
    tbody.empty();

    if (documents.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted py-4">
                    <i class="fas fa-database fa-2x mb-2"></i><br>
                    暂无知识库数据
                </td>
            </tr>
        `);
        return;
    }

    documents.forEach(function (doc) {
        // 处理文档状态显示
        const status = doc?.status || 'completed';
        const statusBadge = status === 'completed' ? 'bg-success' :
            status === 'processing' ? 'bg-warning' :
                status === 'failed' ? 'bg-danger' : 'bg-secondary';
        const statusText = status === 'completed' ? '已完成' :
            status === 'processing' ? '处理中' :
                status === 'failed' ? '失败' : '未知';

        // 根据文档名称显示特定的显示名称
        let displayName = doc.name || '未命名';
        if (displayName === 'requirement.md') {
            displayName = '需求管理文档';
        } else if (displayName === 'design.md') {
            displayName = '设计管理文档';
        }

        const row = $(`
            <tr>
                <td>
                    <strong>${displayName}</strong>
                </td>
                <td>
                    ${doc?.type === 'json_data' ?
                        '<span class="badge bg-success"><i class="fas fa-code me-1"></i>JSON数据</span>' :
                        '<span class="badge bg-secondary">' + (doc?.type || 'document') + '</span>'
                    }
                </td>
                <td>
                    <span class="badge bg-info">${doc?.chunk_count || 1}</span>
                </td>
                <td>
                    <small class="text-muted">${doc.content ? doc.content.substring(0, 50) + '...' : '暂无内容'}</small>
                </td>
                <td>
                    <button class="btn btn-outline-warning btn-sm" onclick="rebuildDocument('${doc.name}')" title="重建">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="removeDocument('${doc.name}')" title="移除">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 显示上传模态框
function showUploadModal() {
    $('#uploadForm')[0].reset();
    const modal = new bootstrap.Modal($('#uploadModal')[0]);
    modal.show();
}

// 上传知识库文件
function uploadKnowledgeFile() {
    const fileInput = $('#knowledgeFile')[0];
    const file = fileInput.files[0];

    if (!file) {
        showNotification('请选择文件', 'error');
        return;
    }

    if (!currentKnowledgeBase) {
        showNotification('请先选择项目', 'error');
        return;
    }

    // 禁用上传按钮，防止重复提交
    const uploadButton = $('#uploadButton');
    uploadButton.prop('disabled', true).text('上传中...');

    const formData = new FormData();
    formData.append('file', file);
    // 修复潜在的未定义错误，确保元素存在且有val方法
    const knowledgeNameVal = $('#knowledgeName').length > 0 && typeof $('#knowledgeName').val === 'function' 
        ? $('#knowledgeName').val() 
        : '';
    formData.append('title', knowledgeNameVal.trim() || file.name);
    formData.append('content', ''); // 文件内容将由后端解析
    // 获取JSON处理选项
    const isJsonFile = file.name.toLowerCase().endsWith('.json');
    // 获取分块层级
    let jsonChunkLevel = $('#jsonChunkLevel').val();
    if (jsonChunkLevel === 'auto') {
        jsonChunkLevel = 'auto'; // 自动选择模式
    } else {
        jsonChunkLevel = parseInt(jsonChunkLevel, 10);
    }

    // 获取分块阈值
    let jsonChunkThreshold = $('#jsonChunkThreshold').val();
    if (jsonChunkThreshold === 'custom') {
        jsonChunkThreshold = parseInt($('#jsonCustomThreshold').val(), 10) || 1000;
    } else {
        jsonChunkThreshold = parseInt(jsonChunkThreshold, 10);
    }

    // 同样修复description字段的潜在错误
    const knowledgeDescVal = $('#knowledgeDescription').length > 0 && typeof $('#knowledgeDescription').val === 'function'
        ? $('#knowledgeDescription').val()
        : '';

    const metadata = {
        type: $('#knowledgeType').val(),
        description: knowledgeDescVal.trim()
    };

    // 如果是JSON文件，添加处理选项到元数据
    if (isJsonFile) {
        metadata.json_processing = {
            smart_chunking: true,
            chunk_level: jsonChunkLevel,
            chunk_threshold: jsonChunkThreshold,
            file_extension: 'json'
        };
    }

    formData.append('metadata', JSON.stringify(metadata));

    $.ajax({
        url: `/aicode/api/knowledge_bases/${currentProjectId}/documents`,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function (response) {
            // 恢复按钮状态
            const uploadButton = $('#uploadButton');
            uploadButton.prop('disabled', false).text('上传');

            if (response.success) {
                if (response.status === 'processing') {
                    showNotification('文件上传成功，正在后台处理...', 'info');
                } else {
                    showNotification('文件上传成功', 'success');
                }

                // 先移除焦点，再关闭模态框
                uploadButton.blur();

                // 正确关闭模态框，避免焦点问题
                const modal = bootstrap.Modal.getInstance($('#uploadModal')[0]);
                if (modal) {
                    modal.hide();
                }

                // 重置表单
                $('#uploadForm')[0].reset();

                // 延迟重新加载，确保模态框完全关闭
                setTimeout(() => {
                    loadKnowledgeBase();
                }, 300);
            } else {
                showNotification(response.message || '上传失败', 'error');
            }
        },
        error: function () {
            // 恢复按钮状态
            const uploadButton = $('#uploadButton');
            uploadButton.prop('disabled', false).text('上传');
            showNotification('上传文件失败', 'error');
        },
        complete: function () {
        }
    });
}

// 搜索知识库
function searchKnowledge() {
    const query = $('#searchQuery').val().trim();

    if (!query) {
        showNotification('请输入搜索关键词', 'error');
        return;
    }

    if (!currentKnowledgeBase) {
        showNotification('请先选择项目', 'error');
        return;
    }

    showNotification('正在搜索知识库...','info');
        

    let searchUrl;
    // 使用当前的搜索类型变量
    if (currentSearchType === 'documents') {
        searchUrl = `/aicode/api/knowledge_bases/${currentProjectId}/search`;
    } else {
        searchUrl = `/aicode/api/knowledge_bases/${currentProjectId}/search_code`;
    }
    
    $.ajax({
        url: searchUrl,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            query: query,
            limit: 5
        }),
        timeout: 30000,
        success: function (response) {
            if (response && response.success) {
                displaySearchResults(response.documents || [], query);
            } else {
                showNotification(response.message || '搜索失败', 'error');
            }
        },
        error: function (xhr, status, error) {
            let errorMessage = '搜索失败';
            if (status === 'timeout') {
                errorMessage = '搜索超时，请稍后重试';
            } else if (xhr.status >= 500) {
                errorMessage = '服务器错误，请稍后重试';
            }
            showNotification(errorMessage, 'error');
        },
        complete: function () {
        }
    });
}

// 显示搜索结果
function displaySearchResults(results, query) {
    const container = $('#searchResultsContent');
    container.empty();

    if (results.length === 0) {
        container.html(`
            <div class="text-center text-muted">
                <i class="fas fa-search fa-2x mb-2"></i><br>
                没有找到相关内容
            </div>
        `);
    } else {
        results.forEach(function (result, index) {
            // 修正相似度计算，L2距离越小相似度越高
            const similarity = Math.max(0, (1 - result.score / 10) * 100).toFixed(1);
            
            // 截取内容前200个字符，如果超过则添加省略号
            const fullContent = result.content || '';
            const truncatedContent = fullContent.length > 200 
                ? fullContent.substring(0, 200) + '...' 
                : fullContent;
            
            // 检查是否有语言属性，如果有则添加代码高亮
            const metadata = result.metadata || {};
            const language = metadata.language || '';
            const isCodeBlock = language && currentSearchType === 'code';
            
            // 根据是否有语言属性决定内容显示方式
            let contentHtml = '';
            if (isCodeBlock) {
                contentHtml = `
                    <pre class="search-result-code mb-2" style="cursor: pointer;"><code class="language-${language}">${Utils.escapeHtml(truncatedContent)}</code></pre>
                    ${fullContent.length > 200 ? 
                    `<div class="search-result-full-code" style="display: none;">
                        <pre><code class="language-${language}">${Utils.escapeHtml(fullContent)}</code></pre>
                    </div>
                    <small class="text-muted expand-hint" style="cursor: pointer; display: block;">
                        <i class="fas fa-chevron-down"></i> 点击展开完整内容
                    </small>` : ''}
                `;
            } else {
                contentHtml = `
                    <p class="mb-2 search-result-text" style="cursor: pointer;">
                        ${truncatedContent}
                    </p>
                    ${fullContent.length > 200 ? 
                    `<div class="search-result-full" style="display: none;">
                        ${fullContent}
                    </div>
                    <small class="text-muted expand-hint" style="cursor: pointer; display: block;">
                        <i class="fas fa-chevron-down"></i> 点击展开完整内容
                    </small>` : ''}
                `;
            }
            
            const item = $(`
                <div class="border rounded p-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">${result.title || '文档片段'}</h6>
                        <span class="badge bg-primary">${similarity}% 相似度</span>
                    </div>
                    <div class="search-result-content">
                        ${contentHtml}
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-file-text"></i> 来源: ${result.title || '未知文档'}
                        ${result.metadata && result.metadata.chunk_index !== undefined ?
                    ` | 第${result.metadata.chunk_index + 1}块` : ''}
                        ${isCodeBlock ? ` | 语言: ${language}` : ''}
                    </small>
                </div>
            `);
            
            container.append(item);
            
            // 添加点击事件处理
            if (fullContent.length > 200) {
                const textElement = isCodeBlock ? item.find('.search-result-code') : item.find('.search-result-text');
                const fullElement = isCodeBlock ? item.find('.search-result-full-code') : item.find('.search-result-full');
                const hintElement = item.find('.expand-hint');
                
                item.on('click', function(e) {
                    // 防止点击链接等元素时触发展开
                    if (e.target.tagName === 'A' || $(e.target).closest('a').length > 0) {
                        return;
                    }
                    
                    if (fullElement.is(':visible')) {
                        // 收起完整内容
                        textElement.show();
                        fullElement.hide();
                        hintElement.html('<i class="fas fa-chevron-down"></i> 点击展开完整内容');
                    } else {
                        // 展开完整内容
                        textElement.hide();
                        fullElement.show();
                        hintElement.html('<i class="fas fa-chevron-up"></i> 点击收起完整内容');
                    }
                });
            }
            
            // 如果是代码块，尝试进行语法高亮
            if (isCodeBlock) {
                // 如果页面引入了 Prism.js，则使用它进行语法高亮
                if (typeof Prism !== 'undefined') {
                    const codeElements = item.find('code');
                    codeElements.each(function() {
                        Prism.highlightElement(this);
                    });
                }
            }
        });
    }

    $('#searchResults').show();
}

// 重建文档
function rebuildDocument(documentId) {
    // 调试信息：检查项目ID
    if (!currentProjectId) {
        showNotification('项目ID未设置，无法重建文档', 'error');
        console.error('currentProjectId is null or undefined');
        return;
    }

    console.log(`Rebuilding document: ${documentId} for project: ${currentProjectId}`);

    if (confirm('确定要重建此文档吗？这将从向量库中删除并重新添加该文档。')) {
        showNotification('正在重建文档...', 'info');

        // 对文档ID进行URL编码，防止中文文件名导致的路径问题
        const encodedDocumentId = encodeURIComponent(documentId);

        $.ajax({
            url: `/aicode/api/knowledge_bases/${currentProjectId}/documents/${encodedDocumentId}/rebuild`,
            method: 'POST',
            success: function (response) {
                if (response.success) {
                    showNotification('文档重建成功', 'success');
                    loadKnowledgeBase(); // 重新加载知识库信息
                } else {
                    showNotification(response.message || '重建文档失败', 'error');
                }
            },
            error: function (xhr, status, error) {
                console.error('Rebuild document error:', {xhr, status, error});
                showNotification('重建文档失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : '未知错误'), 'error');
            },
            complete: function () {
            }
        });
    }
}

// 移除文档
function removeDocument(documentId) {
    if (confirm('确定要从知识库中移除此文档吗？')) {
        showNotification('正在移除文档...','info');

        $.ajax({
            url: `/aicode/api/knowledge_bases/${currentProjectId}/documents/${documentId}`,
            method: 'DELETE',
            success: function (response) {
                if (response.success) {
                    showNotification('文档移除成功', 'success');
                    loadKnowledgeBase(); // 重新加载知识库信息
                } else {
                    showNotification(response.message || '移除文档失败', 'error');
                }
            },
            error: function () {
                showNotification('移除文档失败', 'error');
            },
            complete: function () {
            }
        });
    }
}

// 清空知识库
function clearKnowledge() {
    if (confirm('确定要清空整个知识库吗？此操作不可恢复！')) {
        showNotification('正在清空知识库...', 'info');

        $.ajax({
            url: `/aicode/api/knowledge_bases/${currentProjectId}/documents`,
            method: 'DELETE',
            success: function (response) {
                if (response.success) {
                    showNotification('知识库已清空', 'success');
                    loadKnowledgeBase(); // 重新加载知识库信息
                    $('#searchResults').hide();
                } else {
                    showNotification(response.message || '清空知识库失败', 'error');
                }
            },
            error: function () {
                showNotification('清空知识库失败', 'error');
            },
            complete: function () {
            }
        });
    }
}

// 重建代码知识库
function rebuildCodeKnowledgeBase() {
    if (confirm('确定要重建代码知识库吗？这将清空现有代码知识库并重新构建。')) {
        showNotification('正在重建代码知识库...', 'info');

        $.ajax({
            url: `/aicode/api/knowledge_bases/${currentProjectId}/rebuild_code`,
            method: 'POST',
            success: function (response) {
                if (response.success) {
                    showNotification(`代码知识库重建成功！处理了 ${response.processed_files} 个文件，生成了 ${response.total_chunks} 个代码块`, 'success');
                    loadKnowledgeBase(); // 重新加载知识库信息
                } else {
                    showNotification(response.message || '重建代码知识库失败', 'error');
                }
            },
            error: function () {
                showNotification('重建代码知识库失败', 'error');
            },
            complete: function () {
            }
        });
    }
}

// 工具函数
function showNotification(message, type = 'info') {
    Utils.showAlert(message, type)
}