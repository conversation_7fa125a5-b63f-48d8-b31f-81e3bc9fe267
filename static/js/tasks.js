/**
 * 任务管理相关JavaScript
 */

// 任务管理模块
window.TaskManager = {
    currentProjectId: null,
    currentProject: null,
    currentPage: 1,
    pageSize: 10,
    searchTitle: '',
    statusFilter: '',

    /**
     * 初始化任务页面
     */
    initTasksPage: function (projectId) {
        this.currentProjectId = projectId;
        this.loadProject();
        this.loadTasks();
        this.bindEvents();
    },

    /**
     * 创建统一的生成任务模态框
     */
    createGenerateTasksModal: function () {
        // 如果模态框已存在，不重复创建
        if ($('#generateTasksModal').length > 0) {
            return;
        }

        const modalHtml = `
            <!-- 生成任务确认模态框 -->
            <div class="modal fade" id="generateTasksModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header" style="padding: 0.5rem 1rem;">
                            <h5 class="modal-title" id="generateTasksModalTitle">生成任务</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" style="padding: 0.75rem 1rem;">
                            <div id="generateTasksContent">
                                <!-- 内容将通过JavaScript加载 -->
                            </div>
                            <div class="mb-2" id="requirementDisplaySection" style="display: none;">
                                <label class="form-label">需求或问题</label>
                                <textarea class="form-control" id="requirementDisplay" rows="4"
                                    placeholder="请输入需求或问题描述..."></textarea>
                            </div>
                            <div class="mb-2" id="taskCountSection" style="display: none;">
                                <label for="taskCount" class="form-label">任务数量 (可选)</label>
                                <input type="number" class="form-control" id="taskCount" min="1" max="20"
                                    placeholder="留空则自动确定任务数量">
                            </div>
                            <div class="mb-2">
                                <label for="specialInstruction" class="form-label">特殊说明 (可选)</label>
                                <textarea class="form-control" id="specialInstruction" rows="2"
                                    placeholder="添加特殊要求或说明，这些内容将追加到AI生成任务的提示词后面"></textarea>                                
                            </div>
                            <input type="hidden" id="generateTasksProjectId">
                        </div>
                        <div class="modal-footer" style="padding: 0.5rem 1rem;">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmGenerateTasksBtn">确认生成</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);

        // 绑定确认按钮事件
        $('#confirmGenerateTasksBtn').off('click').on('click', function () {
            const currentDocType = $('#generateTasksModal').data('doc-type') || 'requirement';
            TaskManager.confirmGenerateTasks(currentDocType);
        });
    },

    /**
     * 显示生成任务确认模态框
     */
    showGenerateTasks: function (projectId = null, doc_type = 'default') {
        // 确保模态框已创建
        this.createGenerateTasksModal();

        // 根据文档类型设置标题
        const titles = {
            'requirement': '根据需求生成任务',
            'design': '根据设计生成任务',
            'default': '生成任务'
        };
        const title = titles[doc_type] || titles['default'];
        $('#generateTasksModalTitle').text(title);

        // 存储文档类型到模态框数据中
        $('#generateTasksModal').data('doc-type', doc_type);

        // 获取项目信息并显示生成任务确认
        if (projectId == null) {
            projectId = this.currentProjectId;
        }
        if (projectId == null) {
            Utils.showAlert('项目ID无效', 'danger');
        }
        $.ajax({
            url: `/aicode/api/projects/${projectId}`,
            method: 'GET',
            success: function (project) {
                let content = ``;

                let doc = project.requirement;
                let note = '需求';
                if (doc_type == 'design') {
                    doc = project.design;
                    note = '设计';
                }

                // 根据页面类型决定是否显示需求或问题字段
                // 需求管理和设计管理页面不显示需求字段，直接使用代码内容
                // 项目任务页面显示需求字段，允许用户输入
                if (doc_type === 'requirement' || doc_type === 'design') {
                    // 需求/设计管理页面：隐藏需求输入字段，直接使用代码内容
                    $('#requirementDisplaySection').hide();
                    $('#requirementDisplay').val(doc);

                    if (!doc || !doc.trim()) {
                        content = `
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                项目 <strong>${project.name}</strong> 还没有设置${note}，无法生成任务。
                            </div>
                            <p>请先编辑项目并添加${note}内容。</p>
                        `;
                        $('#generateTasksModal .modal-footer .btn-primary').hide();
                        $('#taskCountSection').hide();

                        $('#generateTasksContent').html(content);
                        $('#generateTasksContent').show();
                    } else {
                        $('#generateTasksContent').html("");
                        $('#generateTasksContent').hide();
                        $('#generateTasksModal .modal-footer .btn-primary').show();
                        $('#taskCountSection').show();
                    }
                } else {
                    $('#generateTasksContent').html("");
                    $('#generateTasksContent').hide();

                    // 清空需求输入框
                    $('#requirementDisplay').val("");
                    // 项目任务页面：显示需求输入字段，用户可以手动输入需求
                    $('#requirementDisplaySection').show();
                    $('#generateTasksModal .modal-footer .btn-primary').show();
                    $('#taskCountSection').show();
                }

                // 添加任务生成模式选项（追加或覆盖），但先检查是否已存在
                if ($('#taskModeSection').length === 0) {
                    let modeSection = `
                        <div class="mb-3" id="taskModeSection">
                            <label class="form-label">任务生成模式</label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="taskMode" id="taskModeOverride" value="override" checked>
                                    <label class="form-check-label" for="taskModeOverride">覆盖现有任务</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="taskMode" id="taskModeAppend" value="append">
                                    <label class="form-check-label" for="taskModeAppend">追加到现有任务</label>
                                </div>
                                <div class="form-check form-check-inline ms-3">
                                    <input class="form-check-input" type="checkbox" id="keepSession" checked title="选择任务生成模式，保持会话可以让任务间共享上下文信息">
                                    <label class="form-check-label" for="keepSession">
                                        保持会话
                                    </label>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#taskCountSection').after(modeSection);
                }

                $('#generateTasksProjectId').val(projectId);
                $('#generateTasksModal').modal('show');
            },
            error: function () {
                Utils.showAlert('获取项目信息失败', 'danger');
            }
        });
    },

    /**
     * 标记任务完成状态
     */
    toggleTaskStatus: function (taskId) {
        Utils.confirm('确定要切换任务状态吗？', function () {
            // 保存触发元素的引用
            const triggerElement = document.activeElement;

            API.tasks.toggle(TaskManager.currentProjectId, taskId)
                .done(function (response) {
                    if (response.success) {
                        Utils.showAlert('任务状态切换成功！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 1000);
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    } else {
                        Utils.showAlert('任务状态切换失败: ' + (response.message || response.error), 'danger');
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    }
                })
                .fail(function (xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务状态切换失败: ' + (response.message || response.error || '未知错误'), 'danger');
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                });
        });
    },

    /**
     * 刷新页面（同时更新项目信息和任务列表）
     */
    refreshPage: function () {
        // 同时加载项目信息和任务列表
        this.loadProject();
        this.loadTasks();
    },

    /**
     * 加载项目信息
     */
    loadProject: function () {
        API.projects.get(this.currentProjectId)
            .done(function (project) {
                TaskManager.currentProject = project;
                TaskManager.renderProjectInfo(project);
            })
            .fail(function (xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载项目信息失败: ' + (response.message || '未知错误'), 'danger');
            });
    },

    /**
     * 渲染项目信息
     */
    renderProjectInfo: function (project) {
        $('#project-name').html(Utils.getProviderBadge(project.name));
        $('#project-provider').html(Utils.getProviderBadge(project.provider));
        $('#project-created-at').text(Utils.formatDateTime(project.created_at));

        // 显示当前运行的ClaudeAgent数量
        const agentCount = project.run_state || 0;
        let agentStatusHtml = '';
        if (agentCount > 0) {
            agentStatusHtml = `<span class="badge bg-success">运行中 (${agentCount})</span>`;
        } else {
            agentStatusHtml = '<span class="badge bg-secondary">未运行</span>';
        }
        $('#project-run-state').html(agentStatusHtml);

        if (project.requirement) {
            $('#requirement-section').show();
        } else {
            $('#requirement-section').hide();
        }
    },

    /**
     * 确认生成任务
     */
    confirmGenerateTasks: function (doc_type) {
        const projectId = $('#generateTasksProjectId').val();
        const taskCount = $('#taskCount').val();
        const taskMode = $('input[name="taskMode"]:checked').val(); // 获取任务生成模式
        const keepSession = $('#keepSession').is(':checked'); // 获取保持会话选项

        const submitBtn = $('#generateTasksModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '生成中...');

        const requestData = {};
        requestData.doc_type = doc_type;
        requestData.mode = taskMode; // 添加任务生成模式到请求数据
        requestData.keep_session = keepSession; // 添加保持会话选项到请求数据
        requestData.async_thread = true; // 异步执行
        if (taskCount && taskCount.trim()) {
            requestData.num_tasks = parseInt(taskCount);
        }
        if ($('#specialInstruction').length > 0) {
            const specialInstruction = $('#specialInstruction').val();
            if (specialInstruction && specialInstruction.trim()) {
                requestData.special_instruction = specialInstruction.trim();
            }
        }

        if ($('#requirementDisplay').length > 0) {
            requirement = $('#requirementDisplay').val();
            if (requirement && requirement.trim()) {
                requestData.requirement = requirement.trim();
            }
        }

        API.projects.generateTasks(projectId, requestData)
            .done(function (response) {
                if (response.success) {
                    Utils.showAlert('任务生成请求已提交！', 'success');
                    // 在关闭模态框前保存触发元素的引用
                    const triggerElement = document.activeElement;
                    $('#generateTasksModal').modal('hide');
                    setTimeout(() => {
                        TaskManager.loadTasks(); // 重新加载任务列表
                    }, 1000);
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                } else {
                    Utils.showAlert('任务生成失败: ' + response.message, 'danger');
                }
            })
            .fail(function (xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('任务生成失败: ' + (response.message || '未知错误'), 'danger');
            })
            .always(function () {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 加载任务列表
     */
    loadTasks: function (resetPage = false) {
        // 确保使用正确的项目ID
        const projectId = this.currentProjectId || $('#generateTasksProjectId').val();
        if (!projectId) {
            console.error('无法加载任务列表：项目ID为空');
            return;
        }

        // 重置页码
        if (resetPage) {
            this.currentPage = 1;
        }

        // 构建查询参数
        const params = new URLSearchParams({
            page: this.currentPage,
            page_size: this.pageSize
        });

        if (this.searchTitle) {
            params.append('title', this.searchTitle);
        }

        if (this.statusFilter) {
            params.append('status', this.statusFilter);
        }

        // 发送请求
        $.ajax({
            url: `/aicode/api/projects/${projectId}/tasks?${params.toString()}`,
            method: 'GET',
            success: (response) => {
                TaskManager.renderTasksList(response.tasks || []);
                TaskManager.updateTaskStats(response.tasks || []);
                TaskManager.renderPagination(response.pagination || {});
                TaskManager.updateTaskCountInfo(response.pagination || {});
            },
            error: (xhr) => {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载任务列表失败: ' + (response.error || response.message || '未知错误'), 'danger');
            }
        });
    },

    /**
     * 渲染任务列表
     */
    renderTasksList: function (tasks) {
        const container = $('#tasks-container');

        if (tasks.length === 0) {
            container.html(`
                <div class="text-center py-5">
                    <i class="fas fa-tasks fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">还没有任务</h3>
                    <p class="text-muted mb-4">为项目生成任务来开始工作</p>
                    <button class="btn btn-primary btn-lg" onclick="TaskManager.showGenerateTasks()">
                        <i class="fas fa-cogs"></i> 生成任务
                    </button>
                </div>
            `);
            return;
        }

        let html = `
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 5%">#</th>
                                    <th style="width: 25%">任务标题</th>
                                    <th style="width: 30%">任务描述</th>
                                    <th style="width: 10%">类型</th>
                                    <th style="width: 10%">状态</th>
                                    <th style="width: 10%">执行时间</th>
                                    <th style="width: 10%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        tasks.forEach((task, index) => {
            const executionTime = task.execution_time ?
                (task.execution_time < 60 ?
                    `${task.execution_time.toFixed(2)}秒` :
                    `${(task.execution_time / 60).toFixed(1)}分钟`) :
                '-';

            const subtaskCount = (task.subtasks || []).length;
            const subtaskBadge = subtaskCount > 0 ?
                `<span class="badge bg-info ms-2" title="${subtaskCount}个子任务">${subtaskCount}</span>` : '';

            // 任务类型显示 - 只保留功能、BUG、其他三种类型
            let taskType = '';
            if (task.stype) {
                const typeMap = {
                    'bug': { class: 'danger', text: 'BUG' },
                    'feature': { class: 'success', text: '功能' }
                };
                const typeInfo = typeMap[task.stype] || { class: 'secondary', text: '其他' };
                taskType = `<span class="badge bg-${typeInfo.class}">${typeInfo.text}</span>`;
            } else {
                taskType = '<span class="badge bg-secondary">其他</span>';
            }

            // 定时任务状态显示
            let scheduledBadge = '';
            if (task.has_schedule_config) {
                let statusText, statusIcon, statusClass;
                if (task.is_running) {
                    // 已启用且正在运行
                    statusClass = 'success';
                    statusText = '运行中';
                    statusIcon = 'fa-play-circle';
                } else if (task.scheduled_enabled) {
                    // 已启用但未运行
                    statusClass = 'warning';
                    statusText = '已启用';
                    statusIcon = 'fa-pause-circle';
                } else if (task.status === 'in_progress') {
                    // 任务状态为in_progress，说明正在等待启动
                    statusClass = 'info';
                    statusText = '待启动';
                    statusIcon = 'fa-clock';
                } else {
                    // 配置了但未启用
                    statusClass = 'secondary';
                    statusText = '未启用';
                    statusIcon = 'fa-clock';
                }

                scheduledBadge = `<span class="badge bg-info ms-2" title="定时任务">
                    <i class="fas fa-clock"></i> 定时
                </span>`;

                if (task.next_run_time) {
                    const nextRun = new Date(task.next_run_time).toLocaleString('zh-CN');
                    scheduledBadge += `<br><small class="text-muted"><i class="fas fa-clock"></i> 下次: ${nextRun}</small>`;
                }
            }

            html += `
                <tr>
                    <td>${task.id}</td>
                    <td>
                        <div class="fw-bold ${task.keep_session ? 'keep-session-task' : ''}"
                             style="cursor: pointer;"
                             onclick="TaskManager.openSubtasksModal('${task.id}')" title="${task.keep_session ? '保持会话任务 - 点击查看子任务' : '点击查看子任务'}">
                            ${task.title || task.name || 'N/A'}${subtaskBadge}${scheduledBadge}
                            ${task.keep_session ? '<i class="fas fa-link text-success ms-2" title="保持会话"></i>' : ''}
                        </div>
                        ${task.dependencies && task.dependencies.length > 0 ?
                    `<small class="text-muted"><i class="fas fa-link"></i> 依赖: ${task.dependencies.join(', ')}</small>` :
                    ''}
                    </td>
                    <td>
                        <small class="task-description">${task.description || ''}</small>
                    </td>
                    <td>${taskType}</td>
                    <td>${Utils.getStatusBadge(task.status || 'pending')}</td>
                    <td><span class="text-muted">${executionTime}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary"
                                    onclick="TaskManager.openSubtasksModal('${task.id}')" title="子任务管理">
                                <i class="fas fa-tasks"></i>
                            </button>
                            <button type="button" class="btn btn-outline-primary"
                                    onclick="TaskManager.editTask('${task.id}')" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger"
                                    onclick="TaskManager.deleteTask('${task.id}')" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button type="button" class="btn btn-outline-info"
                                    onclick="TaskManager.viewDetail('${task.id}')" title="详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-outline-success"
                                    onclick="TaskManager.runTask('${task.id}')" title="运行">
                                <i class="fas fa-play"></i>
                            </button>
                            <button type="button" class="btn btn-outline-primary"
                                    onclick="TaskManager.openChatInterface('${task.id}')" title="进入会话">
                                <i class="fas fa-comments"></i>
                            </button>
                            <!-- 手动切换为已完成状态 -->
                            <button type="button" class="btn 'btn-outline-success'"
                                    onclick="TaskManager.toggleTaskStatus('${task.id}')" title="切换状态">
                                <i class="fas fa-toggle-on"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        container.html(html);
    },

    /**
     * 更新任务统计
     */
    updateTaskStats: function (tasks) {
        const total = tasks.length;
        const completed = tasks.filter(t => t.status === 'completed').length;
        const inProgress = tasks.filter(t => t.status === 'in_progress').length;
        const failed = tasks.filter(t => t.status === 'failed').length;

        $('#total-tasks').text(total);
        $('#completed-tasks').text(completed);
        $('#in-progress-tasks').text(inProgress);
        $('#failed-tasks').text(failed);
    },

    /**
     * 更新任务数量信息
     */
    updateTaskCountInfo: function (pagination) {
        const { total_count = 0, current_page = 1, page_size = 10 } = pagination;
        const start = (current_page - 1) * page_size + 1;
        const end = Math.min(current_page * page_size, total_count);

        if (total_count === 0) {
            $('#task-count-info').text('显示 0 个任务');
        } else {
            $('#task-count-info').text(`显示第 ${start}-${end} 个，共 ${total_count} 个任务`);
        }
    },

    /**
     * 渲染分页控件
     */
    renderPagination: function (pagination) {
        const { current_page = 1, total_pages = 1, has_next = false, has_prev = false } = pagination;

        const container = $('#pagination');

        if (total_pages <= 1) {
            container.empty();
            return;
        }

        let html = '';

        // 上一页
        html += `
            <li class="page-item ${has_prev ? '' : 'disabled'}">
                <a class="page-link" href="#" onclick="TaskManager.goToPage(${current_page - 1}); return false;">
                    <i class="fas fa-chevron-left"></i> 上一页
                </a>
            </li>
        `;

        // 页码按钮
        const startPage = Math.max(1, current_page - 2);
        const endPage = Math.min(total_pages, current_page + 2);

        if (startPage > 1) {
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="TaskManager.goToPage(1); return false;">1</a>
                </li>
            `;
            if (startPage > 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="TaskManager.goToPage(${i}); return false;">${i}</a>
                </li>
            `;
        }

        if (endPage < total_pages) {
            if (endPage < total_pages - 1) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="TaskManager.goToPage(${total_pages}); return false;">${total_pages}</a>
                </li>
            `;
        }

        // 下一页
        html += `
            <li class="page-item ${has_next ? '' : 'disabled'}">
                <a class="page-link" href="#" onclick="TaskManager.goToPage(${current_page + 1}); return false;">
                    下一页 <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;

        container.html(html);
    },

    /**
     * 跳转到指定页
     */
    goToPage: function (page) {
        if (page < 1) return;
        this.currentPage = page;
        this.loadTasks();
    },

    /**
     * 绑定事件
     */
    bindEvents: function () {
        // 各种按钮事件
        $(document).on('click', '#refresh-tasks-btn', () => this.refreshPage());
        $(document).on('click', '#add-task-btn', () => this.showAddTaskModal());
        $(document).on('click', '#run-tasks-btn', () => this.runAllTasks());
        $(document).on('click', '#reset-tasks-btn', () => this.resetTasks());
        $(document).on('click', '#stop-execution-btn', () => this.stopExecution());
        $(document).on('click', '#generate-tasks-btn', () => this.showGenerateTasks(this.currentProjectId));
        $(document).on('click', '#delete-all-tasks-btn', () => this.deleteAllTasks());
        // 添加任务确认按钮事件
        $(document).on('click', '#confirmAddTask', () => this.submitAddTask());

        // 搜索和分页事件
        $(document).on('click', '#search-btn', () => this.performSearch());
        $(document).on('click', '#clear-search', () => this.clearSearch());
        $(document).on('change', '#page-size', () => this.changePageSize());
        $(document).on('keypress', '#search-title', (e) => {
            if (e.which === 13) { // 回车键
                this.performSearch();
            }
        });
        $(document).on('change', '#filter-status', () => this.performSearch());
    },

    /**
     * 执行搜索
     */
    performSearch: function () {
        this.searchTitle = $('#search-title').val().trim();
        this.statusFilter = $('#filter-status').val();
        this.loadTasks(true); // 重置页码
    },

    /**
     * 清空搜索
     */
    clearSearch: function () {
        $('#search-title').val('');
        $('#filter-status').val('');
        this.searchTitle = '';
        this.statusFilter = '';
        this.loadTasks(true); // 重置页码
    },

    /**
     * 改变每页显示数量
     */
    changePageSize: function () {
        const newPageSize = parseInt($('#page-size').val());
        if (newPageSize && newPageSize > 0) {
            this.pageSize = newPageSize;
            this.loadTasks(true); // 重置页码
        }
    },

    /**
     * 显示添加任务模态框
     */
    showAddTaskModal: function () {
        $('#addTaskForm')[0].reset();
        $('#addTaskModal').modal('show');
    },

    /**
     * 提交添加任务
     */
    submitAddTask: function () {
        const formData = {
            title: $('#taskTitle').val().trim(),
            description: $('#taskDescription').val().trim(),
            stype: $('#taskType').val() || null,
            testStrategy: $('#taskTestStrategy').val().trim(),
            dependencies: $('#taskDependencies').val().trim().split(',').map(id => id.trim()).filter(id => id),
            keep_session: $('#taskKeepSession').is(':checked'),
            execute_immediately: $('#executeImmediately').is(':checked'),
            schedule: $('#taskSchedule').val().trim() || null,
            async_thread: true  // 异步执行
        };

        if (!formData.title) {
            Utils.showAlert('请输入任务标题', 'warning');
            return;
        }

        const submitBtn = $('#addTaskModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '添加中...');

        API.tasks.create(this.currentProjectId, formData)
            .done(function (response) {
                if (response.success) {
                    let message = '任务添加成功！';
                    if (formData.execute_immediately && response.execution_result) {
                        if (response.execution_result.success) {
                            message += ' 任务运行请求已提交。';
                        } else {
                            message += ` 但执行失败: ${response.execution_result.message}`;
                        }
                    }
                    Utils.showAlert(message, 'success');
                    // 在关闭模态框前保存触发元素的引用
                    const triggerElement = document.activeElement;
                    $('#addTaskModal').modal('hide');
                    setTimeout(() => TaskManager.loadTasks(), 1000);
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                } else {
                    Utils.showAlert('任务添加失败: ' + response.message, 'danger');
                }
            })
            .fail(function (xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('任务添加失败: ' + (response.error || response.message || '未知错误'), 'danger');
            })
            .always(function () {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 编辑任务
     */
    editTask: function (taskId) {
        API.tasks.get(this.currentProjectId, taskId)
            .done(function (response) {
                if (response.success) {
                    TaskManager.showEditTaskModal(response.task);
                } else {
                    Utils.showAlert('获取任务信息失败: ' + response.error, 'danger');
                }
            })
            .fail(function () {
                Utils.showAlert('获取任务信息失败', 'danger');
            });
    },

    /**
     * 显示编辑任务模态框
     */
    showEditTaskModal: function (task) {
        const editModalHtml = `
            <div class="modal fade" id="editTaskModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑任务</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editTaskForm">
                                <div class="mb-3">
                                    <label for="editTaskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editTaskTitle" value="${task.title || task.name || ''}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskDescription" class="form-label">任务描述</label>
                                    <textarea class="form-control" id="editTaskDescription" rows="4">${task.description || ''}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskType" class="form-label">任务类型</label>
                                    <select class="form-select" id="editTaskType">
                                        <option value="other">其他</option>
                                        <option value="feature" ${task.stype === 'feature' ? 'selected' : ''}>功能</option>
                                        <option value="bug" ${task.stype === 'bug' ? 'selected' : ''}>BUG</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskTestStrategy" class="form-label">测试策略</label>
                                    <textarea class="form-control" id="editTaskTestStrategy" placeholder="如何验证任务完成" >${task.testStrategy || ''}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskDependencies" class="form-label">依赖任务 (可选)</label>
                                    <input type="text" class="form-control" id="editTaskDependencies"
                                           value="${task.dependencies ? task.dependencies.join(', ') : ''}"
                                           placeholder="输入依赖的任务ID，多个用逗号分隔">
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="editTaskKeepSession"
                                           ${task.keep_session ? 'checked' : ''}>
                                    <label class="form-check-label" for="editTaskKeepSession">
                                        保持会话
                                        <span class="text-muted">(使用前任务的会话状态)</span>
                                    </label>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskSchedule" class="form-label">定时任务 (Cron表达式)</label>
                                    <input type="text" class="form-control" id="editTaskSchedule"
                                           value="${task.schedule || ''}"
                                           placeholder="例如: 0 */6 * * * (每6小时执行一次)">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle"></i>
                                        使用Linux cron格式: 分 时 日 月 周
                                        <a href="#" onclick="TaskManager.showCronHelp(); return false;">查看帮助</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="TaskManager.submitEditTask('${task.id}')">保存修改</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        $('#editTaskModal').remove();

        // 添加新的模态框并显示
        $('body').append(editModalHtml);
        $('#editTaskModal').modal('show');

        // 模态框关闭后移除，并处理焦点问题
        $('#editTaskModal').on('hidden.bs.modal', function () {
            // 保存当前焦点元素
            const activeElement = document.activeElement;

            // 移除模态框
            $(this).remove();

            // 如果焦点在被隐藏的元素上，手动将焦点移到body上
            if (activeElement && activeElement !== document.body && activeElement !== document.documentElement) {
                // 检查元素是否在DOM中仍然存在
                if (!document.contains(activeElement)) {
                    // 将焦点移到body上以避免aria-hidden警告
                    document.body.focus();
                }
            }
        });
    },

    /**
     * 提交编辑任务
     */
    submitEditTask: function (taskId) {
        const formData = {
            title: $('#editTaskTitle').val().trim(),
            description: $('#editTaskDescription').val().trim(),
            stype: $('#editTaskType').val() || null,
            testStrategy: $('#editTaskTestStrategy').val().trim(),
            dependencies: $('#editTaskDependencies').val().trim().split(',').map(id => id.trim()).filter(id => id),
            keep_session: $('#editTaskKeepSession').is(':checked'),
            schedule: $('#editTaskSchedule').val().trim() || null
        };

        if (!formData.title) {
            Utils.showAlert('请输入任务标题', 'warning');
            return;
        }

        const submitBtn = $('#editTaskModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '保存中...');

        API.tasks.update(this.currentProjectId, taskId, formData)
            .done(function (response) {
                if (response.success) {
                    Utils.showAlert('任务更新成功！', 'success');
                    // 在关闭模态框前保存触发元素的引用
                    const triggerElement = document.activeElement;
                    $('#editTaskModal').modal('hide');
                    setTimeout(() => TaskManager.loadTasks(), 1000);
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                } else {
                    Utils.showAlert('任务更新失败: ' + response.message, 'danger');
                }
            })
            .fail(function (xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('任务更新失败: ' + (response.error || response.message || '未知错误'), 'danger');
            })
            .always(function () {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 删除任务
     */
    deleteTask: function (taskId) {
        Utils.confirm('确定要删除这个任务吗？此操作不可撤销。', function () {
            // 保存触发元素的引用
            const triggerElement = document.activeElement;

            API.tasks.delete(TaskManager.currentProjectId, taskId)
                .done(function (response) {
                    if (response.success) {
                        Utils.showAlert('任务删除成功！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 1000);
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    } else {
                        Utils.showAlert('任务删除失败: ' + (response.error || response.message || '未知错误'), 'danger');
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    }
                })
                .fail(function (xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务删除失败: ' + (response.error || response.message || '未知错误'), 'danger');
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                });
        });
    },

    /**
     * 运行单个任务
     */
    runTask: function (taskId) {
        Utils.confirm('确定要运行这个任务吗？', function () {
            // 保存触发元素的引用
            const triggerElement = document.activeElement;

            API.tasks.run(TaskManager.currentProjectId, taskId, { async_thread: true })
                .done(function (response) {
                    if (response.success) {
                        Utils.showAlert('任务运行请求已提交！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 3000);
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    } else {
                        Utils.showAlert('任务启动失败: ' + response.message, 'danger');
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    }
                })
                .fail(function (xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务启动失败: ' + (response.message || '未知错误'), 'danger');
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                });
        });
    },

    /**
     * 继续任务
     */
    continueTask: function (taskId) {
        Utils.confirm('确定要继续执行这个任务吗？', function () {
            // 保存触发元素的引用
            const triggerElement = document.activeElement;

            API.tasks.continue(TaskManager.currentProjectId, taskId)
                .done(function (response) {
                    if (response.success) {
                        Utils.showAlert('任务继续执行！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 3000);
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    } else {
                        Utils.showAlert('任务继续执行失败: ' + response.message, 'danger');
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    }
                })
                .fail(function (xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务继续执行失败: ' + (response.message || '未知错误'), 'danger');
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                });
        });
    },

    /**
     * 运行所有任务
     */
    runAllTasks: function () {
        Utils.confirm('确定要运行所有任务吗？', function () {
            API.projects.runTasks(TaskManager.currentProjectId, { async_thread: true })
                .done(function (response) {
                    if (response.success) {
                        Utils.showAlert('任务运行请求已提交！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 3000);
                    } else {
                        Utils.showAlert('任务运行失败: ' + response.message, 'danger');
                    }
                })
                .fail(function (xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务运行失败: ' + (response.message || '未知错误'), 'danger');
                });
        });
    },

    /**
     * 重置所有任务
     */
    resetTasks: function () {
        Utils.confirm('确定要重置所有任务吗？这将清除所有任务的执行结果。', function () {
            // 保存触发元素的引用
            const triggerElement = document.activeElement;

            API.projects.resetTasks(TaskManager.currentProjectId)
                .done(function (response) {
                    if (response.success) {
                        Utils.showAlert('任务重置成功！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 1000);
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    } else {
                        Utils.showAlert('任务重置失败: ' + response.message, 'danger');
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    }
                })
                .fail(function (xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务重置失败: ' + (response.message || '未知错误'), 'danger');
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                });
        });
    },

    /**
     * 停止执行
     */
    stopExecution: function () {
        Utils.confirm('确定要停止所有任务的执行吗？', function () {
            // 保存触发元素的引用
            const triggerElement = document.activeElement;

            API.projects.stopExecution(TaskManager.currentProjectId)
                .done(function (response) {
                    if (response.success) {
                        Utils.showAlert('已发送停止执行命令！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 3000);
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    } else {
                        Utils.showAlert('停止执行失败: ' + response.message, 'danger');
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    }
                })
                .fail(function (xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('停止执行失败: ' + (response.message || '未知错误'), 'danger');
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                });
        });
    },

    /**
     * 删除所有任务
     */
    deleteAllTasks: function () {
        Utils.confirm('确定要删除所有任务吗？此操作不可撤销。', function () {
            // 保存触发元素的引用
            const triggerElement = document.activeElement;

            API.tasks.deleteAllTasks(TaskManager.currentProjectId)
                .done(function (response) {
                    if (response.success) {
                        Utils.showAlert('所有任务删除成功！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 1000);
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    } else {
                        Utils.showAlert('删除所有任务失败: ' + response.message, 'danger');
                        // 确保焦点正确设置
                        setTimeout(() => {
                            if (triggerElement && document.contains(triggerElement)) {
                                triggerElement.focus();
                            }
                        }, 0);
                    }
                })
                .fail(function (xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('删除所有任务失败: ' + (response.message || '未知错误'), 'danger');
                    // 确保焦点正确设置
                    setTimeout(() => {
                        if (triggerElement && document.contains(triggerElement)) {
                            triggerElement.focus();
                        }
                    }, 0);
                });
        });
    },

    /**
     * 查看任务详情
     */
    viewDetail: function (taskId) {
        API.tasks.get(this.currentProjectId, taskId)
            .done(function (response) {
                if (response.success) {
                    TaskManager.showTaskDetailModal(response.task);
                } else {
                    Utils.showAlert('获取任务信息失败: ' + response.error, 'danger');
                }
            })
            .fail(function () {
                Utils.showAlert('获取任务信息失败', 'danger');
            });
    },

    /**
     * 显示任务详情模态框
     */
    showTaskDetailModal: function (task) {
        // 定时任务信息
        let scheduleInfo = '';
        if (task.has_schedule_config) {
            // 显示定时任务属性解释
        }

        const detailHtml = `
            <div class="modal fade" id="taskDetailModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header d-flex justify-content-between align-items-center">
                            <h5 class="modal-title mb-0">任务详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <dl class="row mb-0">
                                <dt class="col-sm-3 text-start fw-bold">任务ID:</dt>
                                <dd class="col-sm-9 mb-2">${task.id}</dd>

                                <dt class="col-sm-3 text-start fw-bold">标题:</dt>
                                <dd class="col-sm-9 mb-2">${task.title || task.name || 'N/A'}</dd>

                                <dt class="col-sm-3 text-start fw-bold">描述:</dt>
                                <dd class="col-sm-9 mb-2">${task.description || '无'}</dd>

                                <dt class="col-sm-3 text-start fw-bold">测试策略:</dt>
                                <dd class="col-sm-9 mb-2">${task.testStrategy || '无'}</dd>

                                <dt class="col-sm-3 text-start fw-bold">状态:</dt>
                                <dd class="col-sm-9 mb-2">${Utils.getStatusBadge(task.status || '未知')}</dd>

                                <dt class="col-sm-3 text-start fw-bold">依赖:</dt>
                                <dd class="col-sm-9 mb-2">${task.dependencies ? task.dependencies.join(', ') : '无'}</dd>

                                ${scheduleInfo}

                                <dt class="col-sm-3 text-start fw-bold">创建时间:</dt>
                                <dd class="col-sm-9 mb-2">${Utils.formatDateTime(task.created_at) || '未知'}</dd>

                                <dt class="col-sm-3 text-start fw-bold">更新时间:</dt>
                                <dd class="col-sm-9 mb-2">${Utils.formatDateTime(task.updated_at) || '未知'}</dd>
                            </dl>
                            ${task.result ? '<h6>执行结果:</h6><pre class="bg-light p-3 rounded">' + Utils.escapeHtml(task.result) + '</pre>' : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        $('#taskDetailModal').remove();

        // 添加新的模态框并显示
        $('body').append(detailHtml);
        $('#taskDetailModal').modal('show');

        // 模态框关闭后移除
        $('#taskDetailModal').on('hidden.bs.modal', function () {
            $(this).remove();
        });
    },

    /**
     * 打开聊天界面
     */
    openChatInterface: function (taskId) {
        Router.navigate(`/aicode/task_chat.html?project_id=${this.currentProjectId}&task_id=${taskId}`);
    },

    /**
     * 查看LLM日志
     */
    viewLLMLogs: function (taskId) {
        Router.navigate(`/aicode/task_llm_logs.html?project_id=${this.currentProjectId}&task_id=${taskId}`);
    },

    /**
     * 显示继续任务模态框
     */
    continueTask: function (taskId) {
        // 设置当前任务ID
        $('#continueTaskId').val(taskId);
        // 清空之前的内容
        $('#continueTaskRequest').val('');
        // 显示模态框
        $('#continueTaskModal').modal('show');
    },

    // ==================== 子任务管理方法 ====================

    /**
     * 打开子任务管理模态框
     */
    openSubtasksModal: function (taskId) {
        this.currentTaskId = taskId;
        $('#current-task-id').val(taskId);

        // 获取任务信息
        API.tasks.list(this.currentProjectId)
            .done((response) => {
                const task = response.tasks.find(t => t.id === parseInt(taskId));
                if (task) {
                    $('#subtask-task-title').text(task.title || task.name || 'N/A');
                    this.loadSubtasks(taskId);
                    $('#subtasksModal').modal('show');
                }
            })
            .fail(() => {
                Utils.showAlert('获取任务信息失败', 'danger');
            });
    },

    /**
     * 加载子任务列表
     */
    loadSubtasks: function (taskId) {
        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${taskId}/subtasks`,
            method: 'GET',
            success: (response) => {
                if (response.success) {
                    this.renderSubtasks(response.subtasks || []);
                } else {
                    Utils.showAlert('加载子任务失败', 'danger');
                }
            },
            error: (xhr) => {
                Utils.showAlert('加载子任务失败: ' + (xhr.responseJSON?.error || xhr.responseJSON?.message || '未知错误'), 'danger');
            }
        });
    },

    /**
     * 渲染子任务列表
     */
    renderSubtasks: function (subtasks) {
        const container = $('#subtasks-container');
        $('#subtask-count').text(`${subtasks.length}个子任务`);

        if (subtasks.length === 0) {
            container.html(`
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-tasks fa-3x mb-3"></i>
                    <p>暂无子任务，点击"添加子任务"或"智能分解"来创建</p>
                </div>
            `);
            return;
        }

        let html = '';
        subtasks.forEach((subtask, index) => {
            const statusBadge = Utils.getStatusBadge(subtask.status || 'pending');
            const checklistHtml = (subtask.checklist || []).length > 0 ?
                `<ul class="list-unstyled mb-0 small">
                    ${subtask.checklist.map(item => `<li><i class="fas fa-check-circle text-success"></i> ${item}</li>`).join('')}
                </ul>` : '<span class="text-muted small">无检查项</span>';

            html += `
                <div class="card mb-2">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-2">
                                    <span class="badge bg-secondary me-2">${index + 1}</span>
                                    ${subtask.name}
                                    ${statusBadge}
                                </h6>
                                ${subtask.description ? `<p class="text-muted small mb-2">${subtask.description}</p>` : ''}
                                <div class="mt-2">
                                    <strong class="small">检查项:</strong>
                                    ${checklistHtml}
                                </div>
                            </div>
                            <div class="btn-group btn-group-sm ms-3" role="group">
                                <button type="button" class="btn btn-outline-primary"
                                        onclick="TaskManager.editSubtask('${subtask.id}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="TaskManager.deleteSubtask('${subtask.id}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button type="button" class="btn btn-outline-${subtask.status === 'completed' ? 'secondary' : 'success'}"
                                        onclick="TaskManager.toggleSubtaskStatus('${subtask.id}', '${subtask.status}')"
                                        title="${subtask.status === 'completed' ? '标记为未完成' : '标记为完成'}">
                                    <i class="fas fa-${subtask.status === 'completed' ? 'times' : 'check'}"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.html(html);
    },

    /**
     * 显示添加子任务表单
     */
    showAddSubtaskForm: function () {
        $('#subtaskForm')[0].reset();
        $('#edit-subtask-id').val('');
        $('#add-subtask-form').slideDown();
    },

    /**
     * 取消子任务表单
     */
    cancelSubtaskForm: function () {
        $('#add-subtask-form').slideUp();
        $('#subtaskForm')[0].reset();
        $('#edit-subtask-id').val('');
    },

    /**
     * 保存子任务
     */
    saveSubtask: function () {
        const taskId = $('#current-task-id').val();
        const subtaskId = $('#edit-subtask-id').val();
        const name = $('#subtask-name').val().trim();
        const description = $('#subtask-description').val().trim();
        const checklistText = $('#subtask-checklist').val().trim();
        const checklist = checklistText ? checklistText.split('\n').filter(item => item.trim()) : [];

        if (!name) {
            Utils.showAlert('请输入子任务名称', 'warning');
            return;
        }

        const data = { name, description, checklist };
        const url = subtaskId ?
            `/aicode/api/projects/${this.currentProjectId}/tasks/${taskId}/subtasks/${subtaskId}` :
            `/aicode/api/projects/${this.currentProjectId}/tasks/${taskId}/subtasks`;
        const method = subtaskId ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            method: method,
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: (response) => {
                if (response.success) {
                    Utils.showAlert(subtaskId ? '子任务更新成功' : '子任务创建成功', 'success');
                    this.cancelSubtaskForm();
                    this.loadSubtasks(taskId);
                    this.loadTasks(); // 刷新主任务列表以更新子任务数量
                } else {
                    Utils.showAlert(response.error || response.message || '操作失败', 'danger');
                }
            },
            error: (xhr) => {
                Utils.showAlert((xhr.responseJSON?.error || xhr.responseJSON?.message || '操作失败'), 'danger');
            }
        });
    },

    /**
     * 编辑子任务
     */
    editSubtask: function (subtaskId) {
        const taskId = $('#current-task-id').val();

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${taskId}/subtasks/${subtaskId}`,
            method: 'GET',
            success: (response) => {
                if (response.success) {
                    const subtask = response.subtask;
                    $('#edit-subtask-id').val(subtaskId);
                    $('#subtask-name').val(subtask.name);
                    $('#subtask-description').val(subtask.description || '');
                    $('#subtask-checklist').val((subtask.checklist || []).join('\n'));
                    $('#add-subtask-form').slideDown();
                }
            },
            error: (xhr) => {
                Utils.showAlert('获取子任务信息失败', 'danger');
            }
        });
    },

    /**
     * 删除子任务
     */
    deleteSubtask: function (subtaskId) {
        if (!confirm('确定要删除这个子任务吗？')) {
            return;
        }

        const taskId = $('#current-task-id').val();

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${taskId}/subtasks/${subtaskId}`,
            method: 'DELETE',
            success: (response) => {
                if (response.success) {
                    Utils.showAlert('子任务删除成功', 'success');
                    this.loadSubtasks(taskId);
                    this.loadTasks(); // 刷新主任务列表
                } else {
                    Utils.showAlert(response.error || response.message || '删除失败', 'danger');
                }
            },
            error: (xhr) => {
                Utils.showAlert((xhr.responseJSON?.error || '删除失败'), 'danger');
            }
        });
    },

    /**
     * 切换子任务状态
     */
    toggleSubtaskStatus: function (subtaskId, currentStatus) {
        const taskId = $('#current-task-id').val();
        const newStatus = currentStatus === 'completed' ? 'pending' : 'completed';

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${taskId}/subtasks/${subtaskId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({ status: newStatus }),
            success: (response) => {
                if (response.success) {
                    this.loadSubtasks(taskId);
                }
            },
            error: (xhr) => {
                Utils.showAlert('更新状态失败', 'danger');
            }
        });
    },

    /**
     * 智能分解任务
     */
    smartSplitTask: function () {
        const taskId = $('#current-task-id').val();

        if (!confirm('确定要使用AI智能分解这个任务吗？这可能需要一些时间。')) {
            return;
        }

        // 显示进度提示
        let seconds = 0;
        const progressHtml = `
            <div id="smart-split-progress" class="alert alert-info d-flex align-items-center" role="alert">
                <i class="fas fa-spinner fa-spin me-2"></i>
                <span id="progress-text">AI智能分解中，<span id="progress-seconds">0</span>秒...</span>
            </div>
        `;

        // 在子任务列表容器前插入进度提示
        $('#subtasks-container').before(progressHtml);

        // 启动计时器
        const timerId = setInterval(() => {
            seconds++;
            $('#progress-seconds').text(seconds);
        }, 1000);

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${taskId}/subtasks/smart_split`,
            method: 'POST',
            success: (response) => {
                if (response.success) {
                    Utils.showAlert(response.message || '智能分解成功', 'success');
                    this.loadSubtasks(taskId);
                    this.loadTasks(); // 刷新主任务列表
                } else {
                    Utils.showAlert(response.message || '智能分解失败', 'danger');
                }
            },
            error: (xhr) => {
                Utils.showAlert((xhr.responseJSON?.error || '智能分解失败'), 'danger');
            },
            complete: () => {
                // 清除计时器和进度提示
                clearInterval(timerId);
                $('#smart-split-progress').remove();
            }
        });
    },

    /**
     * 删除所有子任务
     */
    deleteAllSubtasks: function () {
        const taskId = $('#current-task-id').val();

        // 获取当前子任务数量
        const subtaskCountText = $('#subtask-count').text();
        const match = subtaskCountText.match(/(\d+)/);
        const count = match ? parseInt(match[1]) : 0;

        if (count === 0) {
            Utils.showAlert('当前没有子任务', 'warning');
            return;
        }

        if (!confirm(`确定要删除所有 ${count} 个子任务吗？此操作不可撤销！`)) {
            return;
        }

        // 显示进度提示
        Utils.showAlert('正在删除所有子任务...', 'info');

        $.ajax({
            url: `/aicode/api/projects/${this.currentProjectId}/tasks/${taskId}/subtasks/delete_all`,
            method: 'DELETE',
            success: (response) => {
                if (response.success) {
                    Utils.showAlert(response.message || '已删除所有子任务', 'success');
                    this.loadSubtasks(taskId);
                    this.loadTasks(); // 刷新主任务列表
                } else {
                    Utils.showAlert(response.message || '删除失败', 'danger');
                }
            },
            error: (xhr) => {
                Utils.showAlert((xhr.responseJSON?.error || '删除失败'), 'danger');
            }
        });
    },

    /**
     * 从子任务模态框运行任务
     */
    runTaskFromSubtaskModal: function () {
        const taskId = $('#current-task-id').val();

        if (!taskId) {
            Utils.showAlert('无效的任务ID', 'danger');
            return;
        }

        // 关闭子任务模态框
        $('#subtasksModal').modal('hide');

        // 延迟执行，等待模态框关闭动画完成
        setTimeout(() => {
            // 调用运行任务方法
            this.runTask(taskId);
        }, 300);
    },

    // ==================== 定时任务管理方法 ====================

    /**
     * 显示Cron帮助
     */
    showCronHelp: function () {
        $('#cronHelpModal').modal('show');
    }
};