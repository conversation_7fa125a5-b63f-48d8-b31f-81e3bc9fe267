<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务列表 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Modern Theme -->
    <link href="/aicode/static/css/modern-theme.css" rel="stylesheet">

    <style>
        html, body {
            overflow-x: hidden;
            max-width: 100%;
        }
        
        .requirement-container {
            max-height: 150px;
            overflow: hidden;
            position: relative;
        }

        .requirement-container.expanded {
            max-height: none;
        }

        .requirement-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(transparent, var(--bg-secondary));
            display: flex;
            justify-content: center;
            align-items: end;
            padding-bottom: 8px;
        }

        #tasks-container .card-body {
            padding: 0.75rem 0;
        }

        #tasks-container table tr {
            height: auto;
        }

        #tasks-container table td {
            padding: 0.5rem 0.75rem;
        }

        /* 任务描述显示三行高度 */
        .task-description {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            line-clamp: 3;
            -webkit-line-clamp: 3;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 保持会话任务的特殊样式 */
        .keep-session-task {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
            padding: 10px 12px !important;
            border-radius: 6px !important;
            border-left: 4px solid #28a745 !important;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.1) !important;
            transition: all 0.2s ease-in-out !important;
        }

        .keep-session-task:hover {
            background: linear-gradient(135deg, #c3e6cb 0%, #b8dabc 100%) !important;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.15) !important;
            transform: translateY(-1px) !important;
        }
        
        /* 确保容器不会产生水平滚动 */
        .container-fluid {
            max-width: 100%;
            overflow-x: hidden;
            padding-left: 10px;
            padding-right: 10px;
        }
        
        /* 控制按钮区域宽度 */
        .btn-toolbar {
            max-width: 100%;
            overflow-x: hidden;
        }
        
        /* 控制搜索区域宽度 */
        .row {
            margin-left: 0;
            margin-right: 0;
        }
        
        /* 确保表格容器不会溢出 */
        #tasks-container {
            overflow-x: auto;
            max-width: 100%;
        }
    </style>
</head>

<body>
    <!-- 主内容区域 - 只保留任务列表 -->
    <div class="container-fluid p-1">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-1 mb-2 border-bottom">
            <div class="btn-toolbar mb-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn" id="run-tasks-btn">
                        <i class="fas fa-play"></i> 运行任务
                    </button>
                    <button type="button" class="btn" id="reset-tasks-btn">
                        <i class="fas fa-undo"></i> 重置任务
                    </button>
                    <button type="button" class="btn" id="stop-execution-btn">
                        <i class="fas fa-stop"></i> 停止运行
                    </button>
                    <button type="button" class="btn" id="generate-tasks-btn">
                        <i class="fas fa-redo"></i> 生成任务
                    </button>
                    <button type="button" class="btn" id="refresh-tasks-btn">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button type="button" class="btn" id="add-task-btn">
                        <i class="fas fa-plus"></i> 添加任务
                    </button>   
                    <button type="button" class="btn" id="delete-all-tasks-btn">
                        <i class="fas fa-trash-alt"></i> 删除所有任务
                    </button>                 
                </div>
            </div>
            <div class="mb-0 ms-auto d-flex align-items-center">
                <strong class="me-1">Agent状态:</strong> <span id="project-run-state">-</span>
                <span class="ms-3 d-flex align-items-center"><strong class="me-1">LLM:</strong> <span id="project-provider">-</span></span>
            </div>
        </div>

        <!-- 搜索、过滤和分页信息区域 -->
        <div class="row mb-3 align-items-center">
            <div class="col-md-5">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="search-title" placeholder="搜索任务标题...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="filter-status">
                    <option value="">所有状态</option>
                    <option value="pending">待处理</option>
                    <option value="in_progress">进行中</option>
                    <option value="completed">已完成</option>
                    <option value="failed">失败</option>
                    <option value="disabled">禁用</option>
                </select>
            </div>
            <div class="col-md-4 d-flex justify-content-end align-items-center">
                <div class="text-muted">
                    <span id="task-count-info">显示 0 个任务</span>
                </div>
                <div class="text-muted ms-3">
                    每页显示
                    <select class="form-select form-select-sm d-inline-block w-auto" id="page-size">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    个
                </div>
            </div>
        </div>

        <!-- 任务列表容器 -->
        <div id="tasks-container">
            <div class="text-center py-5">
                <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
                <div>加载中...</div>
            </div>
        </div>

        <!-- 分页控件 -->
        <div class="d-flex justify-content-center mt-3" id="pagination-container">
            <nav aria-label="任务列表分页">
                <ul class="pagination mb-0" id="pagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 添加任务模态框 -->
    <div class="modal fade" id="addTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm">
                        <div class="mb-3">
                            <label for="taskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="taskTitle" required placeholder="请输入任务标题">
                        </div>
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="taskDescription" rows="4"
                                placeholder="请输入任务描述"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="taskType" class="form-label">任务类型</label>
                            <select class="form-select" id="taskType">
                                <option value="other">其他</option>
                                <option value="feature">功能</option>
                                <option value="bug">BUG</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="taskTestStrategy" class="form-label">测试策略</label>
                            <textarea class="form-control" id="taskTestStrategy" rows="2"
                                placeholder="如何验证任务完成"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="taskDependencies" class="form-label">依赖任务 (可选)</label>
                            <input type="text" class="form-control" id="taskDependencies"
                                placeholder="输入依赖的任务ID，多个用逗号分隔">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="taskKeepSession">
                            <label class="form-check-label" for="taskKeepSession">
                                保持会话
                                <span class="text-muted">(使用前任务的会话状态)</span>
                            </label>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="executeImmediately">
                            <label class="form-check-label" for="executeImmediately">
                                立即执行
                                <span class="text-muted">(添加后立即开始执行任务)</span>
                            </label>
                        </div>
                        <div class="mb-3">
                            <label for="taskSchedule" class="form-label">定时任务 (Cron表达式)</label>
                            <input type="text" class="form-control" id="taskSchedule"
                                placeholder="例如: 0 */6 * * * (每6小时执行一次)">
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                使用Linux cron格式: 分 时 日 月 周
                                <a href="#" onclick="TaskManager.showCronHelp(); return false;">查看帮助</a>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmAddTask">添加任务</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑任务模态框 -->
    <div class="modal fade" id="editTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editTaskForm">
                        <input type="hidden" id="editTaskId">
                        <div class="mb-3">
                            <label for="editTaskTitle" class="form-label">任务标题 <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editTaskTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="editTaskDescription" rows="4"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskTestStrategy" class="form-label">测试策略</label>
                            <textarea class="form-control" id="editTaskTestStrategy" placeholder="如何验证任务完成"
                                rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskType" class="form-label">任务类型</label>
                            <select class="form-select" id="editTaskType">
                                <option value="other">其他</option>
                                <option value="feature">功能</option>
                                <option value="bug">BUG</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskTestStrategy" class="form-label">测试策略</label>
                            <textarea class="form-control" id="editTaskTestStrategy" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskDependencies" class="form-label">依赖任务 (可选)</label>
                            <input type="text" class="form-control" id="editTaskDependencies"
                                placeholder="输入依赖的任务ID，多个用逗号分隔">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="editTaskKeepSession">
                            <label class="form-check-label" for="editTaskKeepSession">
                                保持会话
                                <span class="text-muted">(使用前任务的会话状态)</span>
                            </label>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskSchedule" class="form-label">定时任务 (Cron表达式)</label>
                            <input type="text" class="form-control" id="editTaskSchedule"
                                placeholder="例如: 0 */6 * * * (每6小时执行一次)">
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                使用Linux cron格式: 分 时 日 月 周
                                <a href="#" onclick="TaskManager.showCronHelp(); return false;">查看帮助</a>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmEditTask">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    
    <!-- 子任务管理模态框 -->
    <div class="modal fade" id="subtasksModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-tasks"></i> 子任务管理: <span id="subtask-task-title"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <button type="button" class="btn btn-success btn-sm"
                                onclick="TaskManager.runTaskFromSubtaskModal()">
                                <i class="fas fa-play"></i> 运行任务
                            </button>
                            <button type="button" class="btn btn-primary btn-sm"
                                onclick="TaskManager.showAddSubtaskForm()">
                                <i class="fas fa-plus"></i> 添加子任务
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="TaskManager.smartSplitTask()">
                                <i class="fas fa-magic"></i> 智能分解
                            </button>
                            <button type="button" class="btn btn-danger btn-sm"
                                onclick="TaskManager.deleteAllSubtasks()">
                                <i class="fas fa-trash-alt"></i> 删除所有子任务
                            </button>
                        </div>
                        <div>
                            <span class="badge bg-secondary" id="subtask-count">0个子任务</span>
                        </div>
                    </div>

                    <!-- 添加子任务表单 -->
                    <div id="add-subtask-form" class="card mb-3" style="display: none;">
                        <div class="card-body">
                            <h6 class="card-title">添加新子任务</h6>
                            <form id="subtaskForm">
                                <input type="hidden" id="current-task-id">
                                <input type="hidden" id="edit-subtask-id">
                                <div class="mb-3">
                                    <label class="form-label">子任务名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="subtask-name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">子任务描述</label>
                                    <textarea class="form-control" id="subtask-description" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">检查项 (每行一项)</label>
                                    <textarea class="form-control" id="subtask-checklist" rows="4"
                                        placeholder="请输入检查项，每行一项"></textarea>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-primary" onclick="TaskManager.saveSubtask()">
                                        <i class="fas fa-save"></i> 保存
                                    </button>
                                    <button type="button" class="btn btn-secondary"
                                        onclick="TaskManager.cancelSubtaskForm()">
                                        取消
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 子任务列表 -->
                    <div id="subtasks-container">
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-tasks fa-3x mb-3"></i>
                            <p>暂无子任务，点击"添加子任务"或"智能分解"来创建</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cron帮助模态框 -->
    <div class="modal fade" id="cronHelpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cron表达式帮助</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>基本格式</h6>
                    <p><code>分 时 日 月 周</code></p>

                    <h6>字段说明</h6>
                    <ul>
                        <li><strong>分钟</strong>: 0-59</li>
                        <li><strong>小时</strong>: 0-23</li>
                        <li><strong>日期</strong>: 1-31</li>
                        <li><strong>月份</strong>: 1-12</li>
                        <li><strong>星期</strong>: 0-7 (0和7都表示周日)</li>
                    </ul>

                    <h6>特殊字符</h6>
                    <ul>
                        <li><code>*</code>: 任意值</li>
                        <li><code>,</code>: 多个值 (如: 1,3,5)</li>
                        <li><code>-</code>: 范围 (如: 1-5)</li>
                        <li><code>/</code>: 步长 (如: */6 表示每6个单位)</li>
                    </ul>

                    <h6>常用示例</h6>
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>表达式</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr><td><code>0 */6 * * *</code></td><td>每6小时执行一次</td></tr>
                            <tr><td><code>0 0 * * *</code></td><td>每天午夜执行</td></tr>
                            <tr><td><code>0 9 * * 1-5</code></td><td>工作日上午9点执行</td></tr>
                            <tr><td><code>*/30 * * * *</code></td><td>每30分钟执行一次</td></tr>
                            <tr><td><code>0 0 1 * *</code></td><td>每月1号午夜执行</td></tr>
                            <tr><td><code>0 0 * * 0</code></td><td>每周日午夜执行</td></tr>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看任务详情模态框 -->
    <div class="modal fade" id="viewTaskModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">任务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>任务描述</h6>
                            <div id="viewTaskDescription" class="mb-4"></div>
                            
                            <h6>测试策略</h6>
                            <div id="viewTaskTestStrategy" class="mb-4"></div>
                            
                            <h6>执行日志</h6>
                            <div id="viewTaskLogs" class="bg-light p-3 rounded">
                                <pre class="mb-0 text-muted" style="white-space: pre-wrap; font-size: 0.875rem;">暂无执行日志</pre>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">任务信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless table-sm mb-0">
                                        <tr>
                                            <td><strong>ID:</strong></td>
                                            <td id="viewTaskId">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>标题:</strong></td>
                                            <td id="viewTaskTitle">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>类型:</strong></td>
                                            <td id="viewTaskType">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>状态:</strong></td>
                                            <td id="viewTaskStatus">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>创建时间:</strong></td>
                                            <td id="viewTaskCreatedAt">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>更新时间:</strong></td>
                                            <td id="viewTaskUpdatedAt">-</td>
                                        </tr>
                                        <tr>
                                            <td><strong>依赖:</strong></td>
                                            <td id="viewTaskDependencies">-</td>
                                        </tr>
                                        <tr id="viewTaskScheduleRow" style="display: none;">
                                            <td><strong>定时任务:</strong></td>
                                            <td id="viewTaskSchedule">-</td>
                                        </tr>
                                        <tr id="viewTaskScheduleStatusRow" style="display: none;">
                                            <td><strong>定时状态:</strong></td>
                                            <td id="viewTaskScheduleStatus">-</td>
                                        </tr>
                                        <tr id="viewTaskNextRunRow" style="display: none;">
                                            <td><strong>下次执行:</strong></td>
                                            <td id="viewTaskNextRun">-</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>
    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/projects.js"></script>
    <script src="/aicode/static/js/tasks.js"></script>
    <script src="/aicode/static/js/task_chat.js"></script>

    <script>
        // 页面初始化
        $(document).ready(function () {
            const projectId = Router.getParam('project_id');
            if (projectId) {
                TaskManager.initTasksPage(projectId);
            } else {
                Utils.showAlert('未指定项目ID', 'error');
            }
        });
    </script>
</body>

</html>