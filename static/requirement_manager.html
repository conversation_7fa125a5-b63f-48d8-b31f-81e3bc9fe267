<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求管理 - 全栈助手</title>

    <!-- Bootstrap CSS -->
    <link href="/aicode/static/external/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/aicode/static/external/all.min.css" rel="stylesheet">
    <!-- Bootstrap JS -->
    <script src="/aicode/static/external/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aicode/static/external/jquery-3.6.0.min.js"></script>
    <!-- markdown-editor -->
    <link href="/aicode/static/external/cherry-markdown.css" rel="stylesheet">
    <script src="/aicode/static/external/cherry-markdown.js"></script>
    <script src="/aicode/static/external/echarts.min.js"></script>
    <script src="/aicode/static/external/cherry-table-echarts-plugin.js"></script>

    <script src="/aicode/static/js/common.js"></script>
    <script src="/aicode/static/js/tasks.js"></script>
    <script src="/aicode/static/js/markdown-ai.js"></script>
    <script src="/aicode/static/js/markdown-doc.js"></script>

    <style>
        html {
            height: 100%;
            overflow: hidden;
        }

        body {
            height: 100%;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        #markdownEditor {
            min-width: 800px;
            max-width: 2600px;
            width: 100%;
            height: calc(100vh - 60px); /* 减去按钮区域的高度 */
            margin: 0 auto;
            overflow: hidden;
        }

        iframe.cherry-dialog-iframe {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <div id="actionButtons" class="me-3">
    </div>

    <!-- 编辑区域 -->
    <div id="markdownEditor" placeholder="请输入需求内容...">
    </div>
    
</body>
<script>
    let apiEndpoint = 'requirement';
    let contentField = 'requirement';
    let docType = 'requirement';
    let cherry = null;

    var aiMenu = Cherry.createMenuHook('AI', {
        noIcon: true, name: 'AI',
        onClick: (selection, type) => {
            switch (type) {
                case 'optimize':
                    optimizeRequirement();
                    return `${selection}`;
                case 'gen_design':
                    generateDesign();
                    return `${selection}`;
                case 'gen_tasks':
                    // 先保存当前的设计文档
                    saveContent();
                    TaskManager.showGenerateTasks(currentProjectId,"requirement");
                    return `${selection}`;
                case 'run_requirement':
                    run_requirement();
                    return `${selection}`;
                default:
                    return selection;
            }
        },
        subMenuConfig: [
            {
                noIcon: true,
                name: '一键优化',
                onclick: (event) => {
                    cherry.toolbar.menus.hooks.aiMenuName.fire(null, 'optimize');
                },
            },
            {
                noIcon: true,
                name: '生成设计',
                onclick: (event) => {
                    cherry.toolbar.menus.hooks.aiMenuName.fire(null, 'gen_design');
                },
            },
            {
                noIcon: true,
                name: '生成任务',
                onclick: (event) => {
                    cherry.toolbar.menus.hooks.aiMenuName.fire(null, 'gen_tasks');
                },
            },
            {
                noIcon: true,
                name: '快速实现',
                onclick: (event) => {
                    cherry.toolbar.menus.hooks.aiMenuName.fire(null, 'run_requirement');
                },
            },
        ],
    });

    // 获取URL参数
    function getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    // 初始化
    $(document).ready(function () {
        currentProjectId = getUrlParameter('project_id');
        if (!currentProjectId) {
            alert('缺少项目ID参数');
            window.close();
            return;
        }

        cherry = createDocCherry('markdownEditor','', aiMenu)
    });

    // 生成设计文档
    function generateDesign() {
        // 先保存当前的需求
        saveContent();
        // 弹出警告：将覆盖原来的设计，是否继续？
        if (!confirm('生成设计将覆盖原来的设计文档，是否继续？')) {
            return;
        }

        // 显示加载提示
        const originalBtnHtml = $('#actionButtons').html();
        var timerId = showAIProgress("设计生成")

        // 调用后端API进行优化            
        fetch(`/aicode/api/projects/${currentProjectId}/gen_design`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enable_kb: enable_kb })
        })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    Utils.showAlert('生成设计成功', 'success');
                } else {
                    Utils.showAlert('生成设计失败: ' + result.message, 'danger');
                }
            })
            .catch(error => {
                Utils.showAlert('生成设计失败', 'danger');
            })
            .finally(() => {
                clearInterval(timerId);
                // 恢复按钮
                $('#actionButtons').html(originalBtnHtml);
            });
    }

    // 优化功能
    function optimizeRequirement() {
        const content = cherry.getValue();
        if (!content.trim()) {
            Utils.showAlert('请输入需要优化的内容', 'warning');
            return;
        }

        // 显示加载提示
        const originalBtnHtml = $('#actionButtons').html();
        var timerId = showAIProgress("AI一键优化")

        // 调用后端API进行优化            
        fetch(`/aicode/api/projects/${currentProjectId}/optimize_requirement`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ requirement: content, enable_kb: enable_kb })
        })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    cherry.setValue(result.message);
                    Utils.showAlert('优化成功', 'success');
                } else {
                    Utils.showAlert('优化失败: ' + result.message, 'danger');
                }
            })
            .catch(error => {
                console.error('优化请求失败:', error);
                Utils.showAlert('优化请求失败', 'danger');
            })
            .finally(() => {
                clearInterval(timerId);
                // 恢复按钮
                $('#actionButtons').html(originalBtnHtml);
            });
    }
    // 直接把需求发送给claude
    function run_requirement() {
        // 先保存当前的需求
        saveContent();

        // 显示加载提示
        const originalBtnHtml = $('#actionButtons').html();
        $('#actionButtons').html('<span class="text-primary"><i class="fas fa-spinner fa-spin"></i> 需求实现中，可以在任务管理查看详情...</span>');

        // 调用后端API进行优化            
        fetch(`/aicode/api/projects/${currentProjectId}/run_requirement`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enable_kb: enable_kb })
        })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    Utils.showAlert('需求实现成功', 'success');
                } else {
                    Utils.showAlert('需求实现失败: ' + result.message, 'danger');
                }
            })
            .catch(error => {
                Utils.showAlert('需求实现失败', 'danger');
            })
            .finally(() => {
                // 恢复按钮
                $('#actionButtons').html(originalBtnHtml);
            });
    }
</script>
</html>