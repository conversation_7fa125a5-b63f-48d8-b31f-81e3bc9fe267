# 交互约束
- 始终用中文回复
# 文件访问约束
- 只能读写/mnt/d/agent/auto-claude-tasks/目录下的文件，请勿尝试访问其他目录。
- 用于调试的文件、总结文档写入/mnt/d/agent/auto-claude-tasks//debug目录
# 工具使用
- 使用Bash的ls命令而不是LS工具来列出目录下的文件
# 项目规则约束
## 系统核心功能
AI全栈助手系统是基于Claude Code Agent的AI辅助开放管理系统，该系统集成了多种LLM服务，支持代码生成、文档优化、任务自动化等AI驱动的开发流程。
核心功能：
- 项目管理（支持Git集成）
- 需求和设计管理  
- 任务生成和执行（AI驱动）
- 知识库管理（基于Milvus向量数据库）
- 文件管理和预览
- Web终端（基于xterm.js）

## 项目结构
- /mnt/d/agent/auto-claude-tasks/static目录：前端页面，其中external 和assets是外部依赖包，不要修改。
- /mnt/d/agent/auto-claude-tasks/src目录：后端python代码
- /mnt/d/agent/auto-claude-tasks/prompts目录：与AI交互用到的提示词模板文件
## 技术栈
- **后端**: Python 3.10+, Flask 2.0+
- **AI集成**: Claude Agent SDK, 多种LLM Provider支持
- **向量数据库**: Milvus Lite
- **代码解析**: Tree-sitter (支持C/C++/Java/Go/Python)
- **前端**: Bootstrap 5, jQuery, xterm.js

## 关键架构特点
1. **模块化设计** - 每个功能模块独立（project_manager, task_manager, knowledge_manager等）
2. **动态配置** - 支持通过环境变量配置多种LLM Provider
3. **AI工具集成** - 集成Claude Agent SDK进行任务自动化
4. **实时功能** - 支持WebSocket通信和实时任务执行监控