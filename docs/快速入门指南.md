# AI全栈助手系统快速入门指南

## 系统简介

AI全栈助手系统是一个集成AI能力的全栈项目管理平台，可帮助开发团队自动化项目开发流程。系统支持代码生成、文档优化、任务自动化等AI驱动的开发功能。

## 系统要求

- Python 3.7+
- 4GB以上内存
- 10GB以上硬盘空间

## 快速安装

### 1. 克隆项目

```bash
git clone <项目地址>
cd auto-claude-tasks
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境

```bash
cp .env.example .env
# 编辑.env文件配置LLM服务
```

### 4. 启动系统

```bash
python run_app.py
```

访问地址：http://localhost:5005

默认账号：admin/123456

## 快速使用流程

### 1. 创建项目

1. 登录系统
2. 点击"项目管理" → "新建项目"
3. 填写项目信息并创建

### 2. 编写需求

1. 在项目列表中点击项目
2. 点击"需求管理"
3. 编写需求内容

### 3. 优化需求（AI功能）

1. 在需求编辑器中点击"AI"按钮
2. 选择"一键优化"
3. 等待系统优化完成

### 4. 生成任务（AI功能）

1. 在需求页面点击"AI"按钮
2. 选择"生成任务"
3. 确认生成参数并执行

### 5. 执行任务

1. 点击"任务管理"
2. 点击"运行任务"
3. 监控执行进度

### 6. 查看结果

1. 点击"文件管理"查看生成文件
2. 点击"LLM日志"查看执行日志

## 核心功能

### AI驱动开发
- 需求文档自动优化
- 系统设计自动生成
- 开发任务智能分解
- 代码自动编写

### 项目管理
- 多种项目类型支持
- Git集成管理
- 多LLM Provider支持

### 全流程监控
- 实时任务执行监控
- 完整日志记录
- 执行结果可视化

## 常见问题

### 启动失败
检查Python版本和依赖安装

### 登录失败
确认用户名密码（默认admin/123456）

### 任务执行失败
查看LLM日志获取错误详情

---
**AI全栈助手系统** - 让AI任务管理更简单、更高效！