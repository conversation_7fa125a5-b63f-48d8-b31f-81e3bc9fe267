# AI全栈助手系统用户使用手册

## 目录

1. [系统概述](#系统概述)
2. [系统要求](#系统要求)
3. [安装与部署](#安装与部署)
4. [系统功能详解](#系统功能详解)
   - [用户认证](#用户认证)
   - [项目管理](#项目管理)
   - [需求管理](#需求管理)
   - [设计管理](#设计管理)
   - [规则管理](#规则管理)
   - [任务管理](#任务管理)
   - [知识库管理](#知识库管理)
   - [文件管理](#文件管理)
   - [终端管理](#终端管理)
   - [日志管理](#日志管理)
5. [使用流程](#使用流程)
6. [常见问题与故障排除](#常见问题与故障排除)
7. [技术支持](#技术支持)

## 系统概述

AI全栈助手系统是一个集成AI能力的全栈项目管理平台，旨在帮助开发团队自动化项目开发流程。系统集成了多种大语言模型(LLM)服务，支持代码生成、文档优化、任务自动化等AI驱动的开发功能。

### 核心功能

1. **项目管理** - 创建和管理项目，支持多种项目类型
2. **需求管理** - 编写和优化需求文档
3. **设计管理** - 自动生成系统设计文档
4. **规则管理** - 定义项目约束和规范
5. **任务管理** - AI自动生成和执行开发任务
6. **知识库管理** - 管理项目文档和代码知识
7. **文件管理** - 浏览和预览项目文件
8. **终端管理** - Web终端集成
9. **日志管理** - 全程执行日志记录

### 系统界面特点

- **现代化设计** - 采用Bootstrap框架，界面美观大方
- **响应式布局** - 支持多种屏幕尺寸，移动端友好
- **直观的导航** - 左侧菜单提供清晰的功能模块导航
- **实时数据展示** - 仪表板实时显示项目状态和统计数据
- **集成PM系统** - 内置SmartPM项目管理系统，提供完整的项目管理功能

## 系统要求

### 硬件要求

- CPU: 2核以上
- 内存: 4GB以上
- 硬盘: 10GB以上可用空间

### 软件要求

- 操作系统: Linux (推荐Ubuntu 22.04)、Windows 10/11、macOS 10.15+
- Python版本: 3.7+
- Docker (可选，用于容器化部署)
- Git (用于代码管理功能)
- Node.js (用于Claude工具集成)

## 安装与部署

### 方法一：直接安装运行

1. 克隆项目代码：
   ```bash
   git clone <项目地址>
   cd auto-claude-tasks
   ```

2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 配置环境变量：
   ```bash
   cp .env.example .env
   # 编辑.env文件，配置LLM Providers、Embedding服务等
   ```

4. 启动系统：
   ```bash
   python run_app.py
   ```

### 方法二：Docker容器化部署

1. 构建镜像：
   ```bash
   docker build -t aicode:latest .
   ```

2. 运行容器：
   ```bash
   docker run -d --name aicode \
     -v /opt/aicode/data:/opt/data \
     -p 5005:5005 -p 5006:5006 \
     --add-host ai.secsign.online:*********** \
     aicode:latest
   ```

### 访问系统

启动后，默认访问地址为：http://localhost:5005

默认用户名：admin
默认密码：123456

## 系统功能详解

### 用户认证

#### 登录系统

1. 打开浏览器访问系统地址
2. 输入用户名和密码（默认：admin/123456）
3. 点击"登录"按钮

#### 修改密码

1. 登录系统后，点击右上角用户头像
2. 选择"用户信息"
3. 输入原密码和新密码
4. 点击"修改密码"

#### 退出登录

点击右上角用户头像，选择"退出登录"

### 项目管理

#### 创建项目

1. 登录系统后，在左侧菜单点击"项目管理"
2. 点击"新建项目"按钮
3. 填写项目信息：
   - 项目名称（必填）
   - 项目类型（产品迭代、BUG修复、PMO、代码分析、其他）
   - 项目描述
   - Git管理（无Git管理、提交代码、提交代码并推送）
   - 工作目录
   - 排除模式和包含模式
   - LLM Provider
4. 点击"创建项目"


#### 项目列表视图

项目列表显示所有已创建的项目，包含项目名称、创建时间、LLM Provider、项目类型、Git管理方式、工作目录等信息。每个项目都有查看、编辑、删除和管理操作按钮。

#### 编辑项目

1. 在项目列表中找到要编辑的项目
2. 点击项目行的"编辑"按钮
3. 修改项目信息
4. 点击"保存"

#### 删除项目

1. 在项目列表中找到要删除的项目
2. 点击项目行的"删除"按钮
3. 确认删除操作

### 需求管理

#### 需求管理界面

点击左侧菜单的"需求管理"后，系统显示功能强大的需求编辑器界面：

![需求管理界面](requirements_management_interface.png)

**界面功能说明：**
- **智能工具栏**：包含完整的文档编辑功能：
  - 文档操作：保存、撤销、恢复
  - AI辅助：一键AI优化、智能内容生成
  - 格式化：文本样式、标题、列表、表格等
  - 高级功能：公式、图表、流程图插入
  - 导入导出：支持知识库导入和文档导出
- **专业编辑器**：支持富文本和Markdown混合编辑
- **实时预览**：右侧显示格式化的需求文档效果
- **目录导航**：自动生成文档大纲，支持快速跳转

#### 需求文档模板示例

系统已预置完整的项目需求文档模板，包含以下内容：
- **项目基础管理**：项目立项、信息管理、AI增强功能
- **PMO需求管理**：需求提交流转、任务工时管理、数据统计分析
- **小批试制管理**：试制流程、报告分析
- **产品发版管理**：发版申请评审、产品发布
- **物料与硬件管理**：物料选型、硬件测试、BOM评审
- **质量问题与工程变更**：质量问题跟进、工程变更流程
- **项目集管理**：项目集创建、监控汇总
- **度量数据与汇报**：项目度量、工作汇报、项目复盘
- **系统通用功能**：权限管理、文档管理、通知集成
- **AI增强功能**：智能分析、自动化流程

#### 编写需求

**需求文档编写要点：**
- 明确项目目标和范围
- 描述核心功能特性
- 定义用户角色和使用场景
- 说明技术要求和约束条件
- 列出验收标准和测试要求

#### 优化需求

1. 在需求管理页面，点击工具栏的"AI"按钮
2. 选择"一键优化"
3. 系统将自动优化需求文档

**AI优化功能：**
- 自动梳理文档结构
- 补充缺失的技术细节
- 优化表达和措辞
- 增加可操作性描述
- 提供标准化格式

#### 生成设计

1. 在需求管理页面，点击工具栏的"AI"按钮
2. 选择"生成设计"
3. 系统将根据需求自动生成设计文档

**设计文档包含：**
- 系统架构设计
- 数据库设计
- 接口设计
- 核心算法设计
- 部署架构设计

#### 生成任务

1. 在需求管理页面，点击工具栏的"AI"按钮
2. 选择"生成任务"
3. 设置任务数量和特殊说明
4. 点击"确认生成"

**任务生成特点：**
- 智能拆分需求为可执行任务
- 自动设置任务优先级
- 建立任务依赖关系
- 估算任务完成时间
- 分配合适的技术栈

### 设计管理

#### 设计管理界面

点击左侧菜单的"设计管理"后，系统显示设计文档编辑界面：

![设计管理界面](design_management_interface.png)

**界面功能说明：**
- **设计工具栏**：提供专业的设计文档编辑功能：
  - 文档格式化：标题样式、段落格式、列表结构
  - 图表插入：支持流程图、架构图、时序图等
  - 代码编辑：支持代码块和语法高亮
  - 协作功能：评论、修订历史、版本控制
- **设计模板**：提供标准化的设计文档模板
- **实时预览**：显示格式化后的设计文档效果

#### 设计文档内容

设计管理模块通常包含：
- **系统架构设计**：整体架构、技术栈选择
- **数据库设计**：数据模型、表结构设计
- **接口设计**：API规范、数据交互格式
- **UI/UX设计**：界面设计、交互流程
- **安全设计**：权限控制、数据安全方案

#### 编写设计

#### 生成任务

1. 在设计管理页面，点击工具栏的"AI"按钮
2. 选择"生成任务"
3. 设置任务数量和特殊说明
4. 点击"确认生成"

### 规则管理

#### 规则管理界面

点击左侧菜单的"规则管理"后，系统显示规则编辑器界面：

![规则管理界面](rules_management_interface.png)

**界面功能说明：**
- **编辑器工具栏**：提供丰富的文本编辑功能，包括：
  - 基础格式化：加粗、斜体、标题、列表等
  - 高级功能：表格、代码块、公式、图表插入
  - AI辅助功能：智能内容生成和优化
  - 预览模式：实时预览编辑效果
- **内容编辑区**：支持Markdown格式的规则文档编写
- **实时预览**：右侧显示格式化后的文档效果

#### 设置规则

### 任务管理

#### 任务管理界面

点击左侧菜单的"任务管理"后，系统显示完整的任务管理界面：

![任务管理界面](task_management_interface.png)

**界面功能说明：**
- **操作按钮区**：包含运行任务、重置任务、停止运行、生成任务、刷新、添加任务等操作
- **Agent状态显示**：显示当前AI代理的运行状态
- **任务列表表格**：详细显示所有任务的信息，包括：
  - 任务编号和标题
  - 任务描述
  - 任务类型
  - 任务状态（已完成/进行中/待处理）
  - 执行时间统计
  - 操作按钮（查看、编辑、删除、运行、详情等）

#### 查看任务

#### 运行任务

1. 在任务管理页面，点击"运行任务"按钮
2. 选择执行模式（并行/顺序）
3. 点击"确认运行"

#### 添加任务

1. 在任务管理页面，点击"添加任务"按钮
2. 填写任务信息：
   - 任务标题（必填）
   - 任务描述
   - 测试策略
   - 依赖任务
   - 保持会话
3. 点击"添加任务"

#### 编辑任务

1. 在任务列表中找到要编辑的任务
2. 点击任务行的"编辑"按钮
3. 修改任务信息
4. 点击"保存"

#### 删除任务

1. 在任务列表中找到要删除的任务
2. 点击任务行的"删除"按钮
3. 确认删除操作

#### 重置任务

1. 在任务管理页面，点击"重置任务"按钮
2. 确认重置操作

#### 停止运行

1. 在任务管理页面，点击"停止运行"按钮
2. 确认停止操作

### 知识库管理

#### 上传文档

1. 在项目列表中点击要管理的项目
2. 点击"知识库管理"
3. 点击"上传文档"按钮
4. 选择要上传的文件（支持PDF、Word、Markdown、文本文件）
5. 点击"上传"

#### 搜索知识

1. 在知识库管理页面的搜索框中输入关键词
2. 点击"搜索"按钮
3. 系统将显示相关知识文档

#### 删除知识库

1. 在知识库管理页面，点击"删除知识库"按钮
2. 确认删除操作

### 文件管理

#### 浏览文件

1. 在项目列表中点击要管理的项目
2. 点击"文件管理"
3. 系统将显示项目文件结构

#### 预览文件

1. 在文件列表中点击要预览的文件
2. 系统将在右侧显示文件内容

#### 下载文件

1. 在文件列表中找到要下载的文件
2. 点击文件行的"下载"按钮

### 终端管理

#### 使用终端

1. 在项目列表中点击要管理的项目
2. 点击"终端管理"
3. 系统将打开Web终端
4. 可以在终端中执行命令

#### AI命令

在终端中输入`/ai`命令可以直接调用AI功能

### 日志管理

#### 查看日志

1. 在项目列表中点击要管理的项目
2. 点击"LLM日志"
3. 系统将显示任务执行日志

#### 搜索日志

1. 在日志页面的搜索框中输入关键词
2. 点击"搜索"按钮

## 使用流程

### PM项目管理系统完整使用流程

**演示项目：PM项目管理系统 - 智能化项目管理系统**

#### 系统访问与登录

1. **访问系统**
   - 打开浏览器访问：http://127.0.0.1:5005/aicode/
   - 输入登录凭据：
     - 用户名：admin
     - 密码：123456

2. **选择项目**
   - 点击右上角下拉框
   - 选择"PM项目管理系统"
   - 系统自动切换到项目管理界面

#### 项目管理功能使用

**1. 任务管理**
- 查看已生成的20个任务，涵盖项目基础管理、PMO需求管理、小批试制管理等10大模块
- 任务状态包括：已完成、进行中等
- 支持任务操作：查看详情、编辑、删除、单独运行、查看子任务等
- Agent状态显示系统运行状态

**2. 规则管理**
- 设置技术栈要求：使用纯HTML技术，前端UI演示用，包含演示数据、可保存
- 配置项目约束和规范
- 支持Markdown格式编辑和实时预览

**3. 需求管理**
- 系统已预置完整的项目需求文档模板
- 包含10大功能模块的详细需求说明
- 支持AI一键优化和智能内容生成
- 实时预览和目录导航功能

**4. 设计管理**
- 编写系统设计文档
- 支持图表插入和代码编辑
- 提供协作功能和版本控制

### 新项目开发流程

**演示项目：PM项目管理系统**

1. **创建项目**
   - 登录系统（admin/123456）
   - 点击左侧菜单"项目管理"
   - 点击"新建项目"按钮
   - 填写项目信息：
     ```
     项目名称：PM项目管理系统
     项目类型：产品迭代
     项目描述：智能化项目管理系统
     LLM Provider：智谱
     工作目录：/mnt/d/aicode/smartpm
     ```
   - 点击"创建项目"

2. **编写需求**
   - 在项目列表中找到刚创建的项目
   - 点击"管理"按钮进入项目详情
   - 点击左侧菜单"需求管理"
   - 编写详细需求文档：
     ```
     # PM项目管理系统需求

     ## 项目目标
     开发一个智能化的项目管理系统，支持项目全生命周期管理。

     ## 核心功能
     - 项目创建和配置
     - 任务分配和跟踪
     - 团队协作和沟通
     - 进度报告和分析
     - 文档管理和共享

     ## 技术要求
     - 前端：HTML + Bootstrap + JavaScript
     - 后端：Python Flask
     - 数据库：SQLite/MySQL
     - 部署：Docker容器化
     ```

3. **AI优化需求**
   - 在需求编辑器中点击工具栏的"AI"按钮
   - 选择"一键优化"
   - 系统自动完善需求文档结构和内容

4. **生成设计**
   - 点击"AI"按钮，选择"生成设计"
   - 系统基于需求生成系统设计文档
   - 包含架构设计、数据库设计、接口设计等

5. **生成任务**
   - 点击"AI"按钮，选择"生成任务"
   - 设置任务数量：10个任务
   - 特殊说明：优先开发核心功能模块
   - 点击"确认生成"

6. **执行任务**
   - 进入"任务管理"页面
   - 查看生成的任务列表和依赖关系
   - 点击"运行任务"按钮
   - 选择执行模式：并行执行
   - 监控任务执行进度和状态

7. **查看结果**
   - 在"文件管理"中查看生成的代码文件
   - 在"知识库管理"中查看技术文档
   - 在"LLM日志"中查看执行过程记录

### BUG修复流程

1. **创建项目**
   - 选择项目类型为"BUG修复"
   - 填写项目信息

2. **描述问题**
   - 在需求管理中详细描述BUG现象和修复要求

3. **生成任务**
   - 根据需求生成修复任务

4. **执行修复**
   - 运行任务完成BUG修复

5. **验证结果**
   - 检查修复结果
   - 查看执行日志

## 常见问题与故障排除

### 登录问题

**问题：无法登录系统**
解决方法：
1. 检查用户名和密码是否正确
2. 确认系统是否正常启动
3. 查看系统日志获取错误信息

### 项目创建问题

**问题：创建项目时提示"工作目录不能为空"**
解决方法：
1. 确保填写了工作目录路径
2. 检查目录权限是否正确

**问题：Git管理项目创建失败**
解决方法：
1. 检查Git地址是否正确
2. 确认访问令牌是否有权限
3. 检查网络连接是否正常

### 任务执行问题

**问题：任务执行失败**
解决方法：
1. 查看任务执行日志
2. 检查LLM服务是否正常
3. 确认项目配置是否正确

### 文件访问问题

**问题：无法访问项目文件**
解决方法：
1. 检查项目工作目录是否存在
2. 确认文件权限设置
3. 查看系统日志获取错误信息

### 知识库问题

**问题：文档上传失败**
解决方法：
1. 检查文件格式是否支持
2. 确认文件大小是否超出限制
3. 检查系统存储空间是否充足

## 技术支持

如遇到无法解决的问题，请：

1. 查看系统日志获取详细错误信息
2. 联系系统管理员
3. 提交Issue到项目仓库

### 系统日志位置

- 任务执行日志：`data/logs/`
- LLM交互日志：`data/logs/{项目名}/`

### 重置系统

如需重置所有数据，可删除`data/`目录：
```bash
rm -rf data/
```

## 最佳实践和使用技巧

### 项目创建建议

1. **项目命名规范**
   - 使用清晰描述性的项目名称
   - 避免使用特殊字符和空格
   - 建议格式：[功能模块]_[项目名称]

2. **工作目录设置**
   - 选择有足够磁盘空间的目录
   - 确保目录有适当的读写权限
   - 建议使用绝对路径

3. **LLM Provider选择**
   - **智谱**：适合中文项目，响应速度快
   - **内网**：适合内部部署，数据安全
   - **其他**：根据具体需求选择

### 需求编写技巧

1. **结构化需求文档**
   ```
   # 项目需求文档

   ## 1. 项目背景
   ## 2. 功能需求
   ## 3. 非功能需求
   ## 4. 技术约束
   ## 5. 验收标准
   ```

2. **功能描述要点**
   - 明确用户角色和权限
   - 详细描述业务流程
   - 定义数据输入输出格式
   - 说明异常处理要求

3. **AI优化建议**
   - 先手动编写核心需求
   - 再使用AI进行优化和补充
   - 检查AI生成的内容准确性
   - 根据实际情况调整

### 任务执行策略

1. **任务拆分原则**
   - 每个任务应该是可独立完成的
   - 任务粒度适中（2-8小时完成）
   - 明确任务依赖关系
   - 设置合理的优先级

2. **执行模式选择**
   - **并行执行**：适合无依赖关系的任务
   - **顺序执行**：适合有明确依赖的任务
   - **混合模式**：根据任务特点灵活选择

3. **进度监控**
   - 定期查看任务执行状态
   - 关注执行日志中的错误信息
   - 及时调整阻塞的任务
   - 保持与团队成员的沟通

### 知识库管理

1. **文档分类**
   - 按项目阶段分类（需求、设计、开发、测试）
   - 按技术领域分类（前端、后端、数据库、部署）
   - 使用统一的命名规范

2. **文档维护**
   - 定期更新过期的文档
   - 建立文档版本控制
   - 添加关键词标签便于搜索

### 故障排除指南

1. **常见问题诊断**
   - 检查系统资源使用情况
   - 验证网络连接状态
   - 查看应用程序日志
   - 确认配置文件正确性

2. **性能优化建议**
   - 定期清理临时文件
   - 监控数据库大小
   - 优化任务并发数量
   - 合理设置缓存策略

### 团队协作

1. **权限管理**
   - 根据角色分配适当权限
   - 定期审查用户权限
   - 建立权限变更流程

2. **工作流程标准化**
   - 制定项目开发规范
   - 建立代码审查机制
   - 设置质量检查点

### 安全注意事项

1. **数据安全**
   - 定期备份重要数据
   - 使用强密码策略
   - 启用访问日志记录
   - 定期更新系统补丁

2. **网络安全**
   - 使用HTTPS协议
   - 配置防火墙规则
   - 限制外部访问权限

---