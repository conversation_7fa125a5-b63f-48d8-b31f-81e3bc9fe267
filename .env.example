# 大模型的配置
# LLM Providers Configuration (Dynamic)
# 格式: PROVIDER_{PROVIDER_NAME}_{CONFIG_KEY}={VALUE}
# PROVIDER_NAME: 提供商名称 (如 LOCAL, ZHIPU, CLAUDE等)
# CONFIG_KEY: 配置项键名 (NAME, BASE_URL, AUTH_TOKEN, MODEL, API_KEY, MAX_TOKENS, FALLBACK)

## 内网claude兼容模型
PROVIDER_LOCAL_API_KEY=
PROVIDER_LOCAL_API_TYPE=claude
PROVIDER_LOCAL_AUTH_TOKEN=sk-NO_KEY
PROVIDER_LOCAL_BASE_URL=https://ai.secsign.online:3006
PROVIDER_LOCAL_FALLBACK=
PROVIDER_LOCAL_MAX_TOKENS=20000
PROVIDER_LOCAL_MODEL=
PROVIDER_LOCAL_NAME=内网
PROVIDER_LOCAL_READ_TOOL=

PROVIDER_LOCAL-LLM_API_KEY=sk-6iMVH7FyhzkNDDoRvqanmwplVmAHIQ6ruYZZExQxbL81TGpR
PROVIDER_LOCAL-LLM_API_TYPE=openai
PROVIDER_LOCAL-LLM_AUTH_TOKEN=
PROVIDER_LOCAL-LLM_BASE_URL=https://ai.secsign.online:3003/v1
PROVIDER_LOCAL-LLM_FALLBACK=
PROVIDER_LOCAL-LLM_MAX_TOKENS=20000
PROVIDER_LOCAL-LLM_MODEL=glm-4.5-air
PROVIDER_LOCAL-LLM_NAME=内网openai

# Embedding配置
EMBEDDING_API_KEY=sk-6iMVH7FyhzkNDDoRvqanmwplVmAHIQ6ruYZZExQxbL81TGpR
EMBEDDING_BASE_URL=https://ai.secsign.online:3003/v1
EMBEDDING_MODEL=embed
EMBEDDING_DIMENSION=1024

# 知识库分块配置
MARKDOWN_SEPARATOR=###
CHUNK_SIZE=100
CHUNK_OVERLAP=10
# 最大文件长度
MAX_CONTENT_LENGTH=8192000
# MinerU上传接口
#MINERU_API=https://10.0.8.180/mineru/upload
MINERU_API=http://10.20.35.250:3003/mineru/upload

# API超时设置 (毫秒) 1500秒
# GML-4.6 1秒10个token，输出15Ktoken需要1500秒
API_TIMEOUT_MS=1500000

# 用户认证配置
AUTH_USERNAME=admin
AUTH_PASSWORD=123456
SESSION_TIMEOUT_HOURS=24

# 工具权限配置
#ALLOWED_TOOLS=
DISALLOWED_TOOLS=WebFetch,WebSearch

# 数据目录
DATA_DIR=/opt/data

# 日志清理配置
# 是否启用日志清理功能 (true/false)
ENABLE_LOG_CLEANUP=true

# 在首页标题显示的信息
INDEX_TITLE=本系统仅用于演示

# 通过网关代理路由本APP的地址
#GATEYWAY_URL=https://127.0.0.1:9443