{"LOG": true, "LOG_LEVEL": "warn", "HOST": "0.0.0.0", "PORT": 8100, "APIKEY": "sk-NO_KEY", "transformers": [{"path": "/root/.claude-code-router/plugins/token-multiplier-transformer.js", "options": {"multiplier": 1.0}}, {"path": "/root/.claude-code-router/plugins/iflow-transformer.js"}], "Providers": [{"name": "local", "api_base_url": "https://ai.secsign.online:3003/v1/chat/completions", "api_key": "sk-6iMVH7FyhzkNDDoRvqanmwplVmAHIQ6ruYZZExQxbL81TGpR", "models": ["qwen_coder", "glm-4.6", "glm-4.5-air"], "transformer": {"use": ["token-multiplier", ["sampling", {"temperature": 0.3}]]}}, {"name": "iflow", "api_base_url": "https://ai.secsign.online:3003/v1/chat/completions", "api_key": "sk-6iMVH7FyhzkNDDoRvqanmwplVmAHIQ6ruYZZExQxbL81TGpR", "models": ["glm-4.5-air"], "transformer": {"use": ["iflow-transformer", ["sampling", {"temperature": 0.3}]]}}], "Router": {"default": "local,glm-4.5-air", "background": "local,glm-4.5-air", "think": "local,glm-4.5-air", "longContext": "local,glm-4.6", "longContextThreshold": 100000, "webSearch": ""}}